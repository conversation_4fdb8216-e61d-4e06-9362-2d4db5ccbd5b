using System.Collections.Generic;
using App.Bag;
using HttpLibrary.Response;
using UnityEngine;

namespace App.UI
{
    public class CommonRewardList : MonoBehaviour
    {

        [SerializeField] private Transform _rewardContent;
        [SerializeField] private CommonItem _rewardPrefab;
        
        
         private void RefreshRewardData(List<CommonResponseDataHandle.ItemModel> items, 
            List<int> newHeroes = null,
            Dictionary<int, int> frames = null, 
            List<int> skins = null,
            List<int> equips = null
            )
        {
            if (items != null)
            {
                foreach (var reward in items)
                {
                    int count = reward.count - reward.oldCount;
                    if (count < 0)
                        continue;
                    if (count == 0 && reward.id < (int) Item.HeroCardStart)
                        continue;
                    var item = Instantiate(_rewardPrefab, _rewardContent);
                    // item.gameObject.SetActive();
                    item.FreshItemUI(reward.id, count);
                    if (newHeroes != null && newHeroes.Contains(reward.id))
                        item.ShowNewLabel();
                }
            }

            if (frames != null)
            {
                foreach (var (key, value) in frames)
                {
                    var item = Instantiate(_rewardPrefab, _rewardContent);
                    item.gameObject.SetActive(false);
                    item.FreshFrameUI(key, 1);
                }
            }

            if (skins != null)
            {
                foreach (var skin in skins)
                {
                    var item = Instantiate(_rewardPrefab, _rewardContent);
                    item.gameObject.SetActive(false);
                    item.FreshSkinUI(skin, 1);
                }
            }

            if (equips != null)
            {
                foreach (var equipId in equips)
                {
                    var item = Instantiate(_rewardPrefab, _rewardContent);
                    item.gameObject.SetActive(false);
                    item.FreshEquipUI(equipId, 1);
                }
            }
        }
         
         
          private List<CommonResponseDataHandle.ItemModel> GetRewards(
            CommonResponseDataHandle.UpdateDataResponseData updateData)
        {
            var rewardList = new List<CommonResponseDataHandle.ItemModel>();
            if (updateData.bag != null && updateData.bag.Count > 0)
            {
                foreach (var reward in updateData.bag)
                {
                    int count = reward.count - reward.oldCount;
                    if (count < 0)
                        continue;
                    if (count == 0 && reward.id < (int) Item.HeroCardStart)
                        continue;
                    rewardList.Add(reward);
                }
            }

            return rewardList;
        }

        public void RefreshRewardData(CommonResponseDataHandle.UpdateDataResponseData updateData)
        {
            if (updateData == null)
                return;
            var frames = new Dictionary<int, int>();
            if (updateData.UserInfo is {frames: { }})
            {
                frames = updateData.UserInfo.frames;
            }

            List<int> skins = null;
            if (updateData.skin != null && updateData.skin.newSkinList != null)
                skins = updateData.skin.newSkinList;
            
            List<int> equips = null;
            if (updateData.lords != null && updateData.lords.newEquips != null)
                equips = updateData.lords.newEquips;

            if (frames.Count == 0 && (updateData?.bag == null ||
                                      updateData.bag.Count == 0) &&
                                      (skins == null || skins.Count == 0) &&
                                      (equips == null || equips.Count == 0))
            {
                return;
            }

            var rewardList = GetRewards(updateData);
            
            var newHeroes = RewardUtility.GetNewHeroItems(updateData);
            if (frames.Count == 0 && rewardList.Count == 0
                                  && (skins == null || skins.Count == 0)
                                  && (equips == null || equips.Count == 0))
            {
                return;
            }
          
            RefreshRewardData(rewardList, newHeroes, frames, skins, equips);
        }

         
         
    }
    
    
}