using System;
using UnityEngine;
using App.Config;
using App.UI.Utility;
using Button = FQDev.UI.Button;
using FQDev.UI.Layer;
using LuaScript;

namespace App.UI.Common
{
    public class SkillCampItem : MonoBehaviour
    {
        [SerializeField] private UIGrayscaleImage _skillBg;
        [SerializeField] private UIGrayscaleImage _skillIcon;
        [SerializeField] private Button _button;

        private int _skillID = 0;
        private void Awake()
        {
            _button.AddClick(OnClickShowDetail);
        }

        public void SetData(int skillID)
        {
            _skillID = skillID;
            var skillConfig = ConfigManager.Instance.GetConfig<SkillCampConfigTable>().GetRowData(skillID);
            _skillIcon.sprite = skillConfig.Icon.Load<Sprite>();
        }

        public void SetLocked(bool isLock)
        {
            _skillBg.Grayscale = isLock;
            _skillIcon.Grayscale = isLock;
        }
        
        private void OnClickShowDetail()
        {
            var go = LayerManager.Instance.LoadContent(LayerTag.Tip, 
                "ui/gameplay/HeroSkillCoreDetailDialog");

            var lua = go.GetComponent<LuaBehaviour>();
            var nameAction = lua.ScriptEnv.Get<Action<int>>("SetCoreSkill");
            nameAction?.Invoke(_skillID);
        }
    }
}
