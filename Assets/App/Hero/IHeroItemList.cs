using System;
using System.Collections.Generic;

namespace App.UI.Hero
{
    public enum HeroFilterType
    {
        Attribute = 1,
        Quality = 2,
        Camp = 3,
    }

    public interface IHeroItemList
    {
        Dictionary<int, int> ResourceHeroes { get; }
        Func<HeroConfig, HeroConfig, int> HeroSorting { set; }
        Dictionary<HeroFilterType, List<int>> HeroFilter { set; }
    }
}