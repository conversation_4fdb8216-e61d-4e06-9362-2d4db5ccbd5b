using App.Config;
using UnityEngine;
using Button = FQDev.UI.Button;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine.UI;
using FQDev.AssetBundles;
using DG.Tweening;
using Language;
using App.UI;
using App.UI.Power;
using FQDev.EventSystem;

namespace App.UI.Hero
{
    public class HomeHeroes : MonoBehaviour
    {
        [SerializeField] private Transform _heroNode;
        [SerializeField] private HeroItem _heroItem;
        [SerializeField] private Vector3[] _HeroPostion;
        [SerializeField] private Button _tabButton;
        [SerializeField] private TextMeshProUGUI _title;
        [SerializeField] private Image _heroBg;
        [SerializeField] private GameObject _guardianNode;
        [SerializeField] private GameObject _guardianEmptyNode;
        [SerializeField] private GameObject _guardianSlotNode;
        [SerializeField] private GameObject _maskGo;
        [SerializeField] private TextMeshProUGUI _power;

        private EventSubscriber _subscriber;
        private bool _onNormalFormation = true;
        private List<HeroItem> _heroItemList = new List<HeroItem>();
        private Sequence _sequence;
        private bool _hasInit = false;

        private void Awake()
        {
            InitItemList();
            _subscriber = new EventSubscriber();
            _subscriber.Subscribe<TotalBattlePowerChanged>(OnPowerChanged);
        }

        void Start()
        {
            _maskGo.SetActive(false);
            ShowHeroes(true);
            UpdateGuardianUnlock();
        }

        void OnEnable()
        {
            
        }

        private void OnPowerChanged(TotalBattlePowerChanged evt)
        {
            ShowHeroes(true);
        }

        private void InitItemList()
        {
            if (_hasInit)
                return;
            DB.Hero.FixMainChapterDemonKingHero();
            _hasInit = true;
            _tabButton.AddClick(OnClickTabFormation);
            for (var i = 0; i < DB.Hero.Appear.Length; ++i)
            {
                var heroItem = Instantiate(_heroItem, _heroNode);
                if (i < _HeroPostion.Length)
                    heroItem.transform.localPosition = _HeroPostion[i];
                _heroItemList.Add(heroItem);
            }
        }

        private void UpdateGuardianUnlock()
        {
            var constConfig = ConfigManager.Instance.GetConfig<ConstConfig>();
            _tabButton.gameObject.SetActive(DB.User.SafeMaxChapter > constConfig.SupportSkillsUnlockChapter);
        }

        public void SetButtonState(bool state)
        {
            _tabButton.gameObject.SetActive(state);
        }

        public void RefreshButtonState()
        {
            UpdateGuardianUnlock();
        }

        public void SetFormationType(bool normal)
        {
            _onNormalFormation = normal;
            ShowHeroes();
        }

        private void OnClickTabFormation()
        {
            _onNormalFormation = !_onNormalFormation;
            ShowHeroes();
        }

        private void ShowDisappearAnim()
        {
            if (_sequence != null)
                _sequence.Kill();

            _maskGo.SetActive(true);
            _sequence = DOTween.Sequence();
            _sequence.AppendInterval(0.1f);
            for (int i = 0; i < _heroItemList.Count; ++i)
            {
                if (_heroItemList[i].gameObject.activeSelf)
                {
                    _sequence.Join(_heroItemList[i].transform.DORotate(new Vector3(0, -90, 0), 0.1f));
                }
            }
            _sequence.AppendInterval(0.1f);
        }

        private void ShowMainHeroList(bool init = false)
        {
            InitItemList();
            _heroBg.sprite = AssetBundleManager.Instance.LoadAsset<Sprite>("ui/hero", "HomeMainLineupBox");
            var heroConfigTable = ConfigManager.Instance.GetConfig<HeroConfigTable>();
            _title.SetLang("main_lineup_title");
            if (init == false)
            {
                ShowDisappearAnim();
            }
            
            for (var i = 0; i < DB.Hero.Appear.Length; i++)
            {
                var heroId = DB.Hero.Appear[i];
                if (heroId == 0)
                {
                    if (init)
                        _heroItemList[i].gameObject.SetActive(false);
                    continue;
                }
                var heroConfig = heroConfigTable.GetRowData(heroId);
                if (heroConfig == null)
                    continue;

                var heroItem = _heroItemList[i];
                if (init == false && heroItem.gameObject.activeSelf == false)
                    heroItem.transform.localRotation = Quaternion.Euler(0, -90, 0);
                else
                    heroItem.transform.localRotation = Quaternion.Euler(0, 0, 0);

                if (DB.Hero.Contents.TryGetValue(heroId, out var level) && level >= 1){
                    heroItem.gameObject.SetActive(true);
                    heroItem.FreshUI(heroId, level, heroConfig, () => DB.Hero.Appear.Contains(heroId),
                        () => DB.Hero.IsAppearHeroFull());
                    heroItem.SetButtonAction(0);
                }
                else
                {
                    heroItem.gameObject.SetActive(true);
                    heroItem.FreshUI(heroId, 0, heroConfig, () => DB.Hero.Appear.Contains(heroId),
                        () => DB.Hero.IsAppearHeroFull());
                    heroItem.SetButtonAction(3);
                }

                if (init == false)
                {
                    _sequence.Join(heroItem.transform.DORotate(new Vector3(0, 0, 0), 0.1f));
                }
            }

            if (init == false)
            {
                _sequence.OnComplete(()=>
                {
                    for (var i = 0; i < DB.Hero.Appear.Length; i++)
                    {
                        var heroId = DB.Hero.Appear[i];
                        if (heroId == 0)
                        {
                            _heroItemList[i].gameObject.SetActive(false);
                        }
                    }
                    _maskGo.SetActive(false);
                });
            }
        }

        private void ShowGuardianHeroList(bool init = false)
        {
            _heroBg.sprite = AssetBundleManager.Instance.LoadAsset<Sprite>("ui/hero", "HomeSupportLineupBox");
            var heroConfigTable = ConfigManager.Instance.GetConfig<HeroConfigTable>();
            _title.SetLang("support_lineup_title");
            if (init == false)
            {
                ShowDisappearAnim();
            }
            int count = 0;
            for (var i = 0; i < DB.Hero.Appear.Length; i++)
            {
                var heroId = DB.Hero.GetGuardianHero(i);
                if (heroId == 0)
                {
                    if (init)
                        _heroItemList[i].gameObject.SetActive(false);
                    continue;
                }
                var heroConfig = heroConfigTable.GetRowData(heroId);
                if (heroConfig == null)
                    continue;

                ++count;
                var heroItem = _heroItemList[i];
                if (init == false && heroItem.gameObject.activeSelf == false)
                    heroItem.transform.localRotation = Quaternion.Euler(0, -90, 0);
                else
                    heroItem.transform.localRotation = Quaternion.Euler(0, 0, 0);

                if (DB.Hero.Contents.TryGetValue(heroId, out var level) && level >= 1){
                    heroItem.gameObject.SetActive(true);
                    heroItem.FreshUI(heroId, level, heroConfig, () => DB.Hero.GuardianHero.Contains(heroItem.HeroId),
                            () => true);
                    heroItem.SetButtonAction(0);
                }
                else
                {
                    heroItem.gameObject.SetActive(true);
                    heroItem.FreshUI(heroId, 0, heroConfig, () => DB.Hero.GuardianHero.Contains(heroItem.HeroId),
                            () => true);
                    heroItem.SetButtonAction(3);
                }

                if (init == false)
                {
                    _sequence.Join(heroItem.transform.DORotate(new Vector3(0, 0, 0), 0.1f));
                }
            }

            if (init == false)
            {
                _sequence.OnComplete(()=>
                {
                    for (var i = 0; i < DB.Hero.Appear.Length; i++)
                    {
                        var heroId = DB.Hero.GetGuardianHero(i);
                        if (heroId == 0)
                        {
                            _heroItemList[i].gameObject.SetActive(false);
                        }
                    }
                    _maskGo.SetActive(false);
                });
            }
            _guardianSlotNode.SetActive(count > 0);
            _guardianEmptyNode.SetActive(count <= 0);
        }

        private void LoadBattlePower()
        {
            int power = 0;
            for (var i = 0; i < DB.Hero.Appear.Length; i++)
            {
                var heroId = DB.Hero.Appear[i];
                power += BattlePowerManager.Instance.GetHeroBattlePower(heroId);
            }

            for (var i = 0; i < DB.Hero.GuardianHero.Length; i++)
            {
                var heroId = DB.Hero.GetGuardianHero(i);
                power += BattlePowerManager.Instance.GetGuardianHeroBattlePower(heroId);
            }

            _power.text = StringUtils.FormatCurrency(power);
        }

        public void ShowHeroes(bool init = false)
        {
            _guardianNode.SetActive(!_onNormalFormation);
            if (_onNormalFormation)
                ShowMainHeroList(init);
            else
                ShowGuardianHeroList(init);

            LoadBattlePower();
        }

        private void OnDestroy()
        {
            if (_sequence != null)
                _sequence.Kill();

            _subscriber.Dispose();
        }
    }
}