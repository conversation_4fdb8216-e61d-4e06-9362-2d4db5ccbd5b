using System;
using System.Collections.Generic;
using System.Linq;
using App.Bag;
using App.Config;
using App.GamePlay.UI;
using App.Gifts;
using App.Hero;
using App.UI.Common;
using App.UI.Utility;
using App.UI.Power;
using FQDev.EventSystem;
using FQDev.UI.Layer;
using Language;
using LuaScript;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Button = FQDev.UI.Button;

namespace App.UI.Hero
{
    public class HeroDetailLayer : PopupLayerContent, IEventSender
    {
        [SerializeField] private Button _closeBtn;
        [SerializeField] private HeroItemBase _heroItem;
        [SerializeField] private TextMeshProUGUI _textLevel;
        [SerializeField] private TextMeshProUGUI _textName;
        [SerializeField] private TextMeshProUGUI _textDescription;
        [SerializeField] private TextMeshProUGUI _textGoldCost;
        [SerializeField] private Button _animatedButtonUpGrade;
      //  [SerializeField] private Button _animatedButtonCompose;
        [SerializeField] private UIGrayscaleImage _upgradeImageGrayscale;

        [SerializeField] private Transform _skillNode;
        [SerializeField] private SkillItem _skillItem;
        [SerializeField] private SkillCoreItem _skillCoreItem;
        [SerializeField] private SkillCampItem _skillCampItem;
        [SerializeField] private Transform _detailNode;
        [SerializeField] private HeroAttributeItem _heroAttributeItem;
        [SerializeField] private HeroAttributeItem[] _normalAttributeItems;

        [SerializeField] private Button _btnLink;
        [SerializeField] private TextMeshProUGUI _txtLink;
        [SerializeField] private TextMeshProUGUI _txtDescreption;
        
        [SerializeField] private Button _btnHeroCompose;
        
        [SerializeField] private Image _imageAttribute;
        [SerializeField] private TextMeshProUGUI _textAttribute;
        
        [SerializeField] private LuaBehaviour _luaHeroTalent;
        [SerializeField] private LuaBehaviour _luaChoose;
        [SerializeField] private LuaBehaviour _luaHeroEquip;
        [SerializeField] private GameObject _extraCostNode;
        [SerializeField] private Image _extraCostIcon;
        [SerializeField] private TextMeshProUGUI _extraCostNum;
        [SerializeField] private Button _btnExtraCostTip;
        [SerializeField] private Button _btnSkin;
        // [SerializeField] private Button _btnExchange;
        [SerializeField] private TextMeshProUGUI _power;
        [SerializeField] private Button _btnTransfer;
        [SerializeField] private RectTransform _btnNode;
        
        private int _heroId;
        private int _level;
        private HeroConfig _heroConfig;
        private HeroLevelConfig _heroLevelConfig;

        private readonly HttpRequest _httpRequest = new HttpRequest();
        private readonly List<SkillItem> _skillItemList = new();
        private readonly List<SkillCoreItem> _skillCoreItemList = new();
        private readonly List<SkillCampItem> _skillCampItemList = new();
        private SkillItem _currentSkillItem;

        private HeroAttributeItem _durationAttributeItem;
        private HeroAttributeItem _specialAttributeItem;
        
        private bool _hasLevelup;
        private float _upgradeButtonPosY;
        
        public int HeroId => _heroId;
        public int Level => _level;

        private EventSubscriber _subscriber;
        
        private void Awake()
        {
            _closeBtn.AddClick(() =>
            {
                AudioManager.Instance.PlaySound("Main_Click");
                Close();
                if (_hasLevelup)
                    GiftManager.Instance.CheckHeroesTriggerGift(_heroId, GetGiftTriggerType());
            });
            _btnLink.AddClick(() =>
            {
                ItemLinkUtility.JumpLink(_heroId, Close);
            });
            
            _btnHeroCompose.AddClick(OnClickCompose);
            _animatedButtonUpGrade.AddClick(ClickUpgrade);
            
            _upgradeButtonPosY = _btnNode.localPosition.y;

            _subscriber = new EventSubscriber();
            _subscriber.Subscribe<ItemUpdatedEvent>(OnGoldChanged);
            _subscriber.Subscribe<HeroSkinUpdate>(OnSkinUpdate);
            _subscriber.Subscribe<TotalBattlePowerChanged>(OnPowerChanged);
            _btnExtraCostTip.AddClick(ShowExtraCostTip);
            _btnSkin.AddClick(OnClickShowSkinDetail);
            _btnTransfer.AddClick(OnClickTransfer);
            //_btnExchange.AddClick(OnClickShowFragmentExchange);
        }

        private void OnDestroy()
        {
            _subscriber.Dispose();
        }

        private void OnGoldChanged(ItemUpdatedEvent updatedEvent)
        {
            if(_heroLevelConfig == null || !_heroLevelConfig.Costs.ContainsKey(updatedEvent.Id))
                return;

            SetData(_heroId, _level);
        }

        private void OnSkinUpdate(HeroSkinUpdate evt)
        {
            SetData(_heroId, _level);
        }

        public void SetData(int id)
        {
            var level = HeroManager.Instance.GetHeroLevel(id);
            SetData(id, level);
        }

        private void LoadBattlePower()
        {
            _power.text = StringUtils.FormatCurrency(BattlePowerManager.Instance.GetHeroBattlePower(_heroId));
        }

        private void OnPowerChanged(TotalBattlePowerChanged evt)
        {
            LoadBattlePower();
        }

        private void OnClickShowSkinDetail()
        {
            var content = LayerManager.Instance.LoadContent(
                    LayerTag.Tip,
                    "ui/hero/HeroSkinDetailDialog");
            var luaScript = content.GetComponent<LuaBehaviour>();
            luaScript.CallFunctionInt("Initialized", _heroId);

            LogEvent.I.Track("Hero_Skin_Open")
                .IsNew().AddMaxChapter()
                .Add("Hero_ID", _heroId)
                .Send();
        }

        // private void OnClickShowFragmentExchange()
        // {
        //     var config = ConfigManager.Instance.GetConfig<FragmentExchangeConst>();
        //     switch(_heroConfig.Quality)
        //     {
        //         case 3:
        //         {
        //             if (!BagManager.Instance.CheckItemEnough(config.UniversalCVRPurpleItem.Id, config.UniversalCVRPurpleItem.Count))
        //             {
        //                 MessageTip.Create("Not_Enough_Universal_Card1");
        //                 return;
        //             }
        //         };break;
        //         case 4:
        //         {
        //             if (!BagManager.Instance.CheckItemEnough(config.UniversalCVROrangeItem.Id, config.UniversalCVROrangeItem.Count))
        //             {
        //                 MessageTip.Create("Not_Enough_Universal_Card2");
        //                 return;
        //             }
        //         };break;
        //     }
        //
        //     var tipContent = LayerManager.Instance.LoadContent(
        //             LayerTag.Popup,
        //             "ui/fragmentexchange/FragmentExchangeDialog");
        //
        //     var tipScript = tipContent.GetComponent<LuaBehaviour>();
        //     var action = tipScript.Get<Action<int>>("Initialize");
        //     action?.Invoke(_heroId);
        // }

        private void ShowExtraCostTip()
        {
            var costId = _heroLevelConfig.Costs.Keys.ToList().FirstOrDefault(k => k != (int)Item.Money && k != _heroId);
            if (costId > 0)
            {
                var extraConfig = ConfigManager.Instance.GetConfig<ItemConfigTable>().GetRowData(costId);
                var tipContent = LayerManager.Instance.LoadContent(
                    LayerTag.Tip,
                    "ui/message/CommonArrowTipsLayer2");
                var tipScript = tipContent.GetComponent<LuaBehaviour>();
                var action = tipScript.Get<Action<string>>("SetName");
                action?.Invoke(LanguageManager.Instance.GetLanguage(extraConfig.Name, extraConfig.Name));
                var action1 = tipScript.Get<Action<string>>("SetContent");
                action1?.Invoke(LanguageManager.Instance.GetLanguage(extraConfig.Description, extraConfig.Description));
                var action2 = tipScript.Get<Action<Vector3>>("SetPosition");
                action2?.Invoke(_extraCostIcon.transform.position);
            }
        }

        private void LoadCoreSkill()
        {
            if (_heroConfig == null || _heroConfig.CoreSkills.Count <= 0)
            {
                foreach (var item in _skillCoreItemList)
                {
                    item.gameObject.SetActive(false);
                }
                return;
            }

            for (var i = 0; i < _heroConfig.CoreSkills.Count; i++)
            {
                if (i < _skillCoreItemList.Count)
                {
                    _skillCoreItemList[i].SetData(_heroConfig.CoreSkills[i]);
                    _skillCoreItemList[i].SetLocked(_level <= 0);
                }
                else
                {
                    var item = Instantiate(_skillCoreItem, _skillNode);
                    item.SetData(_heroConfig.CoreSkills[i]);
                    item.SetLocked(_level <= 0);
                    _skillCoreItemList.Add(item);
                }
            }

            for (var i = 0; i < _skillCoreItemList.Count; i++)
            {
                _skillCoreItemList[i].gameObject.SetActive(i < _heroConfig.CoreSkills.Count);
            }
        }
        
        private void LoadCampSkill()
        {
            if (_heroConfig == null || _heroConfig.CampSkill.Count <= 0)
            {
                foreach (var item in _skillCampItemList)
                {
                    item.gameObject.SetActive(false);
                }
                return;
            }

            for (var i = 0; i < _heroConfig.CampSkill.Count; i++)
            {
                if (i < _skillCampItemList.Count)
                {
                    _skillCampItemList[i].SetData(_heroConfig.CampSkill[i]);
                    _skillCampItemList[i].SetLocked(_level <= 0);
                }
                else
                {
                    var item = Instantiate(_skillCampItem, _skillNode);
                    item.SetData(_heroConfig.CampSkill[i]);
                    item.SetLocked(_level <= 0);
                    _skillCampItemList.Add(item);
                }
            }

            for (var i = 0; i < _skillCampItemList.Count; i++)
            {
                _skillCampItemList[i].gameObject.SetActive(i < _heroConfig.CampSkill.Count);
            }
        }

        public void SetData(int id, int level)
        {
           // _addHeight = 0;
            _heroId = id;
            _level = level;
            var heroMaxLevel = ConfigManager.Instance.HeroLevelRef.GetHeroMaxLevel(_heroId);
            _heroConfig = ConfigManager.Instance.GetConfig<HeroConfigTable>().GetRowData(id);
            _heroItem.RefreshUI(_heroId, _level);
            if (_level >= 1)
            {
                // _textLevel.transform.parent.gameObject.SetActive(true);
                _textLevel.text = _level.ToString();
                _txtLink.gameObject.SetActive(false);
                _txtDescreption.gameObject.SetActive(false);
                _animatedButtonUpGrade.gameObject.SetActive(true);
                _btnHeroCompose.gameObject.SetActive(false);
            }
            else
            {
                if (!HeroManager.Instance.IsHeroLocked(_heroConfig))
                {
                    if (HeroManager.Instance.ReachComposeItemNumber(_heroConfig))
                    {
                        _txtLink.gameObject.SetActive(false);
                        _txtDescreption.gameObject.SetActive(false);
                        _btnHeroCompose.gameObject.SetActive(true);
                    }
                    else
                    {
                        _txtLink.gameObject.SetActive(true);
                        _txtDescreption.gameObject.SetActive(false);
                        _btnHeroCompose.gameObject.SetActive(false);
                        var itemConfig = BagManager.Instance.GetItemInfo(_heroConfig.LevelUpItemId);
                        _txtLink.SetLang(itemConfig.GetSource, itemConfig.GetSource);
                    }
                }
                else
                {
                    _txtLink.gameObject.SetActive(false);
                    _txtDescreption.gameObject.SetActive(true);
                    _btnHeroCompose.gameObject.SetActive(false);
                    
                    if (_heroConfig.UnlockConditions.TryGetValue((int)HeroUnlockCondition.PassChapter, out var unlockStage))
                        _txtDescreption.SetLang("Chapter_Unlock_1", unlockStage);
                    else if (_heroConfig.UnlockConditions.TryGetValue((int)HeroUnlockCondition.Event, out var eventValue))
                        _txtDescreption.SetLang("get_source_acticity");
                }
            }

            _textName.SetLang(_heroConfig.Name);
            _textDescription.SetLang($"{_heroConfig.Name}Description");

            LoadCoreSkill();
            LoadCampSkill();
            var skillIndex = 0;
            foreach (var skill in _heroConfig.Skills)
            {
                var skillItem = GetSkillItem(skillIndex);
                skillItem.FreshUI(skill.Value);
                skillItem.SetLocked(skill.Key > _level);
                skillItem.SetLockLevel(skill.Key > _level, skill.Key);
                skillItem.SetShowDetailListener(ClickShowSkillItemDetail);
                ++skillIndex;
            }

            if (_level > heroMaxLevel || _level <= 0)
                _heroLevelConfig = null;
            else
                _heroLevelConfig = ConfigManager.Instance.HeroLevelRef[_heroId, _level];

            //  if (_heroLevelConfig != null)
            //      _heroLevelConfig.Costs.TryGetValue(_heroConfig.LevelUpItemId, out _);
            // int levelUpNeedCount = Mathf.Max(0, cardCostCount - BagManager.Instance.GetItemCount(_heroConfig.LevelUpItemId));
            // _btnExchange.gameObject.SetActive((_heroConfig.Quality == 3 || _heroConfig.Quality == 4) && _level > 0 && _level < heroMaxLevel && levelUpNeedCount > 0);

            _btnTransfer.gameObject.SetActive(ConfigManager.Instance.HeroDemonKingRef.ExistTransferHero(_heroId));
            
            if (_level < heroMaxLevel && _heroLevelConfig != null && _heroLevelConfig.Costs.Count > 0
                && DB.Hero.Contents.TryGetValue(_heroId, out var lv) && lv > 0)
            {
                _extraCostNode.SetActive(false);
                var isCostEnough = _heroLevelConfig.Costs.All(kvp => BagManager.Instance.GetItemCount(kvp.Key) >= kvp.Value);
                _heroLevelConfig.Costs.TryGetValue((int) Item.Money, out var goldCount);
                _textGoldCost.text = goldCount.ToString();
                _upgradeImageGrayscale.Grayscale = !isCostEnough;
            }
            else
            {
                _animatedButtonUpGrade.gameObject.SetActive(false);
            }

            //Attributes
            if (_heroLevelConfig != null)
            {
                SetAttribute(_level, heroMaxLevel, _heroLevelConfig);
            }
            else
            {
                SetAttribute(1, heroMaxLevel, ConfigManager.Instance.HeroLevelRef[_heroId, 1]);
            }
            LoadBattlePower();
            var attributeConfig = ConfigManager.Instance.GetConfig<AttackAttributeConfigTable>()
                .GetRowData(_heroConfig.AttackAttribute);
            _imageAttribute.sprite = attributeConfig.Res?.Load<Sprite>();
            _textAttribute.SetLang(attributeConfig.Name);
            
            _luaHeroTalent.CallFunctionInt("Initialized",_heroId);
            _luaChoose.CallFunctionInt("Initialized",_heroId);
            _luaHeroEquip.CallFunctionInt("Initialized",_heroId);
        }

        private void OnClickCompose()
        {
            _httpRequest.HeroCompose(_heroId, () =>
            {
                var content = LayerManager.Instance.LoadContent((int) LayerTag.Popup, "ui/hero/GetHeroDialog");
                var randomCardDetail = content.GetComponent<GetHeroDialog>();
                randomCardDetail.Initialize(_heroConfig);
                //Close();
                
                var curLevel = HeroManager.Instance.GetHeroLevel(_heroId);
                this.DispatchEvent(Witness<HeroUpgradeEvent>._, _heroId, curLevel, _heroConfig);
                SetData(_heroId, curLevel);
            });
        }

        private SkillItem GetSkillItem(int index)
        {
            while (index >= _skillItemList.Count)
            {
                var skillItem = Instantiate(_skillItem, _skillNode);
                _skillItemList.Add(skillItem);
            }

            return _skillItemList[index];
        }

        private void SetAttribute(int currentLevel, int heroMaxLevel, HeroLevelConfig heroLevelConfig)
        {
            float add = 0;
            HeroLevelConfig nextLevelConfig = null;
            if (currentLevel < heroMaxLevel)
            {
                nextLevelConfig = ConfigManager.Instance.HeroLevelRef[_heroId, currentLevel + 1];
            }

            for (var i = 0; i < _normalAttributeItems.Length; i++)
            {
                if (i == 0)
                {
                    _normalAttributeItems[i].SetTextValueByKey(_heroConfig.AttackDescription);
                }
                else if (i == 1)
                {
                    if (nextLevelConfig != null)
                        add = nextLevelConfig.AttackDamage - heroLevelConfig.AttackDamage;

                    _normalAttributeItems[i].SetTextValue(heroLevelConfig.AttackDamage, add);
                }
                else if(i == 2)
                {
                    if (nextLevelConfig != null)
                        add = nextLevelConfig.AttackIntervalShow - heroLevelConfig.AttackIntervalShow;

                    _normalAttributeItems[i].SetTextValue(heroLevelConfig.AttackIntervalShow, add);
                }
                else
                {
                    if (nextLevelConfig != null)
                        add = nextLevelConfig.Hp - heroLevelConfig.Hp;
                    
                    _normalAttributeItems[i].SetTextValue(heroLevelConfig.Hp, add);
                }
            }

            var extraAttrCount = 0;
            if (heroLevelConfig.EffectDuration > 0)
            {
                extraAttrCount++;
                if (_durationAttributeItem == null)
                    _durationAttributeItem = Instantiate(_heroAttributeItem, _detailNode);
                if (nextLevelConfig != null)
                    add = nextLevelConfig.EffectDuration - heroLevelConfig.EffectDuration;

                _durationAttributeItem.SetTextTitleByKey(_heroConfig.EffectDurationDescription);
                _durationAttributeItem.SetTextValue(heroLevelConfig.EffectDuration, add);
                if (_heroConfig.EffectDurationIcon != null)
                    _durationAttributeItem.SetImage(_heroConfig.EffectDurationIcon.Load<Sprite>());
            }

            if (heroLevelConfig.ExtraAttributeValue > 0)
            {
                extraAttrCount++;
                if (_specialAttributeItem == null)
                    _specialAttributeItem = Instantiate(_heroAttributeItem, _detailNode);
                if (nextLevelConfig != null)
                    add = nextLevelConfig.ExtraAttributeValue - heroLevelConfig.ExtraAttributeValue;

                _specialAttributeItem.SetTextTitleByKey(_heroConfig.ExtraAttributeDescription);
                _specialAttributeItem.SetTextValue(heroLevelConfig.ExtraAttributeValue, add);
                if (_heroConfig.ExtraAttributeIcon != null)
                    _specialAttributeItem.SetImage(_heroConfig.ExtraAttributeIcon.Load<Sprite>());
            }

            if (extraAttrCount >= 1)
            {
                var newPos = new Vector3(0, _upgradeButtonPosY - 100, 0);
                if (_heroConfig.Quality > 2)
                    newPos += new Vector3(0, -50, 0);
                _btnNode.localPosition = newPos;
                _txtDescreption.transform.localPosition = newPos;
            }
        }

        private void ClickUpgrade()
        {
            AudioManager.Instance.PlaySound("Main_Click");

            int itemId = -1;
            foreach (var levelCost in _heroLevelConfig.Costs)
            {
                var count = BagManager.Instance.GetItemCount(levelCost.Key);
                if (count >= levelCost.Value) continue;

                switch(levelCost.Key)
                {
                    case (int) Item.Money:
                    case (int) Item.Diamond:
                    {
                        itemId = levelCost.Key;
                    };break;
                    default:
                    {
                        if (levelCost.Key == _heroId)
                            itemId = 2;
                        else
                            itemId = 3;
                    };break;
                }
                break;
            }

            switch (itemId)
            {
                case 3:
                    MessageTip.Create("MessageLackLayer_title");                
                    return;
                case 2:
                    MessageTip.Create("CardIsNotEnough");
                    return;
                case 1:
                case 0:
                    if (itemId == 0)
                    {
                        if (!DB.User.TripleGift.TryGetValue(11, out long endTime))
                        {
                            this.DispatchEvent(Witness<HeroLevelUpLackEvent>._, 11);
                        }
                        else
                        {
                            if(!GiftManager.Instance.CheckGetItemTriggerGift(itemId))
                                MessageLackLayer.Create(itemId, Close);
                        }
                    }
                    else
                    {
                        if(!GiftManager.Instance.CheckGetItemTriggerGift(itemId))
                            MessageLackLayer.Create(itemId, Close);             
                    }
                    return;
            }

            _httpRequest.HeroUpgrade(_heroId, () =>
            {
                _hasLevelup = true;
                AudioManager.Instance.PlaySound("Hero_LevelUp");

                var heroLevelUpLayer = HeroLevelUpLayer.Create();
                heroLevelUpLayer.SetData(_heroId, _level, _heroConfig);

                // Close();
                var curLevel = HeroManager.Instance.GetHeroLevel(_heroId);
                this.DispatchEvent(Witness<HeroUpgradeEvent>._, _heroId, curLevel, _heroConfig);
                SetData(_heroId, curLevel);
            });
        }
        
        private void ClickShowSkillItemDetail(SkillItem skillItem)
        {
            if (_currentSkillItem == skillItem)
                return;

            CloseSkillItemDetail();
            _currentSkillItem = skillItem;
        }

        public void CloseSkillItemDetail()
        {
            if (_currentSkillItem != null)
            {
                _currentSkillItem.CloseSkillItemDetail();
                _currentSkillItem = null;
            }
        }

        public void OnClickTransfer()
        {
            var go = LayerManager.Instance.LoadContent(LayerTag.Dialog, 
                "ui/herotransfer/HeroTransferDialog");
            var lua = go.GetComponent<LuaBehaviour>();
            lua.CallFunctionInt("Initialize", _heroId);
            Close();
        }

        private void OnDisable()
        {
            CloseSkillItemDetail();
        }

        private GiftTriggerType GetGiftTriggerType()
        {
            if (_heroConfig.Quality > 2)
                return GiftTriggerType.SeniorHero;
            
            return GiftTriggerType.NormalHero;
        }
        
        public static HeroDetailLayer Create()
        {
            return (LayerManager.Instance.LoadContent(LayerTag.Dialog, "ui/hero/HeroDetailLayer") as HeroDetailLayer);
        }

        public EventDispatcher Dispatcher => EventDispatcher.Global;
        
    }

    public class HeroUpgradeEvent : EventBase<int, int, HeroConfig>
    {
        public int HeroId => Field1;
        public int Level => Field2;
        public HeroConfig HeroConfig => Field3;
    }
    public class HeroUnlockEvent : EventBase<int>
    {
        public int HeroId => Field1;
        
    }

    public class HeroLevelUpLackEvent : EventBase<int>
    {      
        public int GroupType => Field1;
    }
}