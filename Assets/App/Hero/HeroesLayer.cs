using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using App.Config;
using App.Hero;
using FQDev.EventSystem;
using FQDev.UI.Layer;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using App.Bag;
using Button = FQDev.UI.Button;
using GameModule.Tutorials;
using App.UI.Home;
using DG.Tweening;
using Language;
using App.DailyBlessing;
using App.UI.Common;
using LuaScript;

namespace App.UI.Hero
{
    public enum HeroItemChooseType
    {
        NormalChoose,
        Remove,
        Use,
        SwapUse,
        SwapRemove,
        Unlock,
        ShowDetail,
    }

    public class HeroesLayer : Layer<PERSON>ontent, IHeroItemList, IEventSender, INavigatorController
    {
        public EventDispatcher Dispatcher => EventDispatcher.Global;
        // [SerializeField] private Button _closeBtn;
        [SerializeField] private Transform _battleHeroNode;
        [SerializeField] private Transform _haveHeroNode;
        [SerializeField] private Transform _lockHeroNode;
        [SerializeField] private HeroItem _heroItem;
        [SerializeField] private Vector3[] _battleHeroLocationPostion;
        [SerializeField] private ScrollRect _scrollRect;

        [SerializeField] private GameObject _nodeHaveTitle;
        [SerializeField] private Image _fragmentIcon;
        [SerializeField] private TextMeshProUGUI _fragmentCount;
        [SerializeField] private Image _fragmentIconSS;
        [SerializeField] private TextMeshProUGUI _fragmentCountSS;
        [SerializeField] private Image _demonKingItemIcon;
        [SerializeField] private TextMeshProUGUI _demonKingItemCount;
        [SerializeField] private Transform _guardianHeroNode;
        [SerializeField] private Button _tabFormationButton;
        [SerializeField] private TextMeshProUGUI _title;
        [SerializeField] private Image _heroBg;
        [SerializeField] private GameObject _guardianNode;
        [SerializeField] private GameObject _guardianEmptyNode;
        [SerializeField] private GameObject _guardianSlotNode;
        [SerializeField] private GameObject _guardianTutorialMask;
        [SerializeField] private GameObject _guardianSelectNode;
        [SerializeField] private List<GameObject> _guardianStateImageList;
        [SerializeField] private TextMeshProUGUI _lockTitle;
        [SerializeField] private Button _guardianTipsButton;
        [SerializeField] private RectTransform _scrollNodeRect;
        [SerializeField] private RectTransform _lineNodeRect;
        [SerializeField] private RectTransform _buttonNodeRect;
        [SerializeField] private LuaBehaviour _selectLuaBehaviour;

        private const int RowCount = 4;
        private const float HeroItemWidth = 198;
        private const float HeroItemHeight = 292;
        private const float HeroItemHeightSpace = 19;
        private const float HeroItemWidthSpace = 28;
        private float _heroNodeWidth;

        private bool _showLockedHeroNode;
        private HeroItem _currentClickHeroItem;
        private HeroItem _swapHeroItemUse;
        private FragmentExchangeConst _fragmentConst;
        private ConstConfig _constConfig;
        private bool _onNormalFormation = true;
        private bool _guardianInit = false;
        private bool _onGuradianSet = false;

        private List<HeroItem> _haveHeroItems = new List<HeroItem>();
        private List<HeroItem> _lockHeroItems = new List<HeroItem>();
        private List<HeroItem> _guardianHeroItems = new List<HeroItem>();
        private List<HeroItem> _allHeroItems = new List<HeroItem>();
        private Queue<HeroItem> _heroItemPool = new Queue<HeroItem>();
        
        private EventSubscriber _eventSubscriber;

        private Sequence _sequence;
        private bool _hasInit = false;

        private void OnDisable()
        {
            HeroFilter = null;
            _onNormalFormation = true;
            LoadFormation();
        }

        private void OnEnable()
        {
            SetUIAdapter();
            _guardianTutorialMask.SetActive(false);
            if (_onNormalFormation)
                ShowGuardianTutorial();
            LoadFragmentCount();
        }
        
        private void OnDestroy()
        {
            if (_sequence != null)
                _sequence.Kill();
            _eventSubscriber.Dispose();
        }

        public void Init(params object[] param)
        {
            if (_hasInit)
                return;

            _hasInit = true;
            _heroNodeWidth = HeroItemWidth * RowCount + HeroItemWidthSpace * (RowCount - 1);
            (_haveHeroNode as RectTransform).SetWidth(_heroNodeWidth);
            (_lockHeroNode as RectTransform).SetWidth(_heroNodeWidth);
            InitHereItems();
            SetHaveHeroPositions();
            SetLockedHeroPositions();
            SetHeroLayout();
            LoadFragmentCount();

            _eventSubscriber = new EventSubscriber();
            _eventSubscriber.Subscribe<HeroUpgradeEvent>(OnHeroUpgraded);
            _eventSubscriber.Subscribe<HeroSkinUpdate>(OnSkinUpdate);
            _eventSubscriber.Subscribe<ItemUpdatedEvent>(OnItemChanged);

            _tabFormationButton.AddClick(OnClickTabFormation);
            _guardianTipsButton.AddClick(OnClickShowGuardianTips);
        }

        public void Show(params object[] param)
        {
            if (param is {Length: <= 0})
                return;
            
            int index = Convert.ToInt32(param[0]);
            if (index > 0)
            {
                var constConfig = ConfigManager.Instance.GetConfig<ConstConfig>();
                //开启援护系统且未进行引导时强制取消切页
                if (DB.User.SafeMaxChapter > constConfig.SupportSkillsUnlockChapter && 
                    PlayerPrefs.GetInt("GuardianSystem2", 0) <= 0)
                    return;
                
                var nameAction = _selectLuaBehaviour.ScriptEnv.Get<Action<int>>("SelectTab");
                nameAction?.Invoke(index);
            }
            
            LoadFormation();
        }

        private void ResetFilter()
        {
            ResetSwap();
            _heroFilter = null;
            SetHaveHeroEnable();
            SetLockHeroEnable();
            SetHeroLayout();
            this.DispatchEvent(
                    Witness<HeroItemListFilterResetEvent>._
                    );
        }

        private void OnItemChanged(ItemUpdatedEvent param)
        {
            if (_fragmentConst == null)
                _fragmentConst = ConfigManager.Instance.GetConfig<FragmentExchangeConst>();

            if (param != null && 
                (param.Id == _fragmentConst.UniversalCVRPurpleItem.Id || 
                 param.Id == _fragmentConst.UniversalCVROrangeItem.Id ||
                 param.Id == _constConfig.DemonKingTransferItemID))
            {
                LoadFragmentCount();
            }
        }

        private void OnHeroUpgraded(HeroUpgradeEvent evt)
        {
            if (_onNormalFormation)
            {
                SetHaveHeroPositions();
                SetLockedHeroPositions();
            }
            else
            {
                for (int i = 0; i < _lockHeroItems.Count; ++i)
                {
                    HeroItem heroItem = _lockHeroItems[i];
                    if (heroItem.HeroId == evt.HeroId)
                    {
                        RefreshGuardianHeroItem(heroItem);
                        heroItem.SetButtonAction(0);
                        return;
                    }
                }
            }
            // SetHaveHeroEnable();
            // SetUnlockNodePosition();
            // SetLockedHeroPositions();
            // SetHeroLayout();
        }

        private void OnSkinUpdate(HeroSkinUpdate evt)
        {
            for (var i = 0; i < _battleHeroNode.childCount; i++)
            {
                var node = _battleHeroNode.GetChild(i).GetComponent<HeroItem>();
                if (node.HeroId == evt.HeroId)
                {
                    node.FreshUI();
                    return;
                }
            }

            foreach(var item in _haveHeroItems)
            {
                if (item.HeroId == evt.HeroId)
                {
                    item.FreshUI();
                    return;
                }
            }

            if (_onNormalFormation == false)
            {
                for (var i = 0; i < _lockHeroNode.childCount; i++)
                {
                    var node = _lockHeroNode.GetChild(i).GetComponent<HeroItem>();
                    if (node.HeroId == evt.HeroId)
                    {
                        node.FreshUI();
                        return;
                    }
                }
            }
        }

        private void SetUIAdapter()
        {
            bool blessUnlock = DailyBlessingManager.IsUnlock();
            if (blessUnlock)
            {
                _lineNodeRect.anchoredPosition = new Vector2(_lineNodeRect.anchoredPosition.x, -866);
                _buttonNodeRect.anchoredPosition = new Vector2(_buttonNodeRect.anchoredPosition.x, -900);
                _scrollNodeRect.sizeDelta = new Vector2(0, -1078);
            }
            else
            {
                _lineNodeRect.anchoredPosition = new Vector2(_lineNodeRect.anchoredPosition.x, -652);
                _buttonNodeRect.anchoredPosition = new Vector2(_buttonNodeRect.anchoredPosition.x, -686);
                _scrollNodeRect.sizeDelta = new Vector2(0, -865);
            }
        }

        private void LoadFragmentCount()
        {
            if (_fragmentConst == null)
                _fragmentConst = ConfigManager.Instance.GetConfig<FragmentExchangeConst>();
            var itemConfig = ConfigManager.Instance.GetConfig<ItemConfigTable>().GetRowData(_fragmentConst.UniversalCVRPurpleItem.Id);
            var itemConfigSS = ConfigManager.Instance.GetConfig<ItemConfigTable>().GetRowData(_fragmentConst.UniversalCVROrangeItem.Id);
            _fragmentCount.text = StringUtils.FormatCurrency(BagManager.Instance.GetItemCount(_fragmentConst.UniversalCVRPurpleItem.Id));
            _fragmentCountSS.text = StringUtils.FormatCurrency(BagManager.Instance.GetItemCount(_fragmentConst.UniversalCVROrangeItem.Id));
            _fragmentIcon.sprite = itemConfig.Icon.Load<Sprite>();
            _fragmentIconSS.sprite = itemConfigSS.Icon.Load<Sprite>();
            
            if (_constConfig == null)
                _constConfig = ConfigManager.Instance.GetConfig<ConstConfig>();
            var demonKingItemConfig = ConfigManager.Instance.GetConfig<ItemConfigTable>().GetRowData(_constConfig.DemonKingTransferItemID);
            _demonKingItemIcon.sprite = demonKingItemConfig.Icon.Load<Sprite>();
            _demonKingItemCount.text = StringUtils.FormatCurrency(BagManager.Instance.GetItemCount(_constConfig.DemonKingTransferItemID));
        }

        private void InitHereItems()
        {
            var heroConfigTable = ConfigManager.Instance.GetConfig<HeroConfigTable>();
            var allHeroes = new List<HeroConfig>(heroConfigTable.Rows.Values);
            HeroConfig targetHeroConfig = null;
            foreach (var config in allHeroes)
            {
                if (HeroManager.Instance.IsHeroLocked(config))
                {
                    if (config.LockInvisible == 1)
                        continue;
                }

                if (ConfigManager.Instance.HeroDemonKingRef.IsDemonKingHero(config.Id))
                {
                    var originHeroID =
                        ConfigManager.Instance.HeroDemonKingRef.GetOriginHeroIDByDemonKingHeroID(config.Id);
                    if (!ConfigManager.Instance.HeroDemonKingRef.HeroIsTransfered(originHeroID))
                        continue;
                }
                
                if (ConfigManager.Instance.HeroDemonKingRef.HeroIsTransfered(config.Id))
                    continue;
                
                targetHeroConfig = config;

                DB.Hero.Contents.TryGetValue(targetHeroConfig.Id, out var level);
                var heroItem = CreateHeroItem(_haveHeroNode, targetHeroConfig, level);
                _allHeroItems.Add(heroItem);
            }
        }

        private int SortHeroes(HeroConfig a, HeroConfig b)
        {
            if (HeroManager.Instance.IsHeroLocked(a) &&
                HeroManager.Instance.IsHeroLocked(b))
            {
                var aUnlockLevel = a.UnlockConditions.GetValueOrDefault((int)HeroUnlockCondition.PassChapter);
                var bUnlockLevel = b.UnlockConditions.GetValueOrDefault((int)HeroUnlockCondition.PassChapter);
                if (aUnlockLevel * bUnlockLevel > 0)
                    return aUnlockLevel.CompareTo(bUnlockLevel);
                if (aUnlockLevel != 0 || bUnlockLevel != 0) // 有章节解锁条件与无章节解锁条件之间比较，章节解锁的排前面
                    return bUnlockLevel.CompareTo(aUnlockLevel);
            }

            if (_heroSorting != null)
            {
                return _heroSorting.Invoke(a, b);
            }

            DB.Hero.Contents.TryGetValue(a.Id, out var levelA);
            DB.Hero.Contents.TryGetValue(b.Id, out var levelB);
            if (levelA != levelB)
                return levelB.CompareTo(levelA);

            if (a.Quality != b.Quality)
                return b.Quality.CompareTo(a.Quality);

            return a.Id.CompareTo(b.Id);
        }

        private void ClearHeroItem(HeroItem item)
        {
            _heroItemPool.Enqueue(item);
            item.transform.SetParent(null);
            item.gameObject.SetActive(false);
        }

        private HeroItem GetHeroItem(Transform node)
        {
            HeroItem item = null;
            if (_heroItemPool.Count > 0)
            {
                item = _heroItemPool.Dequeue();
                item.transform.SetParent(node);
            }
            else
                item = Instantiate(_heroItem, node);

            item.gameObject.SetActive(true);
            return item;
        }

        private HeroItem CreateHeroItem(Transform node, HeroConfig config, int level)
        {
            var heroItem = GetHeroItem(node);
            heroItem.FreshUI(config.Id, level, config, () => DB.Hero.IsHeroAppeared(config.Id),
                () => DB.Hero.IsAppearHeroFull());
            heroItem.SetButtonAction(node == _lockHeroNode ? 0 : 1);
            heroItem.ActionChoose = HeroItemChoose;

            return heroItem;
        }


        private void UpdateNormalItemChoose(HeroItem heroItem, HeroItemChooseType type)
        {
            int index;
            switch (type)
            {
                case HeroItemChooseType.NormalChoose:
                    if (heroItem == _currentClickHeroItem)
                        return;
                    if (_currentClickHeroItem != null)
                        _currentClickHeroItem.HideButtons();
                    _currentClickHeroItem = heroItem;
                    break;
                case HeroItemChooseType.Remove:
                    // _httpRequest.HeroBattle(0, DB.Hero.GetAppearHeroIndex(heroItem.HeroId), () =>
                    // {
                    LogEvent.I.Hero_Choose(0, 0, heroItem.HeroId, heroItem.Level);

                    index = DB.Hero.GetAppearHeroIndex(heroItem.HeroId);
                    DB.Hero.Appear.Set(index, 0);
                    
                    heroItem.transform.SetParent(_haveHeroNode);
                    heroItem.SetBackground(1);
                    SetUnlockNodePosition();
                    NotifyAppearChanged();
                    _haveHeroItems.Add(heroItem);
                    SetHaveHeroEnable();
                    SetHeroLayout();
                    // });
                    break;
                case HeroItemChooseType.Use:
                    index = DB.Hero.GetAppearLocationIndex();
                    if (index == -1)
                        break;

                    LogEvent.I.Hero_Choose(heroItem.HeroId, heroItem.Level, 0, 0);

                    // _httpRequest.HeroBattle(heroItem.HeroId, index, () =>
                    // {
                    DB.Hero.SetAppearHero(heroItem.HeroId, index);
                    AudioManager.Instance.PlaySound("Hero_Go_Battle");
                    
                    heroItem.transform.SetParent(_battleHeroNode);
                    heroItem.transform.localPosition = _battleHeroLocationPostion[index];
                    heroItem.SetBackground(2);
                    // _haveHeroNode.GetComponent<GridLayoutGroup>().enabled = true;
                    SetUnlockNodePosition();
                    NotifyAppearChanged();
                    _haveHeroItems.Remove(heroItem);
                    SetHaveHeroEnable();
                    SetHeroLayout();
                    // });
                    break;
                case HeroItemChooseType.SwapUse:
                    _swapHeroItemUse = heroItem;
                    for (var i = 0; i < _battleHeroNode.childCount; i++)
                    {
                        var node = _battleHeroNode.GetChild(i).GetComponent<HeroItem>();
                        node.WaitingForRemove();
                    }
                    // _haveHeroNode.GetComponent<GridLayoutGroup>().enabled = false;
                    for (var i = 0; i < _haveHeroNode.childCount; i++)
                    {
                        var node = _haveHeroNode.GetChild(i);
                        node.gameObject.SetActive(node == heroItem.transform);
                    }

                    _lockHeroNode.parent.gameObject.SetActive(false);
                    break;
                case HeroItemChooseType.SwapRemove:

                    index = DB.Hero.GetAppearHeroIndex(heroItem.HeroId);
                    // _httpRequest.HeroBattle(_swapHeroItemUse.HeroId, index, () =>
                    // {
                    DB.Hero.SetAppearHero(_swapHeroItemUse.HeroId, index);
                    AudioManager.Instance.PlaySound("Hero_Go_Battle");

                    ResumeHeroItems();

                    if (_swapHeroItemUse != null)
                    {
                        LogEvent.I.Hero_Choose(_swapHeroItemUse.HeroId, _swapHeroItemUse.Level, heroItem.HeroId,
                            heroItem.Level);

                        var indexAtList = _haveHeroItems.IndexOf(_swapHeroItemUse);
                        _haveHeroItems.RemoveAt(indexAtList);
                        _haveHeroItems.Insert(indexAtList, heroItem);

                        var haveNodeLocalPosition = _swapHeroItemUse.transform.localPosition;
                        var battleNodeSiblingIndex = heroItem.transform.GetSiblingIndex();
                        _swapHeroItemUse.transform.SetParent(_battleHeroNode);
                        _swapHeroItemUse.transform.SetSiblingIndex(battleNodeSiblingIndex);
                        _swapHeroItemUse.transform.localPosition = _battleHeroLocationPostion[index];
                        heroItem.transform.SetParent(_haveHeroNode);
                        heroItem.transform.localPosition = haveNodeLocalPosition;
                        // _haveHeroNode.GetComponent<GridLayoutGroup>().enabled = true;
                        
                        _swapHeroItemUse.SetBackground(2);
                        heroItem.SetBackground(1);

                        _swapHeroItemUse.IsWaitingUse = false;
                        _swapHeroItemUse = null;

                        NotifyAppearChanged();
                        SetHaveHeroEnable();
                        SetHeroLayout();
                    }
                    // });

                    break;
                case HeroItemChooseType.Unlock:
                    LoadFormation();
                    break;
                case HeroItemChooseType.ShowDetail:
                    
                    var lay = HeroDetailLayer.Create();
                    //lay.SetData(heroItem);
                    heroItem.HideButtons();
                    break;
            }
        }


        private void UpdateGuardianItemChoose(HeroItem heroItem, HeroItemChooseType type)
        {
            int index;
            switch (type)
            {
                case HeroItemChooseType.NormalChoose:
                    if (heroItem == _currentClickHeroItem)
                        return;
                    if (_currentClickHeroItem != null)
                        _currentClickHeroItem.HideButtons();
                    _currentClickHeroItem = heroItem;
                    break;
                case HeroItemChooseType.Remove:
                    // _httpRequest.HeroBattle(0, DB.Hero.GetAppearHeroIndex(heroItem.HeroId), () =>
                    // {
                    LogEvent.I.Hero_Choose(0, 0, heroItem.HeroId, heroItem.Level);

                    index = DB.Hero.GetGuardianHeroIndex(heroItem.HeroId);
                    DB.Hero.SetGuardianHero(0, index);
                    heroItem.transform.SetParent(_haveHeroNode);
                    heroItem.SetBackground(1);
                    SetUnlockNodePosition();
                    NotifyAppearChanged();
                    _haveHeroItems.Add(heroItem);
                    RefreshGuardianHeroItem(heroItem);
                    SetHaveHeroEnable();
                    SetHeroLayout();
                    // });
                    break;
                case HeroItemChooseType.Use:
                case HeroItemChooseType.SwapUse:
                    _guardianEmptyNode.SetActive(false);
                    _swapHeroItemUse = heroItem;
                    for (var i = 0; i < _guardianHeroNode.childCount; i++)
                    {
                        var node = _guardianHeroNode.GetChild(i).GetComponent<HeroItem>();
                        node.WaitingForRemove(false);
                    }
                    // _haveHeroNode.GetComponent<GridLayoutGroup>().enabled = false;
                    _guardianSelectNode.SetActive(true);
                    for (var i = 0; i < _haveHeroNode.childCount; i++)
                    {
                        var node = _haveHeroNode.GetChild(i);
                        node.gameObject.SetActive(node == heroItem.transform);
                    }

                    _lockHeroNode.parent.gameObject.SetActive(false);
                    _onGuradianSet = true;
                    break;
                case HeroItemChooseType.SwapRemove:

                    index = DB.Hero.GetGuardianHeroIndex(heroItem.HeroId);
                    // _httpRequest.HeroBattle(_swapHeroItemUse.HeroId, index, () =>
                    // {
                    DB.Hero.SetGuardianHero(_swapHeroItemUse.HeroId, index);
                    AudioManager.Instance.PlaySound("Hero_Go_Battle");

                    for (var i = 0; i < _guardianHeroNode.childCount; i++)
                    {
                        var node = _guardianHeroNode.GetChild(i).GetComponent<HeroItem>();
                        node.StopWaitingForRemove();
                    }
                    _guardianSelectNode.SetActive(false);
                    if (_swapHeroItemUse != null)
                    {
                        LogEvent.I.Hero_Choose(_swapHeroItemUse.HeroId, _swapHeroItemUse.Level, heroItem.HeroId,
                            heroItem.Level);

                        var indexAtList = _haveHeroItems.IndexOf(_swapHeroItemUse);
                        _haveHeroItems.RemoveAt(indexAtList);
                        _haveHeroItems.Insert(indexAtList, heroItem);

                        var haveNodeLocalPosition = _swapHeroItemUse.transform.localPosition;
                        var battleNodeSiblingIndex = heroItem.transform.GetSiblingIndex();
                        _swapHeroItemUse.transform.SetParent(_guardianHeroNode);
                        _swapHeroItemUse.transform.SetSiblingIndex(battleNodeSiblingIndex);
                        var rect = _swapHeroItemUse.transform as RectTransform;
                        rect.anchoredPosition = GetGuardianSlotPosition(index);
                        heroItem.transform.SetParent(_haveHeroNode);
                        heroItem.transform.localPosition = haveNodeLocalPosition;
                        // _haveHeroNode.GetComponent<GridLayoutGroup>().enabled = true;
                        
                        _swapHeroItemUse.SetBackground(2);
                        heroItem.SetBackground(1);

                        _swapHeroItemUse.IsWaitingUse = false;
                        _swapHeroItemUse = null;

                        _onGuradianSet = false;
                        NotifyAppearChanged();
                        SetHaveHeroEnable();
                        SetUnlockNodePosition();
                        SetHeroLayout();
                    }
                    // });

                    break;
                case HeroItemChooseType.Unlock:
                    LoadFormation();
                    break;
                case HeroItemChooseType.ShowDetail:
                    
                    var lay = HeroDetailLayer.Create();
                    //lay.SetData(heroItem);
                    heroItem.HideButtons();
                    break;
            }
        }

        private void HeroItemChoose(HeroItem heroItem, HeroItemChooseType type)
        {
            int index;
            if (_onNormalFormation)
                UpdateNormalItemChoose(heroItem, type);
            else
                UpdateGuardianItemChoose(heroItem, type);
        }

        private void NotifyAppearChanged()
        {
            var homeHeroes = FindObjectOfType<HomeHeroes>();
            if (homeHeroes != null)
                homeHeroes.ShowHeroes();
        }

        private void SetUnlockNodePosition()
        {
            _lockHeroNode.parent.gameObject.SetActive(_showLockedHeroNode);
        }

        private void SetHaveHeroPositions()
        {
            var showList = _haveHeroItems.Where(item => item.gameObject.activeSelf).ToList();
            showList.Sort((a, b) => SortHeroes(a.HeroConfig, b.HeroConfig));

            var count = showList.Count;
            var totalRow = 0;
            if (count > 1)
                totalRow = (count - 1) / RowCount;
            _haveHeroNode.SetHeight((totalRow + 1) * (HeroItemHeight + HeroItemHeightSpace) + 30);

            for (var i = count - 1; i >= 0; i--)
            {
                var index = count - 1 - i;
                var item = showList[i];
                if (!item.gameObject.activeSelf)
                    continue;
                item.transform.SetSiblingIndex(index);
                var row = i / RowCount;
                var column = i % RowCount;
                item.transform.localPosition =
                    new Vector3((column + 0.5f) * HeroItemWidth + column * HeroItemWidthSpace - _heroNodeWidth / 2,
                        -((row + 0.5f) * HeroItemHeight + row * HeroItemHeightSpace));
            }
        }

        private void SetLockedHeroPositions()
        {
            var showList = _lockHeroItems.Where(item => item.gameObject.activeSelf).ToList();
            showList.Sort((a, b) =>
            {
                var configA = a.HeroConfig;
                var configB = b.HeroConfig;
                int levelA = 0;
                int levelB = 0;
                DB.Hero.Contents.TryGetValue(configA.Id, out levelA);
                DB.Hero.Contents.TryGetValue(configB.Id, out levelB);

                if (levelA != levelB)
                {
                    return levelB.CompareTo(levelA);
                }
                else
                {
                    return configA.Quality != configB.Quality
                    ? configB.Quality.CompareTo(configA.Quality)
                    : configA.Id.CompareTo(configB.Id);
                }
            });
            var count = showList.Count;
            var totalRow = 0;
            if (count > 1)
                totalRow = (count - 1) / RowCount;
            _lockHeroNode.SetHeight((totalRow + 1) * (HeroItemHeight + HeroItemHeightSpace) + 30);
            // for (var i = 0; i < count; ++i)
            // {
            //     var item = showList[i];
            //     if (!item.gameObject.activeSelf)
            //         continue;
            //     item.transform.SetSiblingIndex(i);
            // }

            for (var i = count - 1; i >= 0; i--)
            {
                var index = count - 1 - i;
                var item = showList[i];
                if (!item.gameObject.activeSelf)
                    continue;
                item.transform.SetSiblingIndex(index);
                var row = i / RowCount;
                var column = i % RowCount;
                item.transform.localPosition =
                    new Vector3((column + 0.5f) * HeroItemWidth + column * HeroItemWidthSpace - _heroNodeWidth / 2,
                        -((row + 0.5f) * HeroItemHeight + row * HeroItemHeightSpace));
            }
        }

        private void SetHeroLayout()
        {
            var count = _haveHeroItems.Count(item => item.gameObject.activeSelf);
            if (count == 0)
            {
                if (_haveHeroNode.gameObject.activeSelf)
                    _haveHeroNode.gameObject.SetActive(false);
                _nodeHaveTitle.SetActive(false);
            }
            else
            {
                if (!_haveHeroNode.gameObject.activeSelf)
                    _haveHeroNode.gameObject.SetActive(true);
                _nodeHaveTitle.SetActive(true);

                SetHaveHeroPositions();
            }

            if (_onNormalFormation == false)
                UpdateGuardianFormationState();

            LayerManager.Instance.StartCoroutine(FreshScrollRectContentLayout());
        }

        private IEnumerator FreshScrollRectContentLayout()
        {
            yield return null;
            LayoutRebuilder.ForceRebuildLayoutImmediate(_scrollRect.content.transform as RectTransform);
        }

        private void SetHaveHeroEnable()
        {
            if (_heroFilter != null)
            {
                _heroFilter.TryGetValue(HeroFilterType.Quality, out var qualities);
                _heroFilter.TryGetValue(HeroFilterType.Attribute, out var attributes);

                if (qualities is { Count: > 0 } && attributes is { Count: > 0 })
                {
                    foreach (var item in _haveHeroItems)
                    {
                        if (qualities.Contains(item.HeroConfig.Quality) && attributes.Contains(item.HeroConfig.AttackAttribute))
                        {
                            item.gameObject.SetActive(true);
                            continue;
                        }

                        item.gameObject.SetActive(false);
                    }
                }
                else if (qualities is { Count: > 0 } || attributes is { Count: > 0 })
                {
                    foreach (var item in _haveHeroItems)
                    {
                        if (qualities != null && qualities.Contains(item.HeroConfig.Quality))
                        {
                            item.gameObject.SetActive(true);
                            continue;
                        }

                        if (attributes != null && attributes.Contains(item.HeroConfig.AttackAttribute))
                        {
                            item.gameObject.SetActive(true);
                            continue;
                        }

                        item.gameObject.SetActive(false);
                    }
                }
                else
                {
                    foreach (var item in _haveHeroItems)
                    {
                        item.gameObject.SetActive(true);
                    }
                }
            }
            else
            {
                foreach (var item in _haveHeroItems)
                {
                    item.gameObject.SetActive(true);
                }
            }

            SetHaveHeroPositions();
        }

        private void SetLockHeroEnable()
        {
            if (_heroFilter != null)
            {
                _heroFilter.TryGetValue(HeroFilterType.Quality, out var qualities);
                _heroFilter.TryGetValue(HeroFilterType.Attribute, out var attributes);
                if (qualities is { Count: > 0 } && attributes is { Count: > 0 })
                {
                    foreach (var item in _lockHeroItems)
                    {
                        if (qualities.Contains(item.HeroConfig.Quality) 
                            && attributes.Contains(item.HeroConfig.AttackAttribute))
                        {
                            item.gameObject.SetActive(true);
                            continue;
                        }

                        item.gameObject.SetActive(false);
                    }
                }
                else if (qualities is { Count: > 0 } || attributes is { Count: > 0 })
                {
                    foreach (var item in _lockHeroItems)
                    {
                        if (qualities != null && qualities.Contains(item.HeroConfig.Quality))
                        {
                            item.gameObject.SetActive(true);
                            continue;
                        }

                        if (attributes != null && attributes.Contains(item.HeroConfig.AttackAttribute))
                        {
                            item.gameObject.SetActive(true);
                            continue;
                        }

                        item.gameObject.SetActive(false);
                    }
                }
                else
                {
                    foreach (var item in _lockHeroItems)
                    {
                        item.gameObject.SetActive(true);
                    }
                }
            }
            else
            {
                foreach (var item in _lockHeroItems)
                {
                    item.gameObject.SetActive(true);
                }
            }
        }

        private void ResumeHeroItems()
        {
            for (var i = 0; i < _battleHeroNode.childCount; i++)
            {
                var node = _battleHeroNode.GetChild(i).GetComponent<HeroItem>();
                node.StopWaitingForRemove();
            }
            _guardianSelectNode.SetActive(false);
            SetHaveHeroEnable();
            if (_showLockedHeroNode)
                _lockHeroNode.parent.gameObject.SetActive(true);
            SetHeroLayout();
        }

        public void ClickLayerBackground()
        {
            if (_currentClickHeroItem != null)
            {
                _currentClickHeroItem.HideButtons();
                _currentClickHeroItem = null;
            }

            if (_swapHeroItemUse != null)
            {
                _swapHeroItemUse.IsWaitingUse = false;
                _swapHeroItemUse = null;
                if (_onNormalFormation)
                    ResumeHeroItems();
                else
                {
                    _onGuradianSet = false;
                    for (var i = 0; i < _guardianHeroNode.childCount; i++)
                    {
                        var node = _guardianHeroNode.GetChild(i).GetComponent<HeroItem>();
                        node.StopWaitingForRemove();
                    }
                    _guardianSelectNode.SetActive(false);
                    SetHaveHeroEnable();
                    SetUnlockNodePosition();
                    SetLockedHeroPositions();
                    SetHeroLayout();
                }
            }
        }

        public void LoadGuardianHeroItemList()
        {
            // if (_guardianHeroItems.Count == 4)
            //     return;

            // for (int i = 0; i <= 3; ++i)
            // {
            //     _guardianHeroItems.Add(Instantiate(_heroItem, _guardianHeroNode));
            // }

            if (_guardianInit)
                return;

            _guardianInit = true;
            for (int i = 0; i < 4; ++i)
            {
                int heroID = DB.Hero.GetGuardianHero(i);
                if (heroID > 0)
                {
                    DB.Hero.Contents.TryGetValue(heroID, out var level);
                    var config = ConfigManager.Instance.GetConfig<HeroConfigTable>().Rows[heroID];
                    var heroItem = CreateHeroItem(_guardianHeroNode, config, level);
                    heroItem.transform.localPosition = _battleHeroLocationPostion[i];
                }
            }   
        }

        private void ShowDisappearAnim()
        {
            if (_sequence != null)
                _sequence.Kill();

            _guardianTutorialMask.SetActive(true);
            _sequence = DOTween.Sequence();
            _sequence.AppendInterval(0.1f);
            if (_onNormalFormation)
            {
                for (var i = _guardianHeroNode.childCount - 1; i >= 0; --i)
                {
                    var item = _guardianHeroNode.GetChild(i).GetComponent<HeroItem>();
                    _sequence.Join(item.transform.DORotate(new Vector3(0, -90, 0), 0.1f));
                }
                _sequence.AppendCallback(()=>
                {
                    _guardianNode.SetActive(false);
                });
            }
            else
            {
                for (var i = 0; i < _battleHeroNode.childCount; i++)
                {
                    var item = _battleHeroNode.GetChild(i).GetComponent<HeroItem>();
                    _sequence.Join(item.transform.DORotate(new Vector3(0, -90, 0), 0.1f));
                }
                _sequence.AppendCallback(()=>
                {
                    _battleHeroNode.SetActive(false);
                });
            }
            _sequence.AppendInterval(0.1f);
        }

        private Vector2 GetGuardianSlotPosition(int index)
        {
            var rect = _guardianSlotNode.transform.GetChild(index) as RectTransform;
            return rect.anchoredPosition;
        }

        private void LoadFormation()
        {
            _battleHeroNode.SetActive(_onNormalFormation);
            _guardianNode.SetActive(!_onNormalFormation);

            if (_onNormalFormation)
            {
                _lockTitle.SetLang("Locked_1");
                _showLockedHeroNode = false;
                _haveHeroItems.Clear();
                _lockHeroItems.Clear();
                for (int i = _allHeroItems.Count - 1; i >= 0; --i)
                {
                    HeroItem item = _allHeroItems[i];
                    DB.Hero.Contents.TryGetValue(item.HeroId, out var level);
                    var index = DB.Hero.GetAppearHeroIndex(item.HeroId);
                    item.FreshUI(item.HeroId, item.Level, item.HeroConfig, () => DB.Hero.IsHeroAppeared(item.HeroId),
                            () => DB.Hero.IsAppearHeroFull());

                    item.gameObject.SetActive(true);
                    if (level > 0)
                    {
                        item.SetButtonAction(1);
                        if (index >= 0)
                        {
                            item.transform.SetParent(_battleHeroNode);
                            item.transform.localPosition = _battleHeroLocationPostion[index];
                        }
                        else
                        {
                            _haveHeroItems.Add(item);
                            item.transform.SetParent(_haveHeroNode);
                        }
                    }
                    else
                    {
                        item.SetButtonAction(0);
                        if (HeroManager.Instance.IsHeroLocked(item.HeroConfig))
                        {
                            if (item.HeroConfig.LockInvisible == 1) 
                                continue;
                            _showLockedHeroNode = true;
                            item.transform.SetParent(_lockHeroNode);
                            _lockHeroItems.Add(item);
                        }
                        else
                        {
                            item.FreshUI(item.HeroId, item.Level, item.HeroConfig, () => DB.Hero.Appear.Contains(item.HeroId),
                                () => DB.Hero.IsAppearHeroFull());
                            _haveHeroItems.Add(item);
                            item.transform.SetParent(_haveHeroNode);
                        }
                    }
                }
            }
            else
            {
                _lockTitle.SetLang("support_hero_unlock");

                _haveHeroItems.Clear();
                _lockHeroItems.Clear();
                _guardianHeroItems.Clear();
                for(int i = _allHeroItems.Count - 1; i >= 0; --i)
                {
                    HeroItem heroItem = _allHeroItems[i];
                    heroItem.gameObject.SetActive(true);
                    heroItem.transform.localRotation = Quaternion.Euler(0, 0, 0);
                    bool isGuardianHero = ConfigManager.Instance.GuardianRef.IsGuardianHero(heroItem.HeroId);
                    bool isUnlockGuardian = ConfigManager.Instance.GuardianRef.IsUnlockGuardian(heroItem.HeroId);
                    heroItem.FreshUI(heroItem.HeroId, heroItem.Level, heroItem.HeroConfig, () => DB.Hero.GuardianHero.Contains(heroItem.HeroId),
                            () => true);
                    int index = DB.Hero.GetGuardianHeroIndex(heroItem.HeroId);
                    if (index >= 0)
                    {
                        heroItem.transform.SetParent(_guardianHeroNode);
                        var rect = heroItem.transform as RectTransform;
                        rect.anchoredPosition = GetGuardianSlotPosition(index);
                        heroItem.SetBackground(2);
                        heroItem.SetButtonAction(1);
                        _guardianHeroItems.Add(heroItem);
                    }
                    else
                    {
                        if (isGuardianHero)
                        {
                            if (isUnlockGuardian == false)
                            {
                                _showLockedHeroNode = true;
                                _lockHeroItems.Add(heroItem);
                                DB.Hero.Contents.TryGetValue(heroItem.HeroId, out var level);
                                heroItem.SetButtonAction(0);
                                heroItem.transform.SetParent(_lockHeroNode);
                                if (level > 0)
                                    heroItem.SetItemDesc("get_source_support");
                            }
                            else
                            {
                                var appearIndex = DB.Hero.GetAppearHeroIndex(heroItem.HeroId);
                                _haveHeroItems.Add(heroItem);
                                heroItem.transform.SetParent(_haveHeroNode);
                                heroItem.SetButtonAction(1);
                                if (appearIndex >= 0)
                                    heroItem.SetItemDesc("main_lineup_title");
                            }
                        }
                        else
                        {
                            ClearHeroItem(heroItem);
                        }
                    }
                }
            }

            SetHaveHeroEnable();
            SetUnlockNodePosition();
            SetLockedHeroPositions();
            SetHeroLayout();
        }

        private void OnClickTabFormation()
        {
            if (_onNormalFormation)
            {
                for (var i = 0; i < _battleHeroNode.childCount; i++)
                {
                    var node = _battleHeroNode.GetChild(i).GetComponent<HeroItem>();
                    node.StopWaitingForRemove();
                }
            }
            else
            {
                for (var i = 0; i < _guardianHeroNode.childCount; i++)
                {
                    var node = _guardianHeroNode.GetChild(i).GetComponent<HeroItem>();
                    node.StopWaitingForRemove();
                }
                _guardianSelectNode.SetActive(false);
            }
            _onNormalFormation = !_onNormalFormation;
            ResetFilter();
            LoadFormation();
        }

        //刷新援护切页下的HeroItem
        private void RefreshGuardianHeroItem(HeroItem heroItem)
        {
            DB.Hero.Contents.TryGetValue(heroItem.HeroId, out var level);
            var index = DB.Hero.GetAppearHeroIndex(heroItem.HeroId);
            bool isGuardianHero = ConfigManager.Instance.GuardianRef.IsGuardianHero(heroItem.HeroId);
            bool isUnlockGuardian = ConfigManager.Instance.GuardianRef.IsUnlockGuardian(heroItem.HeroId);
            if (isGuardianHero == false)
                return;

            heroItem.FreshUI(heroItem.HeroId, heroItem.Level, heroItem.HeroConfig, () => DB.Hero.GuardianHero.Contains(heroItem.HeroId),
                            () => true);
            if (level > 0)
            {
                if (isUnlockGuardian)
                {
                    if (index >= 0)
                        heroItem.SetItemDesc("main_lineup_title");
                }
                else
                    heroItem.SetItemDesc("get_source_support");
            }

        }

        private void UpdateGuardianFormationState()
        {
            for (int i = 0; i < DB.Hero.GuardianHero.Length; ++i)
            {
                int heroID = DB.Hero.GetGuardianHero(i);
                _guardianStateImageList[i].SetActive(heroID > 0);
            }
        }

        public void OnClickSelectTabMenu(int index)
        {
            bool normal = index == 1;
            if (_onNormalFormation == normal)
                return;

            OnClickTabFormation();
        }

        private void ClearGuardianTutorial()
        {
            PlayerPrefs.SetInt("GuardianSystem2", 1);
            PlayerPrefs.SetInt("GuardianSystem3", 1);
        }

        private void ShowGuardianTutorial()
        {
            var constConfig = ConfigManager.Instance.GetConfig<ConstConfig>();
            if (DB.User.SafeMaxChapter <= constConfig.SupportSkillsUnlockChapter)
                return;

            int tutorial2 = PlayerPrefs.GetInt("GuardianSystem2", 0);
            int tutorial3 = PlayerPrefs.GetInt("GuardianSystem3", 0);
            for (int i = 0; i < 4; ++i)
            {
                if (DB.Hero.GetGuardianHero(i) > 0)
                {
                    ClearGuardianTutorial();
                    return;
                }
            }

            // if (ConfigManager.Instance.GuardianRef.HasUnlockGuardianHero())
            // {
            //     ClearGuardianTutorial();
            //     return;
            // }

            int index = 0;
            // int guardianHeroID = 0;
            // if (tutorial3 <= 0)
            // {
            //     guardianHeroID = ConfigManager.Instance.GuardianRef.GetEnoughGuardianHero();
            //     if (guardianHeroID > 0)
            //     {
            //         index = 3;
            //         ClearGuardianTutorial();
            //     }
            // }

            if (tutorial2 <= 0)
            {
                index = 2;
                PlayerPrefs.SetInt("GuardianSystem2", 1);
            }

            if (index <= 0)
                return;

            if (_sequence != null)
                _sequence.Kill();

            _guardianTutorialMask.SetActive(true);
            _sequence = DOTween.Sequence();
            _sequence.AppendInterval(0.5f);
            _sequence.AppendCallback(()=>
            {
                _guardianTutorialMask.SetActive(false);
                TutorialsManager.Instance.Load($"GuardianSystem{index}").Play();
            });
            
        }


        public void OnClickGuardianSlot(int index)
        {
            if (_onGuradianSet == false)
                return;

            _onGuradianSet = false;
            DB.Hero.SetGuardianHero(_swapHeroItemUse.HeroId, index);
            _haveHeroItems.Remove(_swapHeroItemUse);
            _swapHeroItemUse.transform.SetParent(_guardianHeroNode);
            var rect = _swapHeroItemUse.transform as RectTransform;
            rect.anchoredPosition = GetGuardianSlotPosition(index);
            _swapHeroItemUse.FreshUI();
            _swapHeroItemUse.SetBackground(2);
            _swapHeroItemUse.IsWaitingUse = false;
            SetUnlockNodePosition();
            SetHaveHeroEnable();
            SetHeroLayout();

            if (index < 2)
            {
                rect.SetAsLastSibling();
            }
            else
            {
                rect.SetAsFirstSibling();
            }

            for (var i = 0; i < _guardianHeroNode.childCount; i++)
            {
                var node = _guardianHeroNode.GetChild(i).GetComponent<HeroItem>();
                node.StopWaitingForRemove();
            }
            _guardianSelectNode.SetActive(false);
        }

        private void OnClickShowGuardianTips()
        {
            LayerManager.Instance.LoadContent(LayerTag.Dialog, "ui/hero/GuardianRulePop");
        }

        public void OnTalentUpdate()
        {
            if (_onNormalFormation == false)
            {
                LoadFormation();
            }
        }

        public static void Create()
        {
            LayerManager.Instance.LoadContent(LayerTag.Dialog, "ui/hero/HeroesLayer");
        }


        public Dictionary<int, int> ResourceHeroes {
            get
            {
                var allHeroes = ConfigManager.Instance.GetConfig<HeroConfigTable>().Rows.Keys;
                return allHeroes.ToDictionary(id => id, 
                    id => DB.Hero.Contents.TryGetValue(id, out var level) ? level : 0);
            }
        }

        private void ResetSwap()
        {
            if (_currentClickHeroItem != null)
            {
                _currentClickHeroItem.HideButtons();
                _currentClickHeroItem = null;
            }

            if (_swapHeroItemUse != null)
            {
                _swapHeroItemUse.IsWaitingUse = false;
                _swapHeroItemUse = null;
            }
            for (var i = 0; i < _battleHeroNode.childCount; i++)
            {
                var node = _battleHeroNode.GetChild(i).GetComponent<HeroItem>();
                node.StopWaitingForRemove();
            }
            _guardianSelectNode.SetActive(false);
            if (_showLockedHeroNode)
                _lockHeroNode.parent.gameObject.SetActive(true);
        }

        private Func<HeroConfig, HeroConfig, int> _heroSorting;
        
        public Func<HeroConfig, HeroConfig, int> HeroSorting {
            set
            {
                if (value == null)
                    return;
                ResetSwap();
                _heroSorting = value;
                SetHaveHeroPositions();
                SetLockedHeroPositions();
            }
        }

        private Dictionary<HeroFilterType, List<int>> _heroFilter;
        public Dictionary<HeroFilterType, List<int>> HeroFilter {
            set
            {
                ResetSwap();
                _heroFilter = value;
                SetHaveHeroEnable();
                SetLockHeroEnable();
                SetLockedHeroPositions();
                SetHeroLayout();
            }
        }
    }
}