using System;
using System.Collections.Generic;
using LuaScript;
using UnityEngine;

namespace App.UI.Hero
{
    [RequireComponent(typeof(LuaGridViewController))]
    public class HeroFilterContentLua : MonoBehaviour, IHeroItemList
    {
        private readonly Dictionary<int, int> _resourceHeroes = new();
        public void AddResourceHero(int id, int level)
        {
            _resourceHeroes[id] = level;
        }
        public void RemoveResourceHero(int id)
        {
            _resourceHeroes.Remove(id);
        }
        public void ClearResourceHeroes()
        {
            _resourceHeroes.Clear();
        }

        public Dictionary<int, int> ResourceHeroes => _resourceHeroes;
        
        private Func<HeroConfig, HeroConfig, int> _heroSorting;
        public Action OnHeroSortingChanged { set; private get; }

        public Func<HeroConfig, HeroConfig, int> HeroSorting
        {
            set
            {
                _heroSorting = value;
                OnHeroSortingChanged?.Invoke();
            }
            get => _heroSorting;
        }

        private Dictionary<HeroFilterType, List<int>> _heroFilter;
        public Action OnHeroFilterChanged{ set; private get; }

        public Dictionary<HeroFilterType, List<int>> HeroFilter
        {
            get => _heroFilter;
            set
            {
                _heroFilter = value;
                OnHeroFilterChanged?.Invoke();
            }
        }
    }
}