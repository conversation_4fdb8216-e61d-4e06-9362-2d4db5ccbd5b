using System.Linq;
using App.Bag;
using App.Config;
using App.GamePlay;
using App.GamePlay.UI;
using App.UI.Utility;
using DG.Tweening;
using FQDev.AssetBundles;
using FQDev.UI.Layer;
using Language;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Button = FQDev.UI.Button;

namespace App.UI.Hero
{
    public class HeroLevelUpLayer : PopupLayerContent
    {
        [SerializeField] private TextMeshProUGUI _textName;
        [SerializeField] private Button _buttonClose;
        [SerializeField] private HeroAttributes _heroAttributes;

        [SerializeField] private HeroItemBase _heroItem;
        [SerializeField] private Image _imageProgress;
        [SerializeField] private Image _imageLevelBg;
        [SerializeField] private GameObject _nodeUpgradeProgress;
        [SerializeField] private GameObject _nodeUpgradeProgressArrow;
        [SerializeField] private GameObject _nodeMaxProgress;
        
        [SerializeField] private TextMeshProUGUI _textGradeProgress;
        [SerializeField] private TextMeshProUGUI _textMax_Count;

        [SerializeField] private SkillItem _skillItem;
        [SerializeField] private TextMeshProUGUI _textSkillDesc;

        [SerializeField] private Animator _itemAnimator;
        [SerializeField] private ParticleSystem _itemParticle;
        [SerializeField] private ParticleSystem _itemParticle2;
        [SerializeField] private ParticleSystem _itemParticle3;
        [SerializeField] private ParticleSystem _itemParticle4;
        
        [SerializeField] private GameObject _nodeFragment;
        [SerializeField] private GameObject _nodeCloseTip;
        [SerializeField] private GameObject _progressNode;
        
        private void Awake()
        {
            _buttonClose.AddClick(Close);
            _buttonClose.interactable = false;
        }
        
        private void SetDemonKingUI(int heroID)
        {
            if (ConfigManager.Instance.HeroDemonKingRef.IsDemonKingHero(heroID))
            {
                _imageLevelBg.rectTransform.sizeDelta = new Vector2(_imageLevelBg.rectTransform.sizeDelta.x, 110);
                _nodeMaxProgress.SetActive(false);
                _progressNode.SetActive(false);
            }
            else
            {
                _imageLevelBg.rectTransform.sizeDelta = new Vector2(_imageLevelBg.rectTransform.sizeDelta.x, 50);
            }
        }

        public void SetData(int id, int level, HeroConfig config)
        {
            _heroAttributes.HideAttributeNodes();
            if (level <= 0)
                return;

            _progressNode.SetActive(true);
            //Attribute
            _textName.SetLang(config.Name);
            _heroItem.RefreshUI(id, level);
            

            var heroMaxLevel = ConfigManager.Instance.HeroLevelRef.GetHeroMaxLevel(id);
            var heroLevelConfig = ConfigManager.Instance.HeroLevelRef[id, level];
            heroLevelConfig.Costs.TryGetValue(config.LevelUpItemId, out var costCount);

            var count = BagManager.Instance.GetItemCount(config.LevelUpItemId);
            var newLevel = level + 1;
            var heroNextLevelConfig =
                newLevel > heroMaxLevel ? null : ConfigManager.Instance.HeroLevelRef[id, newLevel];

            var beforeLevelupCount = count + costCount;
            _imageProgress.fillAmount = (float) beforeLevelupCount / costCount;
            _textGradeProgress.text = $"{beforeLevelupCount}/{costCount}";
            _nodeUpgradeProgress.SetActive(true);
            _nodeUpgradeProgressArrow.SetActive(false);
            _nodeFragment.SetActive(true);
            
            var particle1 = config.Quality == 6 ? _itemParticle3 : _itemParticle;
            var particle2 = config.Quality == 6 ? _itemParticle4 : _itemParticle2;
            
            DOTween.Sequence(transform).AppendCallback(() =>
                {
                    _itemAnimator.Play("Hero-upgrade");
                    particle1.Play();
                    _nodeUpgradeProgressArrow.SetActive(true);
                    _nodeFragment.SetActive(false);
                })
                .Join(_nodeUpgradeProgressArrow.transform.DOScale(1.2f, 0.5f))
                .AppendInterval(0.5f)
                .AppendCallback(() =>
                {
                    _nodeUpgradeProgressArrow.transform.localScale = Vector3.one;
                    _nodeUpgradeProgress.SetActive(false);
                    _nodeUpgradeProgressArrow.SetActive(false);
                    _nodeFragment.SetActive(true);
                    _textGradeProgress.DOCounter(beforeLevelupCount, count, 1f,
                            (v) => { _textGradeProgress.text = $"{v}/{costCount}"; });
                    _imageProgress.DOCounter((float) 1, 0, 1f, (v) => { _imageProgress.fillAmount = v; });
                })
                .AppendInterval(1.1f)
                .AppendCallback(() =>
                {
                    particle2.Play();
                    _heroItem.RefreshUI(id, newLevel);
                    
                    if (heroNextLevelConfig != null)
                    {
                        if (newLevel == heroMaxLevel)
                        {
                            //"Max"
                            _textMax_Count.text = $"{count}";
                            _imageProgress.transform.parent.gameObject.SetActive(false);
                            _nodeMaxProgress.SetActive(true);
                            _nodeUpgradeProgress.SetActive(false);
                            _nodeUpgradeProgressArrow.SetActive(false);
                            _nodeFragment.SetActive(true);
                        }
                        else
                        {
                            heroNextLevelConfig.Costs.TryGetValue(config.LevelUpItemId, out var costNextCount);
                            _textGradeProgress.text = $"{count}/{costNextCount}";
                            
                            if (count < costNextCount)
                            {
                                var progress = (float) count / costNextCount;
                                _imageProgress.DOCounter(0, progress, 0.5f, (v) => { _imageProgress.fillAmount = v; })
                                    .SetDelay(0.5f);
                            }
                            
                            _nodeUpgradeProgress.SetActive(count >= costNextCount);
                            _nodeUpgradeProgressArrow.SetActive(count >= costNextCount);
                            _nodeFragment.SetActive(count < costNextCount);
                        }
                    }
                    else
                    {
                        _textGradeProgress.text = "";
                        _nodeUpgradeProgress.SetActive(true);
                    }
                    SetDemonKingUI(id);
                })
                .AppendInterval(0.5f)
                .AppendCallback(() =>
                {
                    _heroAttributes.SetAttribute(id, level, heroMaxLevel, config, heroLevelConfig);
                })
                .AppendInterval(0.5f)
                .AppendCallback(() =>
                {
                    foreach (var skill in config.Skills)
                    {
                        if (skill.Key > level && skill.Key == newLevel)
                        {
                            var skillGroup = ConfigManager.Instance.SkillRef.GetSkillGroup(skill.Value);
                            var skillConfig = skillGroup.SkillConfig;
                            var skillValueConfig = ConfigManager.Instance.GetConfig<SkillValueConfigTable>().Rows
                                .SingleOrDefault(a => a.Value.SkillId == skill.Value).Value;
                            if (skillValueConfig != null)
                            {
                                var node = _skillItem.transform.parent;
                                node.localScale = Vector3.zero;
                                node.gameObject.SetActive(true);

                                _skillItem.FreshUI(skill.Value);
                                if (string.IsNullOrEmpty(skillConfig.Sketch) == false)
                                    _textSkillDesc.SetLang(skillConfig.Sketch);
                                else
                                    _textSkillDesc.SetLang(skillConfig.Name, skillValueConfig.ValueMin);
                                node.DOScale(Vector3.one, 0.3f).SetEase(Ease.OutBack);
                            }

                            break;
                        }
                    }
                    
                    _buttonClose.interactable = true;
                    _nodeCloseTip.gameObject.SetActive(true);
                });

            SetDemonKingUI(id);
        }

        private void OnDestroy()
        {
            _textGradeProgress.DOKill();
            _imageProgress.DOKill();
            transform.DOKill();
            _skillItem.transform.parent.DOKill();
        }

        public static HeroLevelUpLayer Create()
        {
            return (LayerManager.Instance.LoadContent(LayerTag.Popup, "ui/hero/HeroLevelUpLayer") as HeroLevelUpLayer);
        }
    }
}