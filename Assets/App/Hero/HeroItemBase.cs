using System;
using App.Bag;
using App.Config;
using App.Hero;
using App.UI.Utility;
using FQDev.EventSystem;
using Language;
using LuaScript;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using UserData;

namespace App.UI.Hero
{
    public class HeroItemBase : MonoBehaviour
    {
        private const string LuaSetUI = "SetUI";

        [SerializeField] private Sprite _unlockBackground;
        [SerializeField] private Sprite _unlockIconBackground;
        [SerializeField] private Sprite _heroBoxGrayAttributesBox;
        [SerializeField] private Sprite _heroBoxGrayLevelBox;

        public enum State
        {
            Locked,
            Unlocked,
            Summoned
        }

        [SerializeField] private UIGrayscaleImage _imageIcon;
        [SerializeField] private Image _imageIconBg;
        [SerializeField] private Image _imageNormalBg;
        [SerializeField] private Image _imageLevelBg;
        [SerializeField] private Image _imageQuality;
        [SerializeField] private Image _imageLockBg;
        [SerializeField] private TextMeshProUGUI _textLevel;
        [SerializeField] private TextMeshProUGUI _textUnlockDescription;
        [SerializeField] private GameObject _transferIcon;

        [SerializeField] private UIGrayscaleImage _imageAttributeBg;
        [SerializeField] private UIGrayscaleImage _imageAttributeIcon;
        [SerializeField] private UIGrayscaleImage _heroCoreNode;
        [SerializeField] private GameObject _nodeLocked;
        
        [SerializeField] private Material _quelity6Material;
        [SerializeField] private GameObject _quelity6FilowNode;

        [SerializeField]
        private LuaBehaviour[] _luaScripts;

        private EventSubscriber _eventSubscriber;

        public int HeroId { get; private set; }
        public int Level { get; private set; }
        public bool UnlockDetail { get; private set; } = false;

        private HeroConfig _heroConfig;

        private void Start()
        {
            _eventSubscriber = new EventSubscriber();
            _eventSubscriber.Subscribe<HeroUpdateEvent>(OnHeroUpdate);
            _eventSubscriber.Subscribe<HeroSkinUpdate>(OnHeroSkinUpdate);
            _eventSubscriber.Subscribe<HeroUpgradeEvent>(OnHeroUpgrade);
        }

        public void RefreshUI(int heroId, bool unlockDetail = false){
            DB.Hero.Contents.TryGetValue(heroId, out var level);
            RefreshUI(heroId, level, unlockDetail);
        }

        public void RefreshUI(int heroId, int level, bool unlockDetail = false)
        {
            HeroId = heroId;
            Level = level;
            UnlockDetail = unlockDetail;
            _heroConfig = ConfigManager.Instance.GetConfig<HeroConfigTable>().GetRowData(heroId);
            SetUI();
        }

        private void SetUI()
        {
            SetHeroIcon();
            SetAttributeDisplay();
            SetTransferAndCoreIcons();

            var state = GetCurrentHeroState();
            SetUIByState(state);

            SetBackground();

            foreach (var luaScript in _luaScripts)
            {
                var action = luaScript.ScriptEnv.Get<Action<int, int>>(LuaSetUI);
                action?.Invoke(HeroId, Level);
            }
        }

        public State GetCurrentHeroState()
        {
            if (Level >= 1)
                return State.Summoned;

            if (HeroManager.Instance.IsHeroLocked(_heroConfig) &&
                BagManager.Instance.GetItemCount(_heroConfig.LevelUpItemId) == 0)
                return State.Locked;

            return State.Unlocked;
        }

        private void SetUIByState(State state)
        {
            switch (state)
            {
                case State.Locked:
                    SetLockedStateUI();
                    break;
                case State.Unlocked:
                    SetUnlockedStateUI();
                    break;
                case State.Summoned:
                    SetSummonedStateUI();
                    break;
            }
        }

        private void SetHeroIcon()
        {
            _imageIcon.sprite = ConfigManager.Instance.HeroGradeRef.GetHeroSpriteByLevel(HeroId, Level);
        }

        private void SetAttributeDisplay()
        {
            _imageAttributeIcon.sprite = AssetRef
                .Parse($"res/heroexres/attributes/attribute_{_heroConfig.AttackAttribute}")
                .Load<Sprite>();
        }

        private void SetTransferAndCoreIcons()
        {
            _transferIcon.SetActive(ConfigManager.Instance.HeroDemonKingRef.ExistTransferHero(HeroId));
            _heroCoreNode.gameObject.SetActive(_heroConfig.CoreSkills.Count > 0);
            _heroCoreNode.Grayscale = Level <= 0;
        }

        private void SetSummonedStateUI()
        {
            _imageAttributeIcon.Grayscale = false;
            _imageAttributeBg.Grayscale = false;
            _imageIcon.Grayscale = false;

            _textUnlockDescription.gameObject.SetActive(false);
            _imageLevelBg.gameObject.SetActive(true);
            _imageLockBg.gameObject.SetActive(false);
            _nodeLocked.SetActive(false);

            SetHeroQualityDisplay();
            SetHeroLevelDisplay();
            SetLevelProgressDisplay();
        }

        private void SetHeroQualityDisplay()
        {
            var heroQualityConfig = ConfigManager.Instance.HeroQualityRef[_heroConfig.Id];
            if (heroQualityConfig == null) return;

            _imageAttributeBg.sprite = heroQualityConfig.AttributeBg.Load<Sprite>();
            _imageIconBg.sprite = heroQualityConfig.IconBg.Load<Sprite>();
            //品质6特殊显示
            //_imageIconBg.material = _heroConfig.Quality == 6 ? _quelity6Material : null;
            _quelity6FilowNode.SetActive(_heroConfig.Quality == 6);
            _imageLevelBg.sprite = heroQualityConfig.LevelBg.Load<Sprite>();

            if (heroQualityConfig.Quality != null)
            {
                _imageQuality.gameObject.SetActive(true);
                _imageQuality.sprite = _heroConfig.QualityPlus > 0
                    ? heroQualityConfig.QualityPlus.Load<Sprite>()
                    : heroQualityConfig.Quality.Load<Sprite>();
                _imageQuality.SetNativeSize();
            }
            else
            {
                _imageQuality.gameObject.SetActive(false);
            }
        }

        private void SetHeroLevelDisplay()
        {
            _textLevel.SetLang("hero_level_text", Level);
        }

        private void SetLevelProgressDisplay()
        {
            var heroMaxLevel = ConfigManager.Instance.HeroLevelRef.GetHeroMaxLevel(HeroId);
            var levelConfig = Level > heroMaxLevel ? null : ConfigManager.Instance.HeroLevelRef[HeroId, Level];

            if (Level < heroMaxLevel && levelConfig != null && levelConfig.Costs.Count > 0)
            {
                // 进度显示逻辑已移除
            }
            else
            {
                SetMaxLevelDisplay();
            }
        }

        private void SetMaxLevelDisplay()
        {
            _textLevel.SetLang("Heros_MaxLevel");
        }

        private void SetLockedStateUI()
        {
            _imageAttributeIcon.Grayscale = true;
            _imageAttributeBg.Grayscale = true;
            _imageIcon.Grayscale = true;

            _imageAttributeBg.sprite = _heroBoxGrayAttributesBox;
            _imageIconBg.sprite = _unlockIconBackground;
            // _imageIconBg.material = null;
            _imageLevelBg.sprite = _heroBoxGrayLevelBox;

            var heroQualityConfig = ConfigManager.Instance.HeroQualityRef[_heroConfig.Id];
            if (heroQualityConfig != null && _imageLockBg != null)
            {
                _imageLockBg.sprite = heroQualityConfig.LockBg.Load<Sprite>();
            }

            _nodeLocked.SetActive(true);
            _imageLockBg.gameObject.SetActive(false);
            _imageQuality.gameObject.SetActive(false);

            SetUnlockConditionText();
            _textUnlockDescription.gameObject.SetActive(UnlockDetail);
            _imageLevelBg.gameObject.SetActive(!UnlockDetail);
            _textLevel.SetLang("Locked_1");
        }

        private void SetUnlockedStateUI()
        {
            _imageAttributeIcon.Grayscale = true;
            _imageAttributeBg.Grayscale = true;
            _imageIcon.Grayscale = true;

            _imageAttributeBg.sprite = _heroBoxGrayAttributesBox;
            _imageIconBg.sprite = _unlockIconBackground;
            // _imageIconBg.material = null;
            _imageLevelBg.sprite = _heroBoxGrayLevelBox;

            var heroQualityConfig = ConfigManager.Instance.HeroQualityRef[_heroConfig.Id];
            if (heroQualityConfig != null && _imageLockBg != null)
            {
                _imageLockBg.sprite = heroQualityConfig.LockBg.Load<Sprite>();
            }

            _textUnlockDescription.gameObject.SetActive(false);
            _imageLevelBg.gameObject.SetActive(true);
            _imageLockBg.gameObject.SetActive(true);
            _nodeLocked.SetActive(false);
            _imageQuality.gameObject.SetActive(false);

            _textLevel.SetLang(CanSummonHero() ? "HeroItem_Btn_Summon" : "Unlocked_1");
        }

        public bool CanSummonHero()
        {
            var count = BagManager.Instance.GetItemCount(_heroConfig.LevelUpItemId);
            return count >= _heroConfig.UnlockCount;
        }

        private void SetUnlockConditionText()
        {
            foreach (var unlock in _heroConfig.UnlockConditions)
            {
                switch (unlock.Key)
                {
                    case (int)HeroUnlockCondition.PassChapter:
                        _textUnlockDescription.SetLang("Chapter_Unlock_1", unlock.Value);
                        break;
                    case (int)HeroUnlockCondition.Event:
                        var itemConfig = ConfigManager.Instance.GetConfig<ItemConfigTable>().GetRowData(HeroId);
                        _textUnlockDescription.SetLang(itemConfig.GetSource);
                        break;
                    case (int)HeroUnlockCondition.InComing:
                        _textUnlockDescription.SetLang("Look_Forward");
                        break;
                }
            }
        }

        private void SetBackground()
        {
            if (Level == 0)
            {
                _imageNormalBg.sprite = _unlockBackground;
            }
            else
            {
                var heroQualityConfig = ConfigManager.Instance.HeroQualityRef[_heroConfig.Id];
                if (heroQualityConfig == null) return;
                var sprite = heroQualityConfig.Background.Load<Sprite>();
                _imageNormalBg.sprite = sprite;
            }
        }

        private void OnDestroy()
        {
            _eventSubscriber?.Dispose();
        }

        private void OnHeroUpdate(HeroUpdateEvent evt)
        {
            if (evt.HeroId == HeroId)
            {
                RefreshUI(HeroId, evt.Level, UnlockDetail);
            }
        }

        private void OnHeroUpgrade(HeroUpgradeEvent evt)
        {
            if (evt.HeroId == HeroId)
            {
                RefreshUI(HeroId, evt.Level, UnlockDetail);
            }
        }

        private void OnHeroSkinUpdate(HeroSkinUpdate evt)
        {
            if (evt.HeroId == HeroId)
            {
                SetHeroIcon();
            }
        }
    }
}
