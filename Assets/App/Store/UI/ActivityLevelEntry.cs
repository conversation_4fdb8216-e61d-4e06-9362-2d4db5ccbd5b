using System.Collections.Generic;
using System.Linq;
using App.Activity.ActivityChapter;
using App.Config;
using App.GamePlay;
using App.UI.Energy;
using DG.Tweening;
using FQDev.UI.Layer;
using HttpLibrary.Response;
using Language;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Button = FQDev.UI.Button;
using App.Bag;
using App.UI.Utility;

namespace App.UI
{
    public class ActivityLevelEntry : LayerContent
    {
        [SerializeField] private Button _buttonClose;
        [SerializeField] private Button _buttonStart;
        [SerializeField] private Button _buttonClear;
        [SerializeField] private Image _imageLeft;
        [SerializeField] private Image _imageRight;
        [SerializeField] private TextMeshProUGUI _textTitle;
        [SerializeField] private Button _buttonLeft;
        [SerializeField] private UIGrayscaleImage _imageStartButton;
        [SerializeField] private Button _buttonRight;
        [SerializeField] private UIGrayscaleImage _imageWipeButton;
        [SerializeField] private TextMeshProUGUI _textLevel;
        [SerializeField] private RectTransform _nodeItems;
        [SerializeField] private TextMeshProUGUI _textLeftCount;
        [SerializeField] private Image _imageBG;
        [SerializeField] private Image _imageTitleBG;
        [SerializeField] private GridLayoutGroup _gridLayoutGroup;
        [SerializeField] private CommonGiftItem _prefabItem;
        [SerializeField] private CommonGiftItem _prefabItemPass;
        [SerializeField] private TextMeshProUGUI _textStartSpend;
        [SerializeField] private TextMeshProUGUI _textClearSpend;
        [SerializeField] private GameObject _nodeStartSpendRes;
        [SerializeField] private GameObject _nodeClearSpendRes;
        
        [SerializeField] private RectTransform _passReward;
        [SerializeField] private RectTransform _passRewardBG;
        [SerializeField] private RectTransform _passRewardContent;
        [SerializeField] private RectTransform _passTitleBG;

        [SerializeField] private GameObject _buttonTextNode;
        [SerializeField] private GameObject _TicketNode;
        [SerializeField] private TextMeshProUGUI _TicketCount;
        [SerializeField] private Image _TicketIcon;
        [SerializeField] private TextMeshProUGUI _wipeTicketCount;
        [SerializeField] private Image _wipeTicketIcon;
        [SerializeField] private GameObject _wipeTicketNode;
        
        
        private ActivityChapterConfig _config;
        private ChapterConfig _chapterConfig;
        private int _currentIndex;
        private ActivityChapterData _data;
        
        
        
        private void Awake()
        {
            _buttonClose.AddClick(ClickButton);
            _buttonLeft.AddClick(ClickLeft);
            _buttonRight.AddClick(ClickRight);
            _buttonStart.AddClick(ClickStart);
            _buttonClear.AddClick(ClickClear);
        }
        public void Initialize(ActivityChapterConfig config)
        {
            _config = config;
            _textTitle.SetLang(_config.Name);
            _imageBG.sprite = _config.EntryBg?.Load<Sprite>();
            _imageTitleBG.sprite = _config.EntryTitleBg?.Load<Sprite>();
            _currentIndex = 0;
            _data = ActivityChapterManager.Instance.GetActivityData(_config.Id);
            _currentIndex = _data.maxLv;
            ItemConfig itemConfig = BagManager.Instance.GetItemInfo(_config.TicketId);
            _TicketIcon.sprite = itemConfig.Icon.Load<Sprite>();
          //  varconfig.PassAward
            UpdateIndex();
            
            LogEvent.I.Track("ActivityMode_Detail_Open")
                .IsNew().AddMaxChapter()
                .Add("Activity_ID", _config.Id)
                .Send();
        }
        private void ClickButton()
        {
            Close();
        }
        private void ClickLeft()
        {
            _currentIndex--;
            UpdateIndex();
        }
        private void ClickRight()
        {
            _currentIndex++;
            UpdateIndex();
        }
        private void ClickStart()
        { 
            AudioManager.Instance.PlaySound("Main_Click");
            
            if (!CheckStartCondition())
                return;

            if (!ActivityChapterManager.Instance.InActivityTime(_config.Id))
            {
                MessageTip.Create("Activiti_Is_Over");
                return;
            }
            
            ActivityChapterManager.Instance.StartLevel(_config.Id, _currentIndex);
            
            GameManager.Instance.SelfGame.PassReward = _passReward.gameObject.activeInHierarchy;
            
            Close();
        }
        
        private void ClickClear()
        {
            AudioManager.Instance.PlaySound("Main_Click");
            if (!EnergyManager.Instance.IsActivityLevelEnough(_data.id))
            {
                EnergyPurchaseLayer.Create();
                return;
            }
   
            ActivityChapterManager.Instance.MopUpActivity(_config.Id,_currentIndex, CBClear);
        }
        
        private bool CheckStartCondition()
        {
            var leftCount = ActivityChapterManager.Instance.GetActivityRemainingTimes(_data.id);
            Debug.Log(leftCount);
            if (leftCount > 0)
            {
                foreach (var cost in _config.Spend)
                {
                    if (BagManager.Instance.CheckItemEnough(cost.Key, cost.Value)) continue;
                    if (cost.Key == (int) Item.Energy)
                    {
                        EnergyPurchaseLayer.Create();
                    }
                    return false;
                }

                return true;
            }

            int ticket = BagManager.Instance.GetItemCount(_config.TicketId);
            if (ticket <= 0)
            {
                return false;
            }

            return true;
        }

        private void UpdateButtonInfo()
        {
            var leftCount = ActivityChapterManager.Instance.GetActivityRemainingTimes(_data.id);
            if (leftCount <= 0)
            {
                _buttonTextNode.SetActive(false);
                _TicketNode.SetActive(true);

                int ticket = BagManager.Instance.GetItemCount(_config.TicketId);
                if (ticket > 0)
                {
                    _TicketCount.text = $"{ticket}/1";
                    _imageStartButton.Amount = 0;
                    _buttonStart.interactable = true;
                }
                else
                {
                    _buttonTextNode.SetActive(true);
                    _TicketNode.SetActive(false);
                    _imageStartButton.Amount = 1;
                    _buttonStart.interactable = false;
                }
            }
            else
            {
                _buttonTextNode.SetActive(true);
                _TicketNode.SetActive(false);
                _imageStartButton.Amount = 0;
                _buttonStart.interactable = true;
            }
        }
        
        private void UpdateIndex()
        {
            var remainTime = ActivityChapterManager.Instance.GetActivityRemainingTimes(_data.id);
            var ActivityConfig = ConfigManager.Instance.GetConfig<ActivityChapterConfigTable>().GetRowData(_config.Id);
            var levelCount = ActivityConfig.ChapterIds;
            if (_currentIndex >= _data.maxLv)
            {
              //  _currentIndex--;
                _imageRight.DOFade(0.5f , 0);
                _buttonRight.interactable = false;
            }
            else
            {
                _imageRight.DOFade(1 , 0);
                _buttonRight.interactable = true;
            }

            if (_currentIndex >= levelCount.Count )
            {
                _currentIndex--;
                _imageRight.DOFade(0.5f , 0);
                _buttonRight.interactable = false;
            }

            int ticket = BagManager.Instance.GetItemCount(_config.TicketId);
            if (remainTime <= 0 && ticket > 0)
            {
                _imageWipeButton.Amount = 0;
                _buttonClear.interactable = true;
                ItemConfig itemConfig = BagManager.Instance.GetItemInfo(_config.TicketId);
                _wipeTicketIcon.sprite = itemConfig.Icon.Load<Sprite>();
                _wipeTicketCount.text = $"{ticket}/1";
                _wipeTicketNode.SetActive(true);
                _nodeClearSpendRes.SetActive(false);
            }
            else
            {     
                ItemConfig energyConfig = BagManager.Instance.GetItemInfo(2);
                _wipeTicketIcon.sprite = energyConfig.Icon.Load<Sprite>();
                _nodeClearSpendRes.SetActive(true);
                _wipeTicketNode.SetActive(false);
                if (remainTime > 0)
                {
                    _imageWipeButton.Amount = 0;
                    _buttonClear.interactable = true;
                }
                else
                {
                    _imageWipeButton.Amount = 1;
                    _buttonClear.interactable = false;
                }
            }

            _textLevel.text = (_currentIndex + 1).ToString();
            _imageLeft.DOFade(_currentIndex <= 0 ? 0.5f : 1f, 0);
            _buttonLeft.interactable = _currentIndex > 0;
            //按钮部分               
            _buttonClear.gameObject.SetActive(_currentIndex <= _data.maxLv - 1 );
            _gridLayoutGroup.cellSize = new Vector2(_currentIndex <= _data.maxLv - 1 ? 399 : 460 , 174);
            //这是宝箱部分
            //清空
            for (int i = 0; i < _nodeItems.childCount; i++)
            {
                Destroy(_nodeItems.GetChild(i).gameObject);
            }

            var itemCount = 0;
            var items = ActivityChapterManager.Instance.GetActivityReward(_config.Id,_currentIndex);
            foreach (var item in items)
            {
                if(item.Count == 0)
                    continue;
                var commonGiftItem = Instantiate(_prefabItem, _nodeItems);
                commonGiftItem.gameObject.SetActive(true);
                commonGiftItem.FreshRewardUI(item);
                itemCount++;
            }
            //这是按钮部分
            var config = ConfigManager.Instance.GetConfig<ActivityChapterConfigTable>().GetRowData(_config.Id);
            var spend = 0;
            foreach (var kv in  config.Spend)
            {
                spend = kv.Value;
                break;
            }
            _textStartSpend.text = "-" + spend;
            _textClearSpend.text = "-" + spend;

            // _nodeStartSpendRes.SetActive(spend != 0);
            // _nodeClearSpendRes.SetActive(spend != 0);
            _textLeftCount.SetLangBy(remainTime);
            // if (remainTime == 0)
            // {
            //     Close();
            // }
            
            //首通
            //扫荡
            //清空
            for (int i = 0; i < _passRewardContent.childCount; i++)
            {
                Destroy(_passRewardContent.GetChild(i).gameObject);
            }

            var passItemCount = 0;
            float passRewardTimes = 0;
            try
            {
                 passRewardTimes = config.Times[_currentIndex];
            }
            catch
            {
                passRewardTimes = 1;
            }
            foreach (var item in items)
            {
                if(item.Count == 0)
                    continue;
                RewardItem rewardItem = new RewardItem();
                rewardItem.Id = item.Id;
                rewardItem.Type = item.Type;
                rewardItem.RangeMax = item.RangeMax;
                rewardItem.Count = Mathf.FloorToInt(item.Count * passRewardTimes);
                var commonGiftItem = Instantiate(_prefabItemPass, _passRewardContent);
                commonGiftItem.gameObject.SetActive(true);
                commonGiftItem.FreshRewardUI(rewardItem);
                passItemCount++;
            }
            //适配大小
            var layerGroup = _passRewardContent.GetComponent<GridLayoutGroup>();
            //每一个格子的大小
            var widthX = layerGroup.cellSize.x ;
            //间隔
            var spaceX = layerGroup.spacing.x;
            //左边距
            var padding = layerGroup.padding.left;
            //数量
            var count = passItemCount;
            
            _passRewardBG.sizeDelta = new Vector2(  count* widthX +  
                                                    (count - 1) * spaceX +
                                                    2 * padding, _passRewardBG.sizeDelta.y);

            _passReward.sizeDelta = _passRewardBG.sizeDelta;
            
            _passReward.SetActive(!_buttonClear.gameObject.activeInHierarchy);
            
            var itemlayerGroup = _nodeItems.GetComponent<GridLayoutGroup>();
            itemlayerGroup.childAlignment = _passReward.gameObject.activeInHierarchy
                ? TextAnchor.UpperLeft
                : TextAnchor.UpperCenter;
            itemlayerGroup.padding = _passReward.gameObject.activeInHierarchy
                ? new RectOffset(25,itemlayerGroup.padding.right,itemlayerGroup.padding.top,itemlayerGroup.padding.bottom)
                : new RectOffset(0,itemlayerGroup.padding.right,itemlayerGroup.padding.top,itemlayerGroup.padding.bottom);
            
            var layerGroupItem = _nodeItems.GetComponent<GridLayoutGroup>();
            //每一个格子的大小
            var widthXItem = layerGroupItem.cellSize.x ;
            //间隔
            var spaceXItem = layerGroupItem.spacing.x;
            //左边距
            var paddingItem = layerGroupItem.padding.left;
            //数量
            var countItem = itemCount;

            _nodeItems.sizeDelta =  new Vector2(countItem * widthXItem +  
                                                (countItem - 1) * spaceXItem +
                                                2 * paddingItem, _nodeItems.sizeDelta.y);
            if (passItemCount == 1)
            {
                _passTitleBG.offsetMax = new Vector2(15, _passTitleBG.offsetMax.y);
                _passTitleBG.offsetMin = new Vector2(-15, _passTitleBG.offsetMin.y);
            }
            UpdateButtonInfo();
        }
        private void CBClear(CommonResponseDataHandle.UpdateDataResponseData data)
        {
            var dialog = RewardDialog.Create();
            dialog.Initialize(data);
            UpdateIndex();
        }
        
        /*
        private List<RewardItem> GetParseReward(string rewardString)
        {
            List<RewardItem> listRewardItems = new();
            var rewards = rewardString.Split('_').ToList();
            foreach (var reward in rewards)
            {
            
                listRewardItems.Add(RewardItem.Parse(reward));
            }
            return listRewardItems;
        }
        */
        
        public static ActivityLevelEntry Create(ActivityChapterConfig config)
        {
            var layer = LayerManager.Instance.LoadContent(LayerTag.Dialog, "ui/home_levelpanel/ActivityLevelEntry");
            var dialog = layer.GetComponent<ActivityLevelEntry>();
            dialog.Initialize(config);
            return dialog;
        }
        
        
    }
}