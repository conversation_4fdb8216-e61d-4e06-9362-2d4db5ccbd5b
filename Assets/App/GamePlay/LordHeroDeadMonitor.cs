using App.GamePlay.Behaviors;
using BehaviorDesigner.Runtime.Tasks;
using FQDev.EventSystem;
using UnityEngine;

namespace App.GamePlay
{
    public class LordHeroDeadMonitor : MonoBehaviour
    {
        public const string EventName = "OnHeroDead";
        private EventSubscriber _eventSubscriber;

        private Lord _lord;
        
        [SerializeField]
        private GameFlag _targetGame = GameFlag.Self;
        
        private void Start()
        {
            if (_lord == null)
                _lord = GetComponent<Lord>();
            _eventSubscriber = new EventSubscriber();
            var game = BehaviorGameUtility.GetGame(_targetGame, _lord.Game);
            _eventSubscriber.Subscribe<FightElementDeadEvent>(OnHeroDead, game);
        }

        private void OnDestroy()
        {
            _eventSubscriber.Dispose();
        }
        
        private void OnHeroDead(FightElementDeadEvent evt)
        {
            if(evt.FightElement is not Hero hero)
                return;
            
            SendBehaviorEvent(EventName, hero);
        }

        private void SendBehaviorEvent(string behaviorEvent, object arg1 = null)
        {
            _lord?.SendBehaviorEvent(behaviorEvent, arg1);
        }
    }
}