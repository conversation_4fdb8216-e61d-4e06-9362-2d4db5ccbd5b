using System.Collections.Generic;
using System.Linq;
using App.Config;
using App.GamePlay.AI;
using App.GamePlay.Buff;
using App.GamePlay.Tiles;
using App.GamePlay.Tiles.Module;
using App.GamePlay.UI;
using App.Hero;
using App.Pool;
using FQDev.AssetBundles;
using FQDev.EventSystem;
using Unity.Mathematics;
using UnityEngine;

namespace App.GamePlay
{
    public class Hero :
        FightElement,
        IAttacker,
        IEnemyBuffProvider,
        IHeroBuffProvider,
        IGameBuffProvider,
        IDamageable
    {
        private HeroConfig _heroConfig;
        internal HeroConfig HeroConfig => _heroConfig;
        private HeroGradeConfig _gradeConfig;
        internal HeroGradeConfig GradeConfig => _gradeConfig;
        private HeroLevelConfig _levelConfig;
        internal HeroLevelConfig LevelConfig => _levelConfig;

        private TileItem _tileItem;
        public TileItem TileItem => _tileItem;

        internal int Grade => _gradeConfig?.Grade ?? 0;

        public override int ID => _heroConfig?.Id ?? 0;

        public override int HashID
        {
            get
            {
                if (_heroConfig == null || _tileItem == null)
                    return 0;
                return (_heroConfig.Id << 8) ^ _tileItem.Index.GetHashCode();
            }
        }

        public AttackAttributeType AttackAttributeType =>
            _heroConfig != null ? (AttackAttributeType)_heroConfig.AttackAttribute : AttackAttributeType.Physics;

        public float AttackDamageBoostRate => BuffSet.GetAttribute(HeroAttributeField.AttackDamageAddedRatio)
                                              - BuffSet.GetAttribute(HeroAttributeField.AttackDamageReducedRatio);
        
        public float AttackPenetrateRatio => BuffSet.GetAttribute(HeroAttributeField.AttackPenetrateRatio);

        public float HitRate => 1 * (1 + BuffSet.GetAttribute(HeroAttributeField.HitRateAddedRatio));
        public int AttackerID => ID;
        public int AttackerHashID => HashID;

        internal SpriteRenderer TileSprite => _tileItem?.GetComponent<IRenderer>()?.SpriteRenderer;

        internal Vector2Int TileIndex => _tileItem?.Index ?? Vector2Int.zero;

        internal float AttackInterval => _levelConfig?.AttackInterval ?? 0.001f;

        internal HeroBuffSet BuffSet { get; private set; }

        
        public IEnumerable<HeroBehaviorSkill> BehaviorSkills => _behaviorSkills;
        private HeroBehaviorSkill[] _behaviorSkills;
        
        private IDamageListener[] _damageListeners;

        public GameObject Owner => gameObject;

        public override bool Dead => Hp <= 0;

        public int Hp { get; internal set; }
        public int MaxHp { get; internal set; }
        private HeroHpProgress _hpProgress;
        public int ReviveCount { get; private set; }

        private int _autoReviveCount;
        public int AutoReviveCount => _autoReviveCount;
        private int _deadCount;
        private const float CommonBuffDuration = 5f;

        private void Awake()
        {
            _behaviorSkills = GetComponents<HeroBehaviorSkill>();
            var col = gameObject.GetOrCreateComponent<CircleCollider2D>();
            col.radius = 0.4f;
            col.isTrigger = true;
            ColliderRadius = col.radius;
            
            _damageListeners = GetComponents<IDamageListener>();
        }

        internal void Init(
            HeroConfig heroConfig,
            HeroGradeConfig gradeConfig,
            HeroLevelConfig levelConfig,
            TileItem tileItem)
        {
            _heroConfig = heroConfig;
            _gradeConfig = gradeConfig;
            _levelConfig = levelConfig;
            _tileItem = tileItem;
            Game = _tileItem.Game;

            BuffSet = new HeroBuffSet(this);
            var hpRatio = 1f;
            var talentHpRatio = Game.SkillSet.GetHeroFieldValue(ID, (int)SkillField.HeroAddMaxHpRatioField);
            var maxGrade = ConfigManager.Instance.HeroGradeRef.GetMaxGrade(ID);
            if (_gradeConfig.Grade >= maxGrade)
            {
                var topHeroHpRatio = Game.SkillSet.GetGlobalFieldValue(SkillField.TopGradeHeroAddMaxHpRatioField);
                hpRatio += topHeroHpRatio;
            }
            hpRatio += talentHpRatio;
            var globalHpRatio = Game.SkillSet.GetGlobalFieldValue(SkillField.GlobalHeroHpAddedRatio);
            hpRatio += globalHpRatio;
            var globalTransferHeroHpRatio =
                Game.SkillSet.GetGlobalFieldValue(SkillField.GlobalTalentTransferHeroMaxHpAdded);
            var transferCount = Mathf.FloorToInt((Game.Day - 1f) / Game.ChapterConfig.TransferDay);
            globalTransferHeroHpRatio *= transferCount;
            hpRatio += globalTransferHeroHpRatio;

            var propertyHpRatio = Game.PropertyCollectionCenter.GetFieldValueWithAnyFilter(
                PropertyField.HpAddRatio,
                new Dictionary<PropertyFilterType, int>()
                {
                    { PropertyFilterType.HeroId, ID },
                    { PropertyFilterType.AttackAttribute, (int)AttackAttributeType }
                }
            );
            hpRatio += propertyHpRatio;
            
            MaxHp = (int)(_levelConfig.Hp * _gradeConfig.HpRatio * hpRatio);
            Hp = MaxHp;
            AddHpProgress(1);
            ReviveCount = 0;
            _autoReviveCount = 0;
            _deadCount = 0;
            ApplyInitAttributes();
        }

        
        private void ApplyInitAttributes()
        {
            var temporaryCriticalRatio =
                Game.SkillSet.GetTodayEffectiveAttributeValue(TodayEffectiveAttributeType.CriticalRatio);
            if (temporaryCriticalRatio > 0)
                BuffSet.ChangeAttribute(HeroAttributeField.CriticalAddedRatio, temporaryCriticalRatio);
            var temporaryCriticalDamage =
                Game.SkillSet.GetTodayEffectiveAttributeValue(TodayEffectiveAttributeType.CriticalDamage);
            if (temporaryCriticalDamage > 0)
                BuffSet.ChangeAttribute(HeroAttributeField.CriticalDamageAddedRatio, temporaryCriticalDamage);
            var temporaryAttackDamageRatio =
                Game.SkillSet.GetTodayEffectiveAttributeValue(TodayEffectiveAttributeType.AttackDamageRatio);
            if (temporaryAttackDamageRatio > 0)
                BuffSet.ChangeAttribute(HeroAttributeField.AttackDamageAddedRatio, temporaryAttackDamageRatio);
            var temporaryAttackDamageValue =
                Game.SkillSet.GetTodayEffectiveAttributeValue(TodayEffectiveAttributeType.AttackDamageValue);
            if (temporaryAttackDamageValue > 0)
                BuffSet.ChangeAttribute(HeroAttributeField.AttackDamageAddedValue, temporaryAttackDamageValue);
            
            
            var propertyCriticalAddRatio = Game.PropertyCollectionCenter.GetFieldValueWithAnyFilter(
                PropertyField.CriticalAddRatio,
                new Dictionary<PropertyFilterType, int>()
                {
                    { PropertyFilterType.HeroId, ID },
                    { PropertyFilterType.AttackAttribute, (int)AttackAttributeType }
                }
            );
            if (propertyCriticalAddRatio > 0)
            {
                BuffSet.ChangeAttribute(HeroAttributeField.CriticalAddedRatio, propertyCriticalAddRatio);
            }
            
            var propertyCriticalDamageAddRatio = Game.PropertyCollectionCenter.GetFieldValueWithAnyFilter(
                PropertyField.CriticalDamageAddRatio,
                new Dictionary<PropertyFilterType, int>()
                {
                    { PropertyFilterType.HeroId, ID },
                    { PropertyFilterType.AttackAttribute, (int)AttackAttributeType }
                }
            );
            if (propertyCriticalDamageAddRatio > 0)
            {
                BuffSet.ChangeAttribute(HeroAttributeField.CriticalDamageAddedRatio, propertyCriticalDamageAddRatio);
            }

        }

        private void OnDisable()
        {
            _tileItem = null;
        }

        internal void ChangeTile(TileItem tileItem)
        {
            _tileItem = tileItem;
        }

        internal void AddHp(int hp)
        {
            int addValue = (int)(hp *
                                 (1 + BuffSet.GetAttribute(HeroAttributeField.HpRecoveryAddedRatio) -
                                  BuffSet.GetAttribute(HeroAttributeField.HpRecoveryReducedRatio)));
            int targetHp = Hp + addValue;
            Hp = math.min(targetHp, MaxHp);
            if (_hpProgress)
                _hpProgress.SetHpProgress((float)Hp / MaxHp);
        }
        
        internal void ChangeMaxHp(int offsetHp)
        {
            MaxHp += offsetHp;
            
            if (offsetHp > 0)
                Hp += offsetHp;
            
            if (Hp > MaxHp)
                Hp = MaxHp;
            
            if (_hpProgress)
                _hpProgress.SetHpProgress((float)Hp / MaxHp);
        }

        internal void ChangeGrade(int grade)
        {
            var configRef = ConfigManager.Instance.HeroGradeRef;

            //设计上最小2级
            grade = Mathf.Clamp(grade, 2, configRef.GetMaxGrade(_heroConfig.Id));

            LoadHeroSprite(grade);
        }

        internal void LoadHeroSprite(int grade)
        {
            var configRef = ConfigManager.Instance.HeroGradeRef;
            _gradeConfig = configRef[_heroConfig.Id, grade];

            //view
            var skinID = 0;
            if (Game.AppearHeroesSkin == null || Game.AppearHeroesSkin.Count == 0)
                skinID = SkinManager.Instance.GetHeroDefaultSkinID(_heroConfig.Id);
            else
                skinID = Game.AppearHeroesSkin[_heroConfig.Id];

            var skinConfig = SkinManager.Instance.GetSkinConfig(skinID);
            TileSprite.sprite = AssetRef.Parse($"{skinConfig.ResourceName}_{_gradeConfig.Grade - 1}").Load<Sprite>();
        }

        private void AddHpProgress(float progress)
        {
            if (_hpProgress == null)
            {
                var prefab = AssetRef.Parse("ui/elementhpbar/HeroHpProgress").Load<GameObject>();
                _hpProgress = Instantiate(prefab, transform).GetComponent<HeroHpProgress>();
            }
            _hpProgress.Init(this);
            _hpProgress.SetHpProgress(progress);
        }

        public void Clear()
        {
            BuffSet.Clear();
        }

        private void Update()
        {
            if (Game.Pause)
                return;

            if (Dead)
                return;

            BuffSet.Update(Time.deltaTime);
        }

        internal void SendBehaviorEvent(BehaviorEvents behaviorEvent, object arg1 = null, object arg2 = null,
            object arg3 = null)
        {
            foreach (var skill in _behaviorSkills)
            {
                skill.Behavior.SendEvent(behaviorEvent.ToString(), arg1, arg2, arg3);
            }
        }

        internal float GetFinalAttackDamage(float coefficient = 1)
        {
            var baseAttackDamage = _levelConfig.AttackDamage * _gradeConfig.AttackRatio * coefficient;
            return Mathf.Max(baseAttackDamage + BuffSet.GetAttribute(HeroAttributeField.AttackDamageAddedValue)
                             - BuffSet.GetAttribute(HeroAttributeField.AttackDamageReducedValue), 0);
        }

        internal float GetBaseAttackDamage(float coefficient = 1)
        {
            var baseAttackDamage = _levelConfig.AttackDamage * _gradeConfig.AttackRatio * coefficient;
            return baseAttackDamage;
        }

        public void OnCausedDamage(int damage)
        {
            Game.LogDamage(_heroConfig.Id, damage);
        }

        public void OnCausedDeath(FightElement target)
        {
            if (target is Enemy)
                SendBehaviorEvent(BehaviorEvents.EnemyDead, (Vector2)target.transform.position, (int)target.ID);
            else
            {
                SendBehaviorEvent(BehaviorEvents.HeroDead, (Vector2)target.transform.position, (int)target.ID);
            }
        }

        public int GetEnemyBuffCasterID(EnemyBuffConfig config)
        {
            return config.UseHashId ? HashID : ID;
        }

        public int GetHeroBuffCasterID()
        {
            return ID;
        }
        
        public int GetGameBuffCasterID()
        {
            return ID;
        }

        private float GetDamageCoefficient(float originCoefficient, IAttacker attacker)
        {
            var finalCoefficient = originCoefficient;

            return finalCoefficient;
        }


        public DamageShowType Damage(AttackContext context)
        {
            if (Dead || !gameObject.activeInHierarchy)
                return context.DamageType;

            if (BuffSet.GetBuff(HeroBuffType.Invincible) is { Count: > 0})
            {
                return context.DamageType | DamageShowType.Resistance;
            }
            
            if (BuffSet.DoShield())
            {
                return context.DamageType | DamageShowType.Resistance;
            }

            var damage = 0;
            var originDamage = context.Damage;
            var damageType = context.DamageType;
            var constConfig = ConfigManager.Instance.GetConfig<GamePlayConst>();
            if (damageType == DamageShowType.Kill)
            {
                damage = Hp;
            }
            else
            {
                
                var coefficient = GetDamageCoefficient(
                    context.Coefficient,
                    context.Attacker);

                var vulnerableBuffs = BuffSet.GetBuffsByType(HeroBuffType.Vulnerable);
                foreach (var buff in vulnerableBuffs)
                {
                    if (buff is HeroAttributeBuff vulnerableBuff)
                        coefficient += vulnerableBuff.BuffData.Value;
                }

                damage = Mathf.CeilToInt(originDamage * coefficient);

                var shieldHpBuff = BuffSet.GetBuff(HeroBuffType.ShieldHp);
                if (shieldHpBuff is { Count: > 0 })
                {
                    foreach (var buff in shieldHpBuff.Cast<IHpShieldBuff>())
                    {
                        damage = buff.Damage(damage);
                        if (damage > 0) continue;
                        damage = 0;
                        break;
                    }
                }

                var damageReductionRadio = 0f;

                var globalDamageReductionByLostHp =
                    Game.SkillSet.GetGlobalFieldValue(SkillField.GlobalTalentDamageReductionByLostHp);
                if (globalDamageReductionByLostHp > 0)
                {
                    var lostHp = MaxHp - Hp;
                    var lostHpRatio = (float)lostHp / MaxHp * 100;
                    var reduction = Mathf.Min(lostHpRatio * globalDamageReductionByLostHp, 0.3f);
                    damageReductionRadio += reduction;
                }

                damageReductionRadio += BuffSet.GetAttribute(HeroAttributeField.DamageDefenseRatio);

                damage -= (int)(Mathf.Min(damageReductionRadio, constConfig.HeroMaxDefenseRatio) * damage);
            }

            if (damage > 0)
            {
                Hp -= damage;
                if (_hpProgress)
                    _hpProgress.SetHpProgress((float)Hp / MaxHp);
                context.Attacker.OnCausedDamage(damage);
                
                if(_damageListeners != null)
                {
                    foreach (var listener in _damageListeners)
                    {
                        listener.OnCausedDamage(context, damage);
                    }
                }
            }

            GameManager.Instance.DamageShowSystem.AddDamage(
                damage,
                (Vector2)transform.position,
                context.DamageColor,
                damageType);

            if (Dead)
            {
                ++_deadCount;
                var recoverRatio =
                    Game.SkillSet.GetGlobalFieldValue(SkillField.GlobalTalentHeroRestoreHpOnFirstImminentDeath);
                if (_deadCount == 1 && recoverRatio > 0)
                {
                    Hp = 0;
                    RecoverHpRatio(recoverRatio);
                    return damageType;
                }
                
                

                Die(context.Attacker);
            }
            else
            {
                var count = constConfig.GlobalTalentQuadMergeRestoreHeroHpCount;
                if (Game.TodayQuadMergeCount > count)
                {
                    var recoverRatio =
                        Game.SkillSet.GetGlobalFieldValue(SkillField.GlobalTalentQuadMatchExtraRecoverHpValue);
                    RecoverHpRatio(recoverRatio);
                }
            }

            return damageType;
        }

        private void RecoverHpRatio(float ratio)
        {
            var addValue = Mathf.FloorToInt(MaxHp * ratio);

            addValue = (int)(addValue *
                             (1 + BuffSet.GetAttribute(HeroAttributeField.HpRecoveryAddedRatio) -
                              BuffSet.GetAttribute(HeroAttributeField.HpRecoveryReducedRatio)));

            Hp += addValue;
            Hp = Hp > MaxHp ? MaxHp : Hp;
            if (_hpProgress)
                _hpProgress.SetHpProgress((float)Hp / MaxHp);
            PlayHpRecoveryEffect();
        }

        public void PlayHpRecoveryEffect()
        {
            var prefab = AssetBundleManager.Instance.LoadAsset<GameObject>("mheffect/prefabs/vfx_hero_addblood_eff",
                "vfx_hero_addblood_eff");
            var effect = GameManager.Instance.GameObjectPool.Get(prefab);
            effect.transform.position = gameObject.transform.position;
            var pooledObject = effect.GetComponent<PooledObject>();
            pooledObject.Method = PooledObject.RecycleMethod.Time;
            pooledObject.Duration = 1f;
            effect.SetActive(true);
        }

        public void RefreshHpProgress()
        {
            if (_hpProgress)
                _hpProgress.SetHpProgress((float)Hp / MaxHp);
        }

        internal void Die(IAttacker killer)
        {
            Hp = 0;
            foreach (var ai in GetComponents<IHeroAI>())
            {
                ai.EndFight();
            }
            TileSprite.sortingOrder = 0;
            
            BuffSet?.OnDie();
            Clear();
            var viewConst = ConfigManager.Instance.GetConfig<GameViewConst>();
            TileSprite.sprite = viewConst.HeroDieEffect.Load<Sprite>();

            Game.DispatchEvent(
                Witness<FightElementDeadEvent>._,
                this,
                killer);

            //只给杀了他的人发
            killer?.OnCausedDeath(this);

            if (_autoReviveCount > 0)
            {
                Revive();
                --_autoReviveCount;
            }
        }

        internal void AddAutoReviveCount(int count = 1)
        {
            _autoReviveCount += count;
        }

        internal void Revive(int hp = 0)
        {
            if (!Dead)
                return;

            if (hp <= 0)
                hp = MaxHp;

            Hp = Mathf.Min(hp, MaxHp);
            LoadHeroSprite(Grade);
            foreach (var ai in GetComponents<IHeroAI>())
            {
                ai.StartFight(this);
            }

            var damageAddedRatio =
                Game.SkillSet.GetGlobalFieldValue(SkillField.GlobalTalentDamageAddedOnHeroResurrection);
            if (damageAddedRatio > 0)
            {
                AddAttributeBuff(
                    HeroAttributeField.AttackDamageAddedRatio,
                    HeroBuffType.AttackGain,
                    damageAddedRatio,
                    CommonBuffDuration);
            }

            var attackSpeedAddedRatio =
                Game.SkillSet.GetGlobalFieldValue(SkillField.GlobalTalentAttackSpeedAddedOnHeroResurrection);
            if (attackSpeedAddedRatio > 0)
            {
                AddAttributeBuff(
                    HeroAttributeField.AttackSpeedAddedRatio,
                    HeroBuffType.SpeedGain,
                    attackSpeedAddedRatio,
                    CommonBuffDuration);
            }
            
            ++ReviveCount;
            
            Game.DispatchEvent(
                Witness<FightElementReviveEvent>._,
                this);
        }


        private void AddAttributeBuff(
            HeroAttributeField field,
            HeroBuffType buffType,
            float value,
            float duration)
        {
            var buffData = new HeroAttributeBuff.Data()
            {
                Duration = duration,
                Field = field,
                Value = value,
            };
            var displayData = new HeroBuffDisplayData
            {
                EffectPrefab = null,
                TintColor = Color.white
            };

            BuffSet.AddBuff<HeroAttributeBuff, HeroAttributeBuff.Data>(
                buffData,
                displayData,
                this,
                (int)buffType,
                HeroBuffType.Attribute);
        }

#if ENABLE_GM && UNITY_EDITOR
        private GUIStyle labelStyle1;
        private GUIStyle labelStyle2;

        [XLua.BlackList] public bool ShowGUI;

        [XLua.BlackList]
        public void OnGUI()
        {
            if (!ShowGUI)
                return;

            if (!Camera.main) return;
            if (labelStyle1 == null)
            {
                labelStyle1 = new GUIStyle
                {
                    fontStyle = FontStyle.Bold,
                    fontSize = 26,
                    normal = { textColor = Color.black },
                };
            }

            if (labelStyle2 == null)
            {
                labelStyle2 = new GUIStyle
                {
                    fontStyle = FontStyle.Bold,
                    fontSize = 25,
                    normal = { textColor = Color.white },
                };
            }

            var position = transform.position;
            position = Camera.main.WorldToScreenPoint(position);
            var msg = $"{Hp}/{MaxHp}";
            GUI.Label(new Rect(position.x - 1, Screen.height - position.y - 0.5f, 100, 100), msg, labelStyle1);

            GUI.Label(new Rect(position.x, Screen.height - position.y, 100, 100), msg, labelStyle2);
        }
#endif
    }
    
    public class MyClass
    {
        
    }
}