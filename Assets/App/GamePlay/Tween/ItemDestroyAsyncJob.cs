using System.Collections.Generic;
using System.Threading;
using App.GamePlay.Tiles;
using App.GamePlay.Tiles.Module;
using Cysharp.Threading.Tasks;
using DG.Tweening;

namespace App.GamePlay.Tween
{
    public class ItemDestroyAsyncJob : AsyncJob
    {
        private readonly HashSet<TileItem> _items2Destroy = new HashSet<TileItem>();

        public ItemDestroyAsyncJob(int executionOrder) : base(executionOrder)
        {
        }

        public void AddItem(TileItem item)
        {
            _items2Destroy.Add(item);
        }
        
        public bool CancelItem(TileItem item)
        {
            return _items2Destroy.Remove(item);   
        }

        public override async UniTask ExecuteAsync(CancellationToken cancellationToken = default)
        {
            var sequence = DOTween.Sequence();

            foreach (var item in _items2Destroy)
            {
                var tween = item.GetComponent<IDestroyAsync>();
                if (tween == null)
                    item.Recycle();
                else
                    sequence.Join(tween.GetDestroyTween());
            }
            _items2Destroy.Clear();
            
            await sequence
                .SetEase(Ease.Flash)
                .WithCancellation(cancellationToken);
        }

        internal bool Any()
        {
            return _items2Destroy.Count > 0;
        }
    }
}