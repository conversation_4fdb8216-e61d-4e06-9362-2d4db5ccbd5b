using System.Collections;
using System.Collections.Generic;
using System.Linq;
using App.Bag;
using App.Config;
using App.UI.Home;
using UnityEngine;

namespace App.GamePlay
{
    public static class ChapterUtil
    {
        public static ChapterWaveTable GetChapterWaveTable(int chapterId)
        {
            var chapterConfig = ConfigManager.Instance.GetConfig<ChapterConfigTable>()
                .GetRowData(chapterId);
            return ConfigManager.Instance.LoadTableCSV<ChapterWaveTable>(chapterConfig.ChapterWave);
        }
        
        public static GuildWarChapterWaveConfigTable GetGuildWarChapterWaveTable(string chapterWaveName)
        {
            return ConfigManager.Instance.LoadTableCSV<GuildWarChapterWaveConfigTable>(chapterWaveName);
        }

        public static EliteChapterWaveTable GetEliteChapterWaveTable(int chapterId)
        {
            var chapterConfig = ConfigManager.Instance.GetConfig<ChapterConfigTable>()
                .GetRowData(chapterId);
            return ConfigManager.Instance.LoadTableCSV<EliteChapterWaveTable>(chapterConfig.ChapterWave);
        }

        public static IEnumerable<RewardItem> GetActivityReward(int chapterId)
        {
            var rewards = new List<RewardItem>();

            var waveTable = GetChapterWaveTable(chapterId);

            //items
            var items = new Dictionary<int, int>();
            var cardNumber = 0;
            foreach (var (_, wave) in waveTable.Rows)
            {
                foreach (var (key, value) in wave.ItemCount)
                {
                    if (items.ContainsKey(key))
                    {
                        items[key] += value;
                    }
                    else
                    {
                        items.Add(key, value);
                    }
                }

                cardNumber += wave.CardCount;
            }

            foreach (var (i, count) in items)
            {
                rewards.Add(new RewardItem()
                {
                    Type = RewardType.Item,
                    Id = i,
                    Count = count
                });
            }

            rewards.Add(new RewardItem()
            {
                Type = RewardType.Item,
                Id = (int)Item.UniversalCard,
                Count = cardNumber
            });

            return rewards;
        }
        
        public static IEnumerable<RewardItem> GetActivityEliteReward(int chapterId)
        {
            var rewards = new List<RewardItem>();

            var waveTable = GetEliteChapterWaveTable(chapterId);

            //items
            var items = new Dictionary<int, int>();
            var cardNumber = 0;
            foreach (var (_, wave) in waveTable.Rows)
            {
                foreach (var (key, value) in wave.ItemCount)
                {
                    if (items.ContainsKey(key))
                    {
                        items[key] += value;
                    }
                    else
                    {
                        items.Add(key, value);
                    }
                }

                cardNumber += wave.CardCount;
            }

            foreach (var (i, count) in items)
            {
                rewards.Add(new RewardItem()
                {
                    Type = RewardType.Item,
                    Id = i,
                    Count = count
                });
            }

            rewards.Add(new RewardItem()
            {
                Type = RewardType.Item,
                Id = (int)Item.UniversalCard,
                Count = cardNumber
            });

            return rewards;
        }

        public static IEnumerable<EnemyConfig> GetLastDayEnemies(int chapterId)
        {
            var waveTable = GetChapterWaveTable(chapterId);
            var enemyConfigTable = ConfigManager.Instance.GetConfig<EnemyConfigTable>();
            var lastDay = waveTable.GetRowData(waveTable.Rows.Count);
            var result = lastDay.EnemyGroup.Select(enemyGroupInfo => enemyConfigTable.GetRowData(enemyGroupInfo.ID))
                .ToList();
            result.AddRange(lastDay.FixedEnemyGroup.Select(fixedEnemyGroupInfo =>
                enemyConfigTable.GetRowData(fixedEnemyGroupInfo.ID)));
            return result;
        }

        public static IEnumerable<EnemyConfig> GetBossEnemies(IEnumerable<EnemyConfig> enemyList)
        {
            return enemyList.Where(enemyConfig => enemyConfig.Type == 2).ToList();
        }

        public static IEnumerable<AttributeResistance.EnemyData> GetNormalEnemies(int chapterId)
        {
            var waves = GetChapterWaveTable(chapterId).Rows.Values.ToList();
            waves.Reverse();
            var enemyConfigTable = ConfigManager.Instance.GetConfig<EnemyConfigTable>();
            var enemyList = new List<AttributeResistance.EnemyData>();
            foreach (var wave in waves)
            {
                foreach (var enemyConfig in from enemyGroupInfo in wave.EnemyGroup
                         select enemyConfigTable.GetRowData(enemyGroupInfo.ID)
                         into enemyConfig
                         where enemyConfig.Type == EnemyType.Normal
                         let srcPrefab = enemyConfig.Prefab
                         where !enemyList.Any(b =>
                             b.Enemy.Prefab.AssetBundlePath.Equals(srcPrefab.AssetBundlePath) &&
                             b.Enemy.Prefab.AssetName.Equals(srcPrefab.AssetName))
                         select enemyConfig)
                {
                    enemyList.Add(new AttributeResistance.EnemyData { Enemy = enemyConfig, Wave = wave });
                }
                foreach (var enemyConfig in from enemyGroupInfo in wave.FixedEnemyGroup
                         select enemyConfigTable.GetRowData(enemyGroupInfo.ID)
                         into enemyConfig
                         where enemyConfig.Type == EnemyType.Normal
                         let srcPrefab = enemyConfig.Prefab
                         where !enemyList.Any(b =>
                             b.Enemy.Prefab.AssetBundlePath.Equals(srcPrefab.AssetBundlePath) &&
                             b.Enemy.Prefab.AssetName.Equals(srcPrefab.AssetName))
                         select enemyConfig)
                {
                    enemyList.Add(new AttributeResistance.EnemyData { Enemy = enemyConfig, Wave = wave });
                }
                
            }

            return enemyList;
        }
        public static IEnumerable<AttributeResistance.EnemyData> GetAllBossInChapter(int chapterId)
        {
            var waves = GetChapterWaveTable(chapterId).Rows.Values.ToList();
            waves.Reverse();
            var enemyConfigTable = ConfigManager.Instance.GetConfig<EnemyConfigTable>();
            var bossList = new List<AttributeResistance.EnemyData>();
            foreach (var wave in waves)
            {
                foreach (var enemyConfig in from enemyGroupInfo in wave.EnemyGroup
                         select enemyConfigTable.GetRowData(enemyGroupInfo.ID)
                         into enemyConfig
                         where enemyConfig.Type == EnemyType.Boss
                         let srcPrefab = enemyConfig.Prefab
                         where !bossList.Any(b =>
                             b.Enemy.Prefab.AssetBundlePath.Equals(srcPrefab.AssetBundlePath) &&
                             b.Enemy.Prefab.AssetName.Equals(srcPrefab.AssetName))
                         select enemyConfig)
                {
                    bossList.Add(new AttributeResistance.EnemyData { Enemy = enemyConfig, Wave = wave });
                }

                foreach (var enemyConfig in from enemyGroupInfo in wave.FixedEnemyGroup
                         select enemyConfigTable.GetRowData(enemyGroupInfo.ID)
                         into enemyConfig
                         where enemyConfig.Type == EnemyType.Boss
                         let srcPrefab = enemyConfig.Prefab
                         where !bossList.Any(b =>
                             b.Enemy.Prefab.AssetBundlePath.Equals(srcPrefab.AssetBundlePath) &&
                             b.Enemy.Prefab.AssetName.Equals(srcPrefab.AssetName))
                         select enemyConfig)
                {
                    bossList.Add(new AttributeResistance.EnemyData { Enemy = enemyConfig, Wave = wave });
                }
            }

            return bossList;
        }
    }
}

