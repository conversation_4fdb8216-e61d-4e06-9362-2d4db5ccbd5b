using App.GamePlay.ChapterSource;
using FQDev.AssetBundles;
using Language;
using UnityEngine;
using UnityEngine.UI;

namespace App.GamePlay.UI
{
    public class GameDayItem : MonoBehaviour
    {
        [SerializeField] private Image imageIcon;
        [SerializeField] private Text textDay;
        [SerializeField] private Text textDayCurrent;
        [SerializeField] private GameObject objCurrentDay;

        public void FreshUI(
            int day, 
            bool currentDay,
            int targetDay, 
            IChapterWaveSource chapterWaveSource)
        {
            textDay.SetLang("Day_1", day);
            textDayCurrent.SetLang("Day_1", day);
            textDay.gameObject.SetActive(!currentDay);
            objCurrentDay.SetActive(currentDay);

            if (day == targetDay)
            {
                imageIcon.sprite = AssetBundleManager.Instance.LoadAsset<Sprite>("ui/gameplay", "flag-icon");
            }
            else
            {
                var wave = chapterWaveSource.GetChapterWave(day);

                if (wave != null)
                {
                    imageIcon.color = Color.white;
                    imageIcon.sprite = wave is {AppearBoss: true}
                        ? AssetBundleManager.Instance.LoadAsset<Sprite>(
                            "ui/gameplay", 
                            "boss-icon")
                        : AssetBundleManager.Instance.LoadAsset<Sprite>(
                            "ui/gameplay",
                            currentDay ? "day-icon-2" : "day-icon");
                }
                else
                {
                    imageIcon.color = Color.clear;
                    textDay.gameObject.SetActive(false);
                    textDayCurrent.gameObject.SetActive(false);
                }
               
            }

            imageIcon.SetNativeSize();
        }
    }
}