namespace App.GamePlay.Buff
{
    public class CombineEnemyAttributeBuff : EnemyBuffBase<CombineEnemyAttributeBuff.Data>
    {
        private int _superpositionCount;
        public struct Data
        {
            public float Duration;
            public float Value;
            public int MaxSuperpositionCount;
            public float SuperpositionValue;
            public EnemyBuffType TriggerBuffType;
            public int HashID;
            public int EnhanceBuffID;
            public float EnhanceBuffDuration;
            public float EnhanceBuffEffectValue;
            public int EnhanceBuffMaxSuperpositionCount;
            public float EnhanceBuffSuperpositionValue;
        }

        
        internal override void MergeData(Data otherData)
        {
            if (otherData.MaxSuperpositionCount > 0)
            {
                if(_superpositionCount >= otherData.MaxSuperpositionCount)
                    return;
                
                _buffData.Duration = otherData.Duration;
                _buffData.Value += otherData.SuperpositionValue;
                _superpositionCount++;
            }
            else
            {
                if (otherData.Value > _buffData.Value)
                {
                    _buffData = otherData;
                }
            }
        }
        
        protected override void OnEnable()
        {
            _superpositionCount = 0;
        }

        internal override float Duration
        {
            get => _buffData.Duration;
            set => _buffData.Duration = value;
        }
    }
}