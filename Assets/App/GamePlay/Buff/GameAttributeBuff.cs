using UnityEngine.UI;

namespace App.GamePlay.Buff
{
    public class GameAttributeBuff : GameBuffBase<GameAttributeBuff.Data>
    {
        public struct Data
        {
            public float Duration;
            public GameBuffAttributeField Field;
            public float Value;
            public int MaxSuperpositionCount;
            public float SuperpositionValue;
        }

        internal override void MergeData(Data otherData)
        {
            if (Owner == null || Owner.GameBuffSet == null)
                return;
            if (otherData.Field != _buffData.Field)
                return;

            if (otherData.MaxSuperpositionCount > 0)
            {
                if (_superpositionCount >= otherData.MaxSuperpositionCount)
                    return;
                _superpositionCount++;
                _buffData.Duration = otherData.Duration;
                _buffData.Value += otherData.SuperpositionValue;
                Owner.GameBuffSet.ChangeAttribute(
                    otherData.Field,
                    otherData.SuperpositionValue);
            }
            else
            {
                if (otherData.Value > _buffData.Value)
                {
                    Owner.GameBuffSet.ChangeAttribute(
                        otherData.Field,
                        otherData.Value - _buffData.Value);
                    _buffData = otherData;
                }
                else
                {
                    _buffData.Duration = otherData.Duration;
                }
            }
        }

        protected override void OnEnable()
        {
            _superpositionCount = 0;
        }

        protected override void OnAdded()
        {
            Owner?.GameBuffSet.ChangeAttribute(_buffData.Field, _buffData.Value);
        }

        protected override void OnRemoved()
        {
            Owner?.GameBuffSet.ChangeAttribute(_buffData.Field, -_buffData.Value);
        }

        internal override float Duration
        {
            get => _buffData.Duration;
            set => _buffData.Duration = value;
        }
    }
}