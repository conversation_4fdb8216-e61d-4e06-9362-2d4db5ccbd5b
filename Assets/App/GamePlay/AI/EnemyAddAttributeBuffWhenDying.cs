using System.Collections.Generic;
using App.Config;
using App.GamePlay.Buff;
using UnityEngine;

namespace App.GamePlay.AI
{
    public class EnemyAddAttributeBuffWhenDying : EnemyAIBase
    {
        [SerializeField] private float _innerRadius;
        [SerializeField] private float _outerRadius;
        [SerializeField] private int _buffId;
        [SerializeField] private float _duration;
        [SerializeField] private float _effectValue;
        [SerializeField] private int _maxSuperpositionCount;
        [SerializeField] private float _superpositionValue;

        private List<Enemy> _targetEnemies = new();

        private EnemyBuffConfig _config;
        
        protected override void OnEnable()
        {
            _config = ConfigManager.Instance.GetConfig<EnemyBuffConfigTable>().GetRowData(_buffId);
        }

        public override void OnDied(bool forced)
        {
            if (!forced)
            {
                FindEnemies(_innerRadius, _outerRadius);
                
                var buffData = new EnemyAttributeBuff.Data()
                {
                    Duration = _duration,
                    Field = (EnemyAttributeField)_config.AttributeField,
                    Value = _effectValue,
                    MaxSuperpositionCount = _maxSuperpositionCount,
                    SuperpositionValue = _superpositionValue,
                };

                foreach (var enemy in _targetEnemies)
                {
                    AddBuff(enemy, buffData);
                }
            }

            enabled = false;
        }
        
        
        private void FindEnemies(float innerRadius, float outerRadius)
        {
            _targetEnemies.Clear();

            foreach (var enemy in Game.EnemyRefresher.AliveEnemies)
            {
                if(enemy == Owner)
                    continue;
                
                var distance = Vector2.Distance(transform.position, enemy.transform.position);
                if (distance >= innerRadius && distance <= outerRadius)
                {
                    _targetEnemies.Add(enemy);
                }
            }
        }

        private bool AddBuff(Enemy enemy, EnemyAttributeBuff.Data buffData)
        {
            return enemy.BuffSet.AddBuff<EnemyAttributeBuff, EnemyAttributeBuff.Data>(
                buffData,
                enemy,
                _config,
                EnemyBuffType.Attribute);
        }
    }
}