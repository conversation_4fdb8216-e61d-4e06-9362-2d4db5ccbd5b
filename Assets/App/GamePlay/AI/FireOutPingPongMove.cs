using System;
using System.Collections;
using System.Collections.Generic;
using App.GamePlay;
using UnityEngine;
using App.Pool;
using DG.Tweening;

namespace App.GamePlay.AI
{

    public class FireOutPingPongMove : FireOutBehaviourBase
    {
        [SerializeField] private Vector2 _velocity;
        [SerializeField] private float _backDistance;
        private float _moveDistance;
        private bool _isBack;
        void OnEnable()
        {
            _moveDistance = 0;
            _isBack = false;
        }

        public Vector2 Velocity
        {
            get => _velocity;
            set => _velocity = value;
        }

        public float BackDistance
        {
            get => _backDistance;
            set => _backDistance = value;
        }
    
        void Update()
        {
            if (GamePaused)
                return;
            var offset = _velocity * Time.deltaTime;
            transform.position = transform.position + (Vector3)offset;
            _moveDistance += offset.magnitude;
            if (_moveDistance > _backDistance)
            {
                if (_isBack)
                {
                    GetComponent<PooledObject>().Recycle();
                }
                else
                {
                    _isBack = true;
                    _moveDistance = 0;
                    _velocity *= -1;
                }
            }
        }
    }
}

