using App.GamePlay;
using App.GamePlay.Behaviors;
using UnityEngine;

namespace BehaviorDesigner.Runtime.Tasks
{
    [TaskCategory("GameCore/Events")]
    [TaskIcon("{SkinColor}HasReceivedEventIcon.png")]
    public class OnHeroDamaged : Conditional
    {
        public enum ElementFilter
        {
            None,
            Hero,
            Enemy,
        }
        public ElementFilter Filter;
        [SharedRequired]
        public SharedFloat DamageAmount;
        [SharedRequired]
        public SharedHero AttackerHero;
        [SharedRequired]
        public SharedEnemy AttackerEnemy;
        
        private bool _eventReceived = false;
        private bool _registered = false;
        
        public override void OnStart()
        {
            // 注册事件监听
            if (!_registered) {
                Owner.RegisterEvent<AttackContext, int>(HeroDamagedListener.EVENT_HERO_DAMAGED, ReceivedEvent);
                _registered = true;
            }
        }
        
        public override TaskStatus OnUpdate()
        {
            return _eventReceived ? TaskStatus.Success : TaskStatus.Failure;
        }
        
        public override void OnEnd()
        {
            if (_eventReceived) {
                Owner.UnregisterEvent<AttackContext, int>(HeroDamagedListener.EVENT_HERO_DAMAGED, ReceivedEvent);
                _registered = false;
            }
            _eventReceived = false;
        }

        private void ReceivedEvent(AttackContext context, int damage)
        {
            //添加筛选
            if (Filter != ElementFilter.None)
            {
                if (Filter == ElementFilter.Hero && context.Attacker is not Hero)
                    return;
                if (Filter == ElementFilter.Enemy && context.Attacker is not Enemy)
                    return;
            }

            DamageAmount.Value = damage;
            AttackerHero.Value = context.Attacker as Hero;
            AttackerEnemy.Value = context.Attacker as Enemy;
            _eventReceived = true;
        }

        public override void OnBehaviorComplete()
        {
            // 行为树完成时取消注册事件
            Owner.UnregisterEvent<AttackContext, int>(HeroDamagedListener.EVENT_HERO_DAMAGED, ReceivedEvent);
            _eventReceived = false;
            _registered = false;
        }
    }
}