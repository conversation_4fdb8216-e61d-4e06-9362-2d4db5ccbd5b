using BehaviorDesigner.Runtime;
using BehaviorDesigner.Runtime.Tasks;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore")]
    public class IsHeroValid : Conditional
    {
        public SharedHero Hero;
        public SharedGameObject HeroGameObject;

        public override TaskStatus OnUpdate()
        {
            var hero = Hero.Value;
            if (hero == null)
            {
                if (HeroGameObject.Value == null)
                    return TaskStatus.Failure;
                hero = HeroGameObject.Value.GetComponent<Hero>();
            }

            return (hero.TileItem == null || hero.Dead) ? TaskStatus.Failure : TaskStatus.Success;
        }

        public override void OnReset()
        {
            Hero = null;
            HeroGameObject = null;
        }
        
        
    }
}