using System.Collections;
using System.Collections.Generic;
using BehaviorDesigner.Runtime.Tasks;
using UnityEngine;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore")]
    public class IsEnemyEqual : Conditional
    {
        public SharedEnemy InputEnemy;
        public SharedEnemy CompareEnemy;
        
        public override TaskStatus OnUpdate()
        {
            if (InputEnemy.Value == null || CompareEnemy.Value == null) return TaskStatus.Failure;
            return InputEnemy.Value.GetInstanceID() == CompareEnemy.Value.GetInstanceID() ? TaskStatus.Success : TaskStatus.Failure;
        }

    }

}
