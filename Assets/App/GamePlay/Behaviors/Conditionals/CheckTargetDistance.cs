using System;
using App.GamePlay.Behaviors;
using BehaviorDesigner.Runtime;
using BehaviorDesigner.Runtime.Tasks;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore")]
    public class CheckTargetDistance : Conditional
    {
        public enum CheckType
        {
            xAxis,
            yAxis,
        }

        public CheckType Type;
        public SharedGameObject Target1; 
        public SharedGameObject Target2; 
        public SharedFloat MaxDistance;

        public override void OnStart()
        {
            if(Target1.Value == null)
                Target1 = gameObject;
        }

        public override TaskStatus OnUpdate()
        {
            switch (Type)
            {
                case CheckType.xAxis:
                    return Math.Abs(Target1.Value.transform.position.x - Target2.Value.transform.position.x) <=
                           MaxDistance.Value
                        ? TaskStatus.Success
                        : TaskStatus.Failure;
                case CheckType.yAxis:
                    return Math.Abs(Target1.Value.transform.position.y - Target2.Value.transform.position.y) <=
                           MaxDistance.Value
                        ? TaskStatus.Success
                        : TaskStatus.Failure;
            }

            return TaskStatus.Failure;
        }

        public override void OnReset()
        {
            Type = CheckType.xAxis;
        }
    }
}