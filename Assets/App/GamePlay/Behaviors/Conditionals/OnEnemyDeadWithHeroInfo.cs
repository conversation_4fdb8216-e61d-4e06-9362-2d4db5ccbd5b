using App.GamePlay;
using App.GamePlay.Behaviors;
using UnityEngine;

namespace BehaviorDesigner.Runtime.Tasks
{
    public class OnEnemyDeadWithHeroInfo : ElementConditional
    {
        public SharedHero Hero;
        public static string EventName = "OnEnemyDeadWithHeroInfo";
        private bool eventReceived = false;
        private bool registered = false;
        
        public override void OnStart()
        {
            // Let the behavior tree know that we are interested in receiving the event specified
            if (!registered) {
                Owner.RegisterEvent<object, object, object>(EventName, ReceivedEvent);
                registered = true;
            }
        }
        public override TaskStatus OnUpdate()
        {
            return eventReceived ? TaskStatus.Success : TaskStatus.Failure;
        }

        public override void OnEnd()
        {
            if (eventReceived) {
                Owner.UnregisterEvent<object, object, object>(EventName, ReceivedEvent);
                registered = false;
            }
            eventReceived = false;
        }
        
        private void ReceivedEvent(object attacker, object p2,object p3)
        {
            if (attacker is Hero)
            {
                Hero.Value = attacker as Hero;
                eventReceived = true;
            }
        }
        
        public override void OnBehaviorComplete()
        {
            // Stop receiving the event when the behavior tree is complete
            Owner.UnregisterEvent<object, object, object>(EventName, ReceivedEvent);
            eventReceived = false;
            registered = false;
        }

    }
}