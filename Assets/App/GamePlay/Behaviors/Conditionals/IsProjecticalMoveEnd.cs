using App.GamePlay.AI;
using BehaviorDesigner.Runtime;
using BehaviorDesigner.Runtime.Tasks;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore")]
    public class IsProjecticalMoveEnd : Conditional
    {
        public SharedFireOut FireOut;
        
        public override TaskStatus OnUpdate()
        {
            FireOutProjecticalMove projecticalMove = null;
            if (FireOut.Value == null)
            {
                projecticalMove = gameObject.GetComponent<FireOutProjecticalMove>();
            }
            else
            {
                projecticalMove = FireOut.Value.GetComponent<FireOutProjecticalMove>();
            }
          
            if (projecticalMove.End)
            {
                return TaskStatus.Success;
            }
            else
            {
                return TaskStatus.Failure;
            }
        }
    }
}