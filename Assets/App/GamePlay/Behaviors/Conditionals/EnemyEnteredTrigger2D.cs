using BehaviorDesigner.Runtime;
using BehaviorDesigner.Runtime.Tasks;
using UnityEngine;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore")]
    public class EnemyEnteredTrigger2D : ElementConditional
    {
        private bool enteredTrigger = false;
        public SharedEnemy otherEnemy;
        public override TaskStatus OnUpdate()
        {
            return enteredTrigger ? TaskStatus.Success : TaskStatus.Failure;
        }

        public override void OnEnd()
        {
            enteredTrigger = false;
        }

        public override void OnTriggerEnter2D(Collider2D other)
        {
            if (Game == null || other.gameObject.layer != Game.EnemyLayer)
                return;
            var enemy = other.gameObject.GetComponent<Enemy>();
            if (enemy != null)
            {
                otherEnemy.Value = enemy;
                enteredTrigger = true;
            }
        }

        public override void OnReset()
        {
            otherEnemy = null;
        }
    }
}