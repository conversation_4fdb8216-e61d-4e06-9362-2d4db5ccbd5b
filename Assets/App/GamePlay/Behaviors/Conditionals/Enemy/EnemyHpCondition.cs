using System.Collections;
using System.Collections.Generic;
using BehaviorDesigner.Runtime;
using BehaviorDesigner.Runtime.Tasks;
using UnityEngine;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore/EnemyCondition")]
    public class EnemyHpCondition : EnemyConditional
    {
        public enum Condition
        {
            GreaterThan,
            GreaterOrEqualTo,
            EqualTo,
            LessThan,
            LessOrEqualTo,
        }
        
        public SharedBool Self;
        public SharedEnemy Another;
        public SharedBool Until;

        public SharedBool IsPercentage;
        public SharedInt Hp;
        public SharedFloat HpPercentage;
        [BehaviorDesigner.Runtime.Tasks.Tooltip("The operation to perform")]
        public Condition condition;

        public override TaskStatus OnUpdate()
        {
            var enemy = Self.Value ? _enemy : Another.Value;
            var hp = IsPercentage.Value ? Mathf.FloorToInt(enemy.MaxHp * HpPercentage.Value) : Hp.Value;

            var con = condition switch
            {
                Condition.GreaterThan =>  enemy.Hp > hp,
                Condition.GreaterOrEqualTo =>  enemy.Hp >= hp,
                Condition.EqualTo => enemy.Hp == hp,
                Condition.LessThan => enemy.Hp < hp,
                Condition.LessOrEqualTo => enemy.Hp <= hp,
            };

            if (con)
            {
                return TaskStatus.Success;
            }
            else if(Until.Value)
            {
                return TaskStatus.Running;
            }
            else
            {
                return TaskStatus.Failure;
            }
        }
    }
}
