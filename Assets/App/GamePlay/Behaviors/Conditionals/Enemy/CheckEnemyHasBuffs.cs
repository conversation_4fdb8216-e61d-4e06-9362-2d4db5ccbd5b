using System;
using System.Collections.Generic;
using App.GamePlay.Buff;
using BehaviorDesigner.Runtime;
using BehaviorDesigner.Runtime.Tasks;

namespace App.GamePlay.Behaviors
{
    [System.Serializable]

    
    [TaskCategory("GameCore")]
    public class CheckEnemyHasBuffs : EnemyConditional
    {
        public SharedBool Self;
        public SharedEnemy Enemy;
        public SharedEnemyBuffTypeList BuffTypes;
        public CheckType Type;
      

        //one的意思是 只要有一个 就返回true
        //all的意思是 必须全部有 才返回true
        public enum CheckType
        {
            One,
            All
        }

        public override TaskStatus OnUpdate()
        {
            var enemy = Self.Value ? _enemy : Enemy.Value;
            if (enemy == null || enemy.Dead)
                return TaskStatus.Failure;


            TaskStatus result = (Type == CheckType.One) ? TaskStatus.Failure : TaskStatus.Success;
            foreach (var type in BuffTypes.Value)
            {
                if (enemy.BuffSet.HasBuffType(type))
                    result = (Type == CheckType.One) ? TaskStatus.Success : TaskStatus.Failure;
            }

            return result;
        }
    }

}