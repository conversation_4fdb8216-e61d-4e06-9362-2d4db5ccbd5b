using BehaviorDesigner.Runtime;
using BehaviorDesigner.Runtime.Tasks;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore")]
    public class HasTag : Conditional
    {
        public SharedGameObject Target;
        public string Tag;
        public SharedBool Contains;

        public override TaskStatus OnUpdate()
        {
            var go = GetDefaultGameObject(Target.Value);
            var tagComponent = go.GetComponent<GameObjectTag>();
            if (tagComponent && (Contains.Value ? tagComponent.Tag.Contains(Tag) : tagComponent.Tag.Equals(Tag)))
                return TaskStatus.Success;
            return TaskStatus.Failure;
        }
    }
}
