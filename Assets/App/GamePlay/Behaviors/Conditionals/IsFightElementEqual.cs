using System.Collections;
using System.Collections.Generic;
using BehaviorDesigner.Runtime.Tasks;
using UnityEngine;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore")]
    public class IsFightElementEqualSelf : ElementConditional
    {
        public SharedElement InputElement;

        public override TaskStatus OnUpdate()
        {
            if (InputElement.Value == null) return TaskStatus.Failure;
            return InputElement.Value.GetInstanceID() == _fightElement.GetInstanceID()
                ? TaskStatus.Success
                : TaskStatus.Failure;
        }
    }
}
