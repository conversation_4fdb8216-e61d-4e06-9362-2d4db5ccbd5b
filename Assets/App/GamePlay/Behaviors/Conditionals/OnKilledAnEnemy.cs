using System.Linq;
using <PERSON>havior<PERSON>esign<PERSON>.Runtime;
using BehaviorDesigner.Runtime.Tasks;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore")]
    public class OnKilledAnEnemy : Conditional
    {
        private int _startValue;
        private bool _inited = false;

        IGameProvider _gameProvider;
        protected Game Game
        {
            get
            {
                _gameProvider ??= gameObject.GetComponent<IGameProvider>();
                return _gameProvider.Game;
            }
        }
        private void Init()
        {
            _inited = true;
            _startValue = GetCurrentValue();
        }
        private int GetCurrentValue()
        {
            return Game.EnemyRefresher.AliveEnemies.Count();
        }
        
        public override TaskStatus OnUpdate()
        {
            if(!_inited)
                Init();
            
            var currentValue = GetCurrentValue();
            return currentValue > _startValue ? TaskStatus.Success : TaskStatus.Failure;
        }

        public override void OnEnd()
        {
            _inited = false;
        }
    }
}