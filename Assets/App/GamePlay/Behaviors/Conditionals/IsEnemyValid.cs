using BehaviorDesigner.Runtime;
using BehaviorDesigner.Runtime.Tasks;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore")]
    public class IsEnemyValid : Conditional
    {
        public SharedEnemy Enemy;
        public SharedGameObject EnemyGameObject;

        public override TaskStatus OnUpdate()
        {
            var enemy = Enemy.Value;
            if (enemy == null)
            {
                if (EnemyGameObject.Value == null)
                    return TaskStatus.Failure;
                enemy = EnemyGameObject.Value.GetComponent<Enemy>();
            }
            var valid = enemy is { Dead: false };
            return valid ? TaskStatus.Success : TaskStatus.Failure;
        }

        public override void OnReset()
        {
            Enemy = null;
            EnemyGameObject = null;
        }
        
        
    }
}