using App.GamePlay.Buff;
using BehaviorDesigner.Runtime;
using BehaviorDesigner.Runtime.Tasks;
using UnityEngine;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore")]
    public class AddHeroShieldControlBuff : ElementAction
    {
        public SharedHero Hero;
        public SharedHeroList Heros;
        public SharedFloat Duration;
        public SharedInt Count;
        public HeroAttributeField EffectField;
        public SharedFloat EffectValue;
        public SharedInt DistinctID = 0;
        public SharedString EffectPath;
        public SharedColor EffectColor = Color.white;

        //对enmey不适用
        public SharedBool HeroNullAddSelf = true;
        
        private AssetRef _effectAsset;

        public override void OnAwake()
        {
            base.OnAwake();
            if (!string.IsNullOrEmpty(EffectPath.Value))
                _effectAsset = AssetRef.Parse(EffectPath.Value);
        }

        public override TaskStatus OnUpdate()
        {
            var buffData = new HeroShieldControlBuff.Data()
            {
                Duration = Duration.Value,
                Field = EffectField,
                Value = EffectValue.Value,
                Count = Count.Value
            };
            
            var displayData = new HeroBuffDisplayData
            {
                EffectPrefab = _effectAsset,
                TintColor = EffectColor.Value
            };
            
            if (Hero.Value != null)
                AddBuff(Hero.Value, buffData, displayData);
            else if (HeroNullAddSelf.Value)
            {
                if(_hero != null)
                    AddBuff(_hero, buffData, displayData);
            }
              
          
            foreach (var hero in Heros.Value)
                AddBuff(hero, buffData, displayData);

            return TaskStatus.Success;
        }

        private void AddBuff(Hero hero, HeroShieldControlBuff.Data buffData, HeroBuffDisplayData displayData)
        {
            if(hero.Dead)
                return;
            IHeroBuffProvider provider;
            if (_hero != null)
                provider = _hero;
            else if (_enemy != null)
                provider = _enemy;
            else if (_lord != null)
                provider = _lord;
            else
                provider = _heroGlobal;
            
            hero.BuffSet.AddBuff<HeroShieldControlBuff, HeroShieldControlBuff.Data>(
                buffData,
                displayData,
                provider,
                DistinctID.Value,
                HeroBuffType.Shield);
        }

    }
}