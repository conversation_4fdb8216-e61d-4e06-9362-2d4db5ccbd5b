using App.Extends;
using <PERSON>haviorDesigner.Runtime;
using Behavior<PERSON>esign<PERSON>.Runtime.Tasks;
using UnityEngine;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore/Search")]
    public class FindFireOutInCircle : Action
    {
        public SharedGameObject Finder;
        public SharedFloat Radius;
        public SharedFireOut FirstResult;
        public SharedFireOutList Result;
        public bool UseFixedCenter = false;
        public SharedVector2 FixedCenter;

        private FireOutFactory _fireOutFactory;

        IGameProvider _gameProvider;
        protected Game Game
        {
            get
            {
                _gameProvider ??= gameObject.GetComponent<IGameProvider>();
                return _gameProvider.Game;
            }
        }

        public override void OnStart()
        {
            _fireOutFactory = Game.FireOutFactory;
        }

        public override void OnReset()
        {
            base.OnReset();
            Result.Value.Clear();
        }

        public override TaskStatus OnUpdate()
        {
            var fireOuts = _fireOutFactory.GetActiveFireOuts();
            var center = UseFixedCenter
                ? FixedCenter.Value
                : (Vector2)(GetDefaultGameObject(Finder.Value).transform.position);

            foreach (var fireOut in fireOuts)
            {
                if(Vector2.Distance(fireOut.transform.position,center) <= Radius.Value)
                    Result.Value.Add(fireOut);
            }
            
            if (Result.Value.Count > 0)
            {
                Result.Value.Sort((a, b) => 
                    a.transform.FastDistance(center)
                        .CompareTo(b.transform.FastDistance(center)));
                FirstResult.Value = Result.Value[0];
            }
            return TaskStatus.Success;
        }
    }
}