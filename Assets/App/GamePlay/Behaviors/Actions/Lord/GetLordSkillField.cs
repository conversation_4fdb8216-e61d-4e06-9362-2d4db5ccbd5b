using System.Collections.Generic;
using BehaviorDesigner.Runtime;
using BehaviorDesigner.Runtime.Tasks;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore/Getter")]
    public class GetLordSkillField : ElementAction
    {
        public SharedLordField FieldID;
        public SharedBool UseInputValue = false;
        public SharedInt InputFieldID;
        public SharedFloat FloatValue;
        public bool IsIntValue = false;
        public SharedInt IntValue;

        public override TaskStatus OnUpdate()
        {
            var value = _lord.Root.Attributes.GetValueOrDefault(UseInputValue.Value
                ? InputFieldID.Value
                : (int)FieldID.Value);

            if (IsIntValue)
                IntValue.Value = (int)value;
            else
                FloatValue.Value = value;

            return TaskStatus.Success;
        }
    }
}