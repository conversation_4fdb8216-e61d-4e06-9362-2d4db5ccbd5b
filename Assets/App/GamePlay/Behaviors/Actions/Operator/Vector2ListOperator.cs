using BehaviorDesigner.Runtime;
using BehaviorDesigner.Runtime.Tasks;
using System;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore/Operation")]
    public class Vector2ListOperator : BehaviorDesigner.Runtime.Tasks.Action
    {
        public enum Operation
        {
            Clear,
            Add,
            RemoveAt,
            Get,
            GetRandomInt,
            GetCount,
        }
        public Operation OperationType;
        public SharedVector2List Vector2List;
        public SharedInt Index = -1;
        public SharedVector2 Target;
        public SharedInt Count;
        private Random random = new Random();
        public override TaskStatus OnUpdate()
        {
            return Operator();
        }

        public override void OnReset()
        {
            OperationType = Operation.Clear;
            Index = -1;
            Target = null;
            Count = 0;
        }

        private TaskStatus Operator()
        {
            switch(OperationType)
            {
                case Operation.Clear:
                {
                    Vector2List.Value.Clear();
                };break;
                case Operation.Add:
                {
                    Vector2List.Value.Add(Target.Value);
                };break;
                case Operation.GetRandomInt:
                {
                    Target.Value = Vector2List.Value[random.Next(Vector2List.Value.Count)];
                };break;
                case Operation.Get:
                {
                    Target.Value = Vector2List.Value[Index.Value];
                };break;
                case Operation.RemoveAt:
                {
                   Vector2List.Value.RemoveAt(Index.Value);
                };break;
                case Operation.GetCount:
                {
                    Count.Value = Vector2List.Value.Count;
                };break;
            }
            return TaskStatus.Success;
        }
    }
}