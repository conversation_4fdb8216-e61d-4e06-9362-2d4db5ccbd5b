using System.Collections.Generic;
using System.Linq;
using BehaviorDesigner.Runtime;
using BehaviorDesigner.Runtime.Tasks;
using UnityEngine;
using Action = BehaviorDesigner.Runtime.Tasks.Action;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore/Operation")]
    public abstract class ListOperator<T> : Action
        where T : Component
    {
        public enum Operation
        {
            Clear,
            Add,
            Remove,
            RemoveAt,
            Get,
            GetCount,
            Distinct,
            Sub,
            Exist,
            Shuffle,
        }
        public Operation OperationType;

        protected abstract T GetTarget();

        protected TaskStatus Operator(
            SharedVariable<List<T>> Source, 
            SharedVariable<List<T>> Targets,
            SharedVariable<T> Target, 
            SharedGameObject TargetGameObject,
            SharedInt Index, 
            SharedInt Count)
        {
            switch (OperationType) {
                case Operation.Clear:
                    Source.Value.Clear();
                    break;
                case Operation.Add:
                {
                    var enemy = GetTarget();
                    if (enemy != null)
                        Source.Value.Add(enemy);
                    
                    Source.Value.AddRange(Targets.Value);

                    break;
                }
                case Operation.Remove:
                {
                    var enemy = GetTarget();
                    if (enemy != null)
                        Source.Value.Remove(enemy);
                    
                    Source.Value.RemoveContains(Targets.Value);
                    break;
                }
                case Operation.RemoveAt:
                    Source.Value.RemoveAt(Index.Value);
                    break;
                case Operation.Get:
                {
                    var index = Index.Value;
                    if (index == -1)
                    {
                        if (Source.Value.Count == 0)
                            return TaskStatus.Failure;
                        
                        var value = Source.Value.Random();
                        Target.Value = value;
                        TargetGameObject.Value = value.gameObject;
                    }
                    else
                    {
                        if(index < 0 || index >= Source.Value.Count)
                            return TaskStatus.Failure;
                        var value = Source.Value[index];
                        Target.Value = value;
                        TargetGameObject.Value = (value!= null && value.gameObject!=null) ? value.gameObject:null;
                    }
                    break;
                }
                case Operation.GetCount:
                {
                    Count.Value = Source.Value.Count;
                    break;
                }
                case Operation.Distinct:
                {
                    Source.Value = Source.Value.Distinct().ToList();
                    break;
                }
                case Operation.Sub:
                {
                    var index = Index.Value;
                    if (index >= Source.Value.Count)
                        return TaskStatus.Failure;
                    
                    if (index < 0)
                        index = 0;

                    for (int i = index ; i < index + Count.Value; i++)
                    {
                        if(i >= Source.Value.Count)
                            break;
                        Targets.Value.Add(Source.Value[i]);
                    }
                    break;
                }
                case Operation.Exist:
                {
                    if (Target.Value != null)
                    {
                        var result = Source.Value.Exists(x => x == Target.Value);
                        return  result ? TaskStatus.Success : TaskStatus.Failure;
                    }
                    else
                    {
                        var result = Source.Value.Intersect(Targets.Value);
                        if(result.Count()==Targets.Value.Count)
                        {
                            return TaskStatus.Success;
                        }
                        else
                        {
                            return TaskStatus.Failure;
                        }
                    }
                }
                case Operation.Shuffle:
                {
                    Source.Value.Shuffle();

                    if (Count.Value > 0)
                        Targets.Value = Source.Value.Take(Count.Value).ToList();
                    break;
                }
            }

            return TaskStatus.Success;
        }
    }
}