using System.Linq;
using <PERSON>havior<PERSON>esign<PERSON>.Runtime;
using BehaviorDesigner.Runtime.Tasks;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore/Operation")]
    public class GetEnemyListHasBuff : Action
    {
        public SharedEnemyList Enemies;
        public SharedInt BuffId;
        public SharedEnemy FirstTarget;
        public SharedGameObject FirstTargetGameObject;
        public SharedEnemyList OutTargets;

        public override TaskStatus OnUpdate()
        {
            if (Enemies?.Value == null || Enemies.Value.Count == 0)
                return TaskStatus.Failure;

            OutTargets.Value = Enemies.Value.Where(e => e.BuffSet.HasBuffDistinctById(BuffId.Value)).ToList();
            
            if (OutTargets.Value?.Count > 0)
            {
                FirstTarget.Value = OutTargets.Value[0];
                FirstTargetGameObject.Value = FirstTarget.Value.gameObject;
            }
            else
            {
                FirstTarget.Value = null;
                FirstTargetGameObject.Value = null;
            }

            return TaskStatus.Success;
        }
    }
}