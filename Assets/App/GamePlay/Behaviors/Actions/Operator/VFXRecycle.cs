using App.Pool;
using <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.Runtime;
using BehaviorDesigner.Runtime.Tasks;
using UnityEngine;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore/Operation")]
    public class VFXRecycle : Action
    {
        public SharedGameObject Target;
        private PooledObject _pooledObject;
    
        private Transform _vfxTransform;

        public override void OnStart()
        {
           
        }

        public override TaskStatus OnUpdate()
        {
            if (Target.Value == null)
                return TaskStatus.Failure;
            _vfxTransform = Target.Value.transform;
            _pooledObject = _vfxTransform.GetComponent<PooledObject>();
            if(_pooledObject!=null)
            _pooledObject.Recycle();
            return TaskStatus.Success;
        }
    }
}