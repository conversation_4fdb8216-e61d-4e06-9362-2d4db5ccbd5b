using BehaviorDesigner.Runtime;
using BehaviorDesigner.Runtime.Tasks;
using System;
using UnityEngine;
using Action = BehaviorDesigner.Runtime.Tasks.Action;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore/Operation")]
    public class Vector2IntSetXY : Action
    {
        [RequiredField]
        public SharedVector2Int vector2;
        [BehaviorDesigner.Runtime.Tasks.Tooltip("The X value")]
        public SharedInt storeXVariable;
        [BehaviorDesigner.Runtime.Tasks.Tooltip("The Y value")]
        public SharedInt storeYVariable;

        public override TaskStatus OnUpdate()
        {
            vector2.Value = new Vector2Int(storeXVariable.Value, storeYVariable.Value);
            return TaskStatus.Success;
        }

        public override void OnReset()
        {
            vector2 = Vector2Int.zero;
            storeXVariable = storeYVariable = 0;
        }
    }
}