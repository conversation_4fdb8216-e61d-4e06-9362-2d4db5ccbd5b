using BehaviorDesigner.Runtime;
using BehaviorDesigner.Runtime.Tasks;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore/Operation")]
    public class EnemyListOperator : ListOperator<Enemy>
    {
        public SharedEnemyList Enemies;
        public SharedInt Index = -1;
        public SharedEnemy Target;
        public SharedGameObject TargetGameObject;
        public SharedEnemyList Targets;
        public SharedInt Count;

        public override TaskStatus OnUpdate()
        {
            return Operator(Enemies, Targets, Target, TargetGameObject, Index, Count);
        }

        protected override Enemy GetTarget()
        {
            if (Target.Value) 
                return Target.Value;
            if (TargetGameObject.Value)
                return TargetGameObject.Value.GetComponent<Enemy>();
            return null;
        }
        
        public override void OnReset()
        {
            OperationType = Operation.Clear;
            Index = -1;
            Target = null;
            TargetGameObject = null;
            Enemies = null;
            Targets = null;
        }
    }
}