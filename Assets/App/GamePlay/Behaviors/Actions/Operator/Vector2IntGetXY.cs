using BehaviorDesigner.Runtime;
using BehaviorDesigner.Runtime.Tasks;
using System;
using UnityEngine;
using Action = BehaviorDesigner.Runtime.Tasks.Action;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore/Operation")]
    public class Vector2IntGetXY : Action
    {
        [BehaviorDesigner.Runtime.Tasks.Tooltip("The Vector2 to get the values of")]
        public SharedVector2Int vector2Variable;
        [BehaviorDesigner.Runtime.Tasks.Tooltip("The X value")]
        [RequiredField]
        public SharedInt storeX;
        [BehaviorDesigner.Runtime.Tasks.Tooltip("The Y value")]
        [RequiredField]
        public SharedInt storeY;

        public override TaskStatus OnUpdate()
        {
            storeX.Value = vector2Variable.Value.x;
            storeY.Value = vector2Variable.Value.y;
            return TaskStatus.Success;
        }

        public override void OnReset()
        {
            vector2Variable = Vector2Int.zero;
            storeX = storeY = 0;
        }
    }
}