using System.Linq;
using <PERSON>haviorDesign<PERSON>.Runtime;
using BehaviorDesigner.Runtime.Tasks;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore/Getter")]
    //获取场上最强英雄
    public class GetBestHero : ElementAction
    {
        public SharedHeroList Heros;
        public SharedHero TargetHero;
        
        public override TaskStatus OnUpdate()
        {
            if (Heros.Value.Count > 0)
            {
                var maxGrade = Heros.Value.Max(h => h.Grade);
                var maxQuality = Heros.Value
                    .Where(h => h.Grade >= maxGrade)
                    .Max(h => h.HeroConfig.Quality);
                
                if (maxGrade > 1)
                {
                    var bestHeroList = Heros.Value
                        .Where(h => h.Grade >= maxGrade && h.HeroConfig.Quality >= maxQuality)
                        .Select(h => h)
                        .ToList();

                    TargetHero.Value = bestHeroList.Random();
                }
            }

            return TaskStatus.Success;
        }
    }
}