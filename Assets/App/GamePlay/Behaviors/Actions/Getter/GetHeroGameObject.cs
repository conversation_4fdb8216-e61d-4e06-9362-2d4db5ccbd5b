using BehaviorDesigner.Runtime;
using BehaviorDesigner.Runtime.Tasks;
using UnityEngine;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore/Getter")]
    public class GetHeroGameObject : ElementAction
    {
        public SharedGameObject HeroGameObject;
        public SharedVector2 HeroPosition;

        public override TaskStatus OnUpdate()
        {
            HeroGameObject.Value = _hero.gameObject;
            HeroPosition.Value = _hero.transform.position;
            return TaskStatus.Success;
        }
    }
}