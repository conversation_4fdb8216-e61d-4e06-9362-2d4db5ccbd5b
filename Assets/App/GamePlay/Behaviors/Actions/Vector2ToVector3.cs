using BehaviorDesigner.Runtime;
using BehaviorDesigner.Runtime.Tasks;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore")]
    public class Vector2ToVector3 : Action
    {
        public SharedVector2 Input;
        public SharedVector3 Store;

        public override TaskStatus OnUpdate()
        {
            Store.Value = Input.Value;
            return TaskStatus.Success;
        }
    }
}