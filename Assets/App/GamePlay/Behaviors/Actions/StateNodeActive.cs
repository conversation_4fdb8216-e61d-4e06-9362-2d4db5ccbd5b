using System.Collections.Generic;
using UnityEngine;
using System;
using System.Linq;

namespace App.GamePlay.Behaviors
{
    public class StateNodeActive : MonoBehaviour
    {
        [Serializable]
        public class  Node
        {
            public int State;
            public List<GameObject> ActiveGameObjects;
        }

        [SerializeField] private List<Node> _nodes = new List<Node>();

        public void SetState(int state)
        {
            foreach (var go in _nodes.Where(node => node.State != state).SelectMany(node => node.ActiveGameObjects))
            {
                go.SetActive(false);
            }
            
            foreach (var go in _nodes.Where(node => node.State == state).SelectMany(node => node.ActiveGameObjects))
            {
                go.SetActive(true);
            }
        }
    }
}