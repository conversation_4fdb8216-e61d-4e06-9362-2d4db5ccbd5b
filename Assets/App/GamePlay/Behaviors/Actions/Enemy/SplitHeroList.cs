using System.Linq;
using <PERSON>havior<PERSON><PERSON><PERSON>.Runtime.Tasks;
using DG.Tweening;
using UnityEngine;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore")]
    public class SplitHeroList : Action
    {
        public SharedHeroList Heros;

        public SharedHeroList OutList1;
        public SharedHeroList OutList2;

        public override TaskStatus OnUpdate()
        {
            var count = Heros.Value.Count;
            if (count < 2) 
                return TaskStatus.Failure;
            
            //如果是奇数，则删掉最后一个
            if (count % 2 == 1)
            {
                Heros.Value.RemoveAt(count - 1);
            }

            var listSplit = Heros.Value.Select((s, i) => new { s, i })
                .GroupBy(x => x.i % 2)
                .Select(g => g.Select(x => x.s).ToList())
                .ToList();
            OutList1.Value = listSplit[0];
            OutList2.Value = listSplit[1];
            
            
            return TaskStatus.Success;
        }
    }
}