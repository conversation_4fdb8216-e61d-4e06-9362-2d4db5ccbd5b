using App.GamePlay.Tiles.Module;
using BehaviorDesigner.Runtime;
using BehaviorDesigner.Runtime.Tasks;
using UnityEngine;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore/EnemyAction")]
    public class ChangeHeroGrade : ElementAction
    {
        public SharedVector2Int TargetIndex;
        public SharedInt HeroGrade;

        public SharedBool EffectShield = true;

        public override TaskStatus OnUpdate()
        {
            var board = Game.Board;
            if (board[TargetIndex.Value].Vacant) 
                return TaskStatus.Failure;
            var before = board[TargetIndex.Value].Item.GetComponent<TileItemHero>().Hero.Grade;
            if (EffectShield.Value && HeroGrade.Value < before)
            {
                if (board[TargetIndex.Value].Item.GetComponent<TileItemHero>().Hero.BuffSet.DoShield())
                    return TaskStatus.Success;
            }
            var heroId = board[TargetIndex.Value].Item.GetComponent<TileItemHero>().Hero.ID;
            return Game.RemoveHero(TargetIndex.Value) &&
                   Game.CreateHero(TargetIndex.Value, heroId, HeroGrade.Value)
                ? TaskStatus.Success
                : TaskStatus.Failure;
        }
    }
}
