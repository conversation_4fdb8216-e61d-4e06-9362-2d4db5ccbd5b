using App.GamePlay.AI;
using BehaviorDesigner.Runtime;
using BehaviorDesigner.Runtime.Tasks;
using UnityEngine;
namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore")]
    public class EnemyMoveSwitch : ElementAction
    {
        public SharedBool Stop = true;
        
        public override TaskStatus OnUpdate()
        {
            var moveComponents = _enemy.gameObject.GetComponents<EnemyMoveBase>();
            if (moveComponents == null) 
                return TaskStatus.Success;
            foreach (var moveComponent in moveComponents)
            {
                if (Stop.Value)
                    moveComponent.Block ++;
                else
                    moveComponent.Block --;
            }

            return TaskStatus.Success;
        }
    }
}