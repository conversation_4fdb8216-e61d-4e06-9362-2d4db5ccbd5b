using BehaviorDesigner.Runtime;
using BehaviorDesigner.Runtime.Tasks;
using UnityEngine;
using Action = BehaviorDesigner.Runtime.Tasks.Action;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore/EnemyAction")]
    public class EnemyHeal : Action
    { 
        public SharedEnemy TargetEnemy;
        public SharedEnemyList TargetEnemies;
        public SharedBool IsPercentage;
        public SharedFloat Percentage;
        public SharedInt Health;

        public override TaskStatus OnUpdate()
        {
           
            if (TargetEnemy.Value == null)
            {
                foreach (var enemy in TargetEnemies.Value)
                {
                    enemy.Heal(
                        IsPercentage.Value ? Mathf.FloorToInt(enemy.MaxHp * Percentage.Value) : Health.Value,
                        Color.green);
                }
            }
            else
            {
                TargetEnemy.Value.Heal(
                    IsPercentage.Value ? Mathf.FloorToInt(TargetEnemy.Value.MaxHp * Percentage.Value) : Health.Value,
                    Color.green);
            }
           
            return TaskStatus.Success;
        }
    }
}