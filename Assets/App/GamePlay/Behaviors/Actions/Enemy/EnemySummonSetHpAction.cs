using App.GamePlay.AI;
using BehaviorDesigner.Runtime;
using BehaviorDesigner.Runtime.Tasks;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore")]
    public class EnemySummonSetHpAction : ElementAction
    {
        public SharedInt SummonId;
        public SharedFloat FinalHpRatio;

        public override TaskStatus OnUpdate()
        {
            var enemy = EnemyAIHelper.SummonOneMonsterByPosition(
                _enemy,
                SummonId.Value,
                _enemy.transform.position);

            enemy.SetMaxHpAddRatio(FinalHpRatio.Value);
            
            return TaskStatus.Success;
        }

     
    }
}