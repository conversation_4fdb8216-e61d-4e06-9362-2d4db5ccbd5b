using System.Collections.Generic;
using App.Config;
using App.GamePlay.Tiles;
using App.GamePlay.Tiles.Module;
using BehaviorDesigner.Runtime;
using BehaviorDesigner.Runtime.Tasks;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore/Search")]
    public class FindHeroAll : ElementAction
    {
        public SharedHeroList OutputHeros;
        public SharedInt MinGrade;
        public SharedIntList AttributeFilter;
        public SharedIntList ExcludeAttributeFilter;
        public SharedIntList ExceptId;
        public SharedIntList IncludeID;
        public SharedBool IncludeDeadHero = false;
        public SharedBool IgnoreMaxGradeHero = false;
        public GameFlag TargetGame = GameFlag.Self;
        private readonly List<TileItemHero> _itemHeroes = new ();
        
        public override TaskStatus OnUpdate()
        {
            OutputHeros.Value.Clear();
            _itemHeroes.Clear();

            if (!BehaviorGameUtility.TryGetBoard(
                    TargetGame, 
                    Game, 
                    out var board))
                return TaskStatus.Success;
            
            for (var x = 0; x < board.ColumnCount; x++)
            {
                for (var y = 0; y < board.RowCount; y++)
                {
                    AddItemHero(board[x, y]);
                }
            }

            foreach (var t in _itemHeroes)
            {
                OutputHeros.Value.Add(t.Hero);
            }

            return TaskStatus.Success;
        }

        private void AddItemHero(TileSlot slot)
        {
            
            if (slot == null
                || slot.Vacant)
                return;
            var tileItem = slot.Item.GetComponent<TileItemHero>();

            if (tileItem == null || tileItem.Hero == null)
                return;
            if (MinGrade.Value >= tileItem.GradeConfig.Grade)
                return;
            if (IgnoreMaxGradeHero.Value && tileItem.GradeConfig.Grade >=
                ConfigManager.Instance.HeroGradeRef.GetMaxGrade(tileItem.HeroConfig.Id))
                return;
            
            if (AttributeFilter.Value is { Count: > 0 } &&
                !AttributeFilter.Value.Contains((int)tileItem.Hero.AttackAttributeType))
                return;
            
            if (ExcludeAttributeFilter.Value is { Count: > 0 } &&
                ExcludeAttributeFilter.Value.Contains((int)tileItem.Hero.AttackAttributeType))
                return;
            
            if (ExceptId.Value is { Count: > 0 } && ExceptId.Value.Contains(tileItem.Hero.ID))
                return;
            
            if(!IncludeDeadHero.Value && tileItem.Hero.Dead)
                return;
            
            if (IncludeID.Value is { Count: > 0 } && !IncludeID.Value.Contains(tileItem.Hero.ID))
                return;
            
            _itemHeroes.Add(tileItem);
        }
    }
}