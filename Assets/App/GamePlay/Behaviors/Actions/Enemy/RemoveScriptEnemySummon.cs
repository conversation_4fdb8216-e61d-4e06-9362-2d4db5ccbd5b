using App.GamePlay.AI;
using BehaviorDesigner.Runtime.Tasks;
using UnityEngine;
namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore")]
    public class RemoveScriptEnemySummon : ElementAction
    {
        public override TaskStatus OnUpdate()
        {
            var summon = GetComponent<EnemySummon>();
            if (_enemy != null && summon != null)
            {
                Object.Destroy(summon);
                return TaskStatus.Success;
            }
            return TaskStatus.Failure;
        }
    }
}