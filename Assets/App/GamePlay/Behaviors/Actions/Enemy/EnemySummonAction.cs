using App.GamePlay.AI;
using BehaviorDesigner.Runtime;
using BehaviorDesigner.Runtime.Tasks;

namespace App.GamePlay.Behaviors
{
    [TaskCategory("GameCore")]
    public class EnemySummonAction : ElementAction
    {
        public SharedInt SummonId;
        public SharedInt SummonCount;
        public SharedFloat SummonXPosition; 
        public SharedFloat SummonYPosition;
        public SharedBool UseSingleIndex;
        public SharedInt SummonXSingleIndex; 
        public SharedInt SummonYSingleIndex;
        public SharedBool UseIndex;
        public SharedVector2IntList SummonListIndex;
        public SharedInt ExtraHp;
        
        public override TaskStatus OnUpdate()
        {
            if (UseIndex.Value)
            {
                EnemyAIHelper.SummonMonster(
                    _enemy,
                    SummonId.Value, 
                    SummonCount.Value, 
                    SummonListIndex.Value,
                    ExtraHp.Value
                );
            }
            else if (UseSingleIndex.Value)
            {
                EnemyAIHelper.SummonMonster(
                    _enemy,
                    SummonId.Value, 
                    SummonCount.Value, 
                    SummonXSingleIndex.Value,
                    SummonYSingleIndex.Value,
                    ExtraHp.Value
                );
            }
            else
            {
                EnemyAIHelper.SummonMonster(
                    _enemy,
                    SummonId.Value, 
                    SummonCount.Value, 
                    SummonXPosition.Value, 
                    SummonYPosition.Value,
                    _enemy.transform.position,
                    ExtraHp.Value);
            }
            
            return TaskStatus.Success;
        }

     
    }
}