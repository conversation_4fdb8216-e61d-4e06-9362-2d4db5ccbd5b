using App.GamePlay.AI;
using <PERSON>haviorDesigner.Runtime;
using <PERSON>haviorDesign<PERSON>.Runtime.Tasks;

namespace App.GamePlay.Behaviors.Setter
{
    [TaskCategory("GameCore/Setter")]
    public class SetFireOutProjecticalTarget : Action
    {
        public SharedFireOut FireOut;
        public SharedGameObject Target;
        
        public override TaskStatus OnUpdate()
        {
            var fireOut = FireOut.Value ? FireOut.Value : gameObject.GetComponent<FireOut>();

            if (fireOut == null)
                return TaskStatus.Failure;

            var move = fireOut.GetComponent<FireOutProjecticalMove>();
            if (!move) return TaskStatus.Failure;

            move.TargetPosition = Target.Value.transform.position;
            move.DoStart();
          
            return TaskStatus.Success;
        }
    }
}


