using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using App.Activity.ActivityChapter;
using App.Config;
using App.DailyBlessing;
using App.GamePlay.UI;
using App.GlobalTalent;
using FQDev.DataCenter.Utility;
using FQDev.EventSystem;
using FQDev.Time;
using HttpLibrary;
using HttpLibrary.Response;
using App.AutoMerge;
using App.Treasure;
using Cysharp.Threading.Tasks;
using FQDev.UI.Layer;

namespace App.GamePlay.ChapterSource
{
    public class ActivityChapterController : 
        IChapterDataSource, 
        IChapterController, 
        IEventSender
    {
        public EventDispatcher Dispatcher { get; } = EventDispatcher.Global;
        private readonly ChapterConfig _chapterConfig;
        private readonly ActivityChapterConfig _activityConfig;
        private readonly long _activityEndTime;

        private readonly List<int> _rules = new ();
        
        public ActivityChapterController(int activityId, int chapterID)
        {
            _chapterConfig = ConfigManager.Instance.GetConfig<ChapterConfigTable>().GetRowData(chapterID);
            _activityConfig = ConfigManager.Instance.GetConfig<ActivityChapterConfigTable>().GetRowData(activityId);
            (_, _activityEndTime) = ActivityChapterManager.Instance.GetActivityTime(activityId);
            

            DB.User.Chapter = chapterID;
            // DB.User.ChapterMode = Mode;
            // DB.User.ChapterDifficulty = _activityConfig.ChapterIds.FindIndex(id => id == chapterID);
            _rules.AddRange(DailyBlessingManager.Instance.DailyBlessing);
            _rules.AddRange(DB.ElementTalent.GetActiveRuleIds());
            var (lord, rules) = App.Lord.LordUtility.GetLordData(DB.Lords.CurLord, Mode, false);
            Lord = lord;
            _rules.AddRange(rules);
            _globalTalents.AddRange(GlobalTalentData.Instance.GetGlobalTalentValueListByMode(Mode));
            _propertyData.AddRange(TreasureWarpData.Instance.GetTreasureProperties());
        }

        public int Mode => (int)LevelMode.Activity;
        public int ChapterID => _chapterConfig.Id;
        public HeroData[] AppearHeroes { get; } = DB.Hero.GetAppearHeroes();
        public GuardianHeroData[] GuardianHeroes { get; } = null;
        public LordData Lord { get; } = default;
        public AssetRef EnemyDeathEffect => _chapterConfig.EnemyDeathEffect;
        public bool OpenAutoMerge { get; private set;}
        public bool IgnorePauseLayer => false;

        public List<IntPair> GetSkillBox(int tier)
        {
            return tier switch
            {
                1 => _chapterConfig.SkillBox1,
                2 => _chapterConfig.SkillBox2,
                3 => _chapterConfig.SkillBox3,
                _ => null
            };
        }

        public bool CheckRestrictEnemySpawnPosition(int day)
        {
            return day <= _chapterConfig.FixedLocationDay;
        }
        
        public int EndDay => _chapterConfig.PassDay;
        public int TowerHp => _chapterConfig.Hp;
        public int TransferDay => _chapterConfig.TransferDay;
        
        public int GetSceneId(Game game)
        {
            var sceneIndex = game.Day / TransferDay;
            sceneIndex %= _chapterConfig.Scenes.Count;
            return _chapterConfig.Scenes[sceneIndex];
        }

        public int GetComboStepIncrease(Game game, int comboCount)
        {
            return comboCount;
        }

        public List<Tuple<SkillValueConfig, float>> RetainSkills => new();
        public int ResurrectionCount => ConfigManager.Instance.GetConfig<GamePlayConst>().ActivityResurrectionCount;
        public void ProcessGameStart(Game game)
        {
            LogEvent.I.Level_Start_Activity();
        }

        public void ProcessGameLoaded(Game game)
        {
            
        }

        public void ProcessGameRestore(Game game)
        {
            LogEvent.I.Level_Restore_Activity();
        }

        public void ProcessGameExit(Game game)
        {
            LogEvent.I.Level_Quit_Activity();
        }

        public void ProcessGameResult(Game game)
        {
            if (game.Victory)
                DoProcessGameResult(game);
            else
                GameResultUtility.ShowGameLose(game, () => DoProcessGameResult(game));

        }

        private void DoProcessGameResult(Game game)
        {
            GameResultLayer.Create(game);
            if (game.Victory)
            {
                LogEvent.I.Level_Win_Activity();
                if (DB.User.ConsecutiveFailedTimes > 0)
                    DB.User.ConsecutiveFailedTimes = 0;
            }
            else
            {
                LogEvent.I.Level_Finish_Activity();
                DB.User.ConsecutiveFailedTimes++;
            }

            var passDay = game.PassedDay;
            

            var param = new Dictionary<string, object>()
            {
                {"id", _activityConfig.Id},
                {"lv", _activityConfig.ChapterIds.FindIndex(id => id == ChapterID)},
                {"day", passDay},
                {"cost", game.GetGameCost()},
                {"taskCount", game.GetGameResultTaskParam()},
                {"type", game.Victory ? "success" : "fail"},
                {"statistics", game.GetGameCheckData()},
                {"battleHeroes", game.AppearHeroes.Keys.ToArray()},
                {"gameId", game.GetGameUniqueID()},
            };

            var http = HttpManager.Instance.Post($"{ServerConfig.UrlPrefix}settleTrial")
                .SetMaskEnable(true);
            http.OnSucceed = OnGameResultRespond;
            http.OnFailure = OnGameResultFailed;
            http.OnCloseReconnection = () => this.DispatchEvent(Witness<GamePlayResultConnectionFailedEvent>._);

            http.AddRequestId()
                .AddToken()
                .AddParameter(param)
                .Send();

            GameManager.Instance.OnGameExit = () =>
                AdManager.Instance.ShowInterstitial(null, INTER_PLACE.AdGameOverInterstitial);
            
            LogEvent.I.Track()
                .Add("MaxWave", passDay)
                .Add("MaxLevel", DB.User.MaxChapter)
                .UserSet();
        }

        public void ProcessFightStart(Game game)
        {
            
        }

        public void ProcessFightEnd(Game game)
        {
            
        }

        private UniTask TryAddSkill(Game game, CancellationToken cancellationToken)
        {
            var chapterWave = game.ChapterWave;
            if (!(chapterWave.WaveSkillBoxes?.Count > 0)) 
                return UniTask.Yield(cancellationToken);
            var boxIndex = chapterWave.WaveSkillBoxes.Weight(box => box.Field2);
            var boxID = chapterWave.WaveSkillBoxes[boxIndex].Field1;
            return game.SkillSet.AddSkill(boxID, cancellationToken);
        }
        
        public UniTask GetPostFightTask(Game game, CancellationToken cancellationToken)
        {
            return TryAddSkill(game, cancellationToken);
        }

        public bool CheckRevivable(Game game)
        {
            return game.Tower.ResurrectionCount < game.ChapterDataSource.ResurrectionCount;
        }
        
        public void TrySaveGame(Game game)
        {
            LevelCreator.SaveGame(game, (int)_activityEndTime);
        }

        private AutoPauseLayerProcess _autoPauseLayerProcess = new ();
        public LayerContent ProcessGamePauseLayerShow(Game game)
        {
            return _autoPauseLayerProcess.ProcessGamePauseLayerShow(game);
        }

        public void ProcessGamePauseLayerHide(LayerContent layer, bool isExiting)
        {
            _autoPauseLayerProcess.ProcessGamePauseLayerHide(layer, isExiting);
        }

        public IEnumerable<int> Rules => _rules;
        public IEnumerable<GoalData> Goals => null;
        public string GamePlayLayerPath => 
            ConfigManager.Instance.GetConfig<GameViewConst>().DefaultGamePlayLayerPath;


        private readonly List<int> _globalTalents = new();
        public List<int> GlobalTalents => _globalTalents;

        private readonly List<PropertyData> _propertyData = new();
        public IEnumerable<PropertyData> PropertyData => _propertyData;
        public Dictionary<int, HeroData> ExtraHeroes => null;

        private void OnGameResultRespond(Dictionary<string, object> data)
        {
            LevelCreator.ClearSavedGame();
            
            ActivityChapterManager.Instance.AnalyzeActivityResultExpand(_activityConfig.Id, data);
            
            var update = CommonResponseDataHandle.UpdateDataResponse(data);

            this.DispatchEvent(
                Witness<GamePlayResultReceivedEvent>._,
                Mode,
                true,
                update?.hero,
                update?.bag,
                default(Dictionary<int, int>)
            );
        }

        private void OnGameResultFailed(int errorCode)
        {
            LevelCreator.ClearSavedGame();
            this.DispatchEvent(
                Witness<GamePlayResultReceivedEvent>._,
                Mode,
                false,
                default(List<CommonResponseDataHandle.HeroModel>), 
                default(List<CommonResponseDataHandle.ItemModel>), 
                default(Dictionary<int, int>));
        }
    }
}