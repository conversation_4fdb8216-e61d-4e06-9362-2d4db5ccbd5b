using System;
using System.Collections.Generic;
using System.Linq;
using App.Config;

namespace App.GamePlay.ChapterSource
{
    public class EliteChapterRewardSource : IChapterRewardSource
    {
        private readonly ChapterConfig _chapterConfig;
        private readonly EliteChapterWaveTable _chapterWaveTable;

        public EliteChapterRewardSource(int chapterID)
        {
            _chapterConfig = ConfigManager.Instance.GetConfig<ChapterConfigTable>().GetRowData(chapterID);
            _chapterWaveTable = ConfigManager.Instance.LoadTableCSV<EliteChapterWaveTable>(_chapterConfig.ChapterWave);
        }
        
        public (int, Dictionary<int, int>) GetChapterRewards(int day, bool victory)
        {
            var rewards = new Dictionary<int, int>();
            var cardNum = 0;
            var rewardDay = victory ? day : day - 1;
            //config reward
            foreach (var (_, chapterWave) in _chapterWaveTable.Rows.Where(configRow => 
                         configRow.Value.Day <= rewardDay))
            {
                foreach (var (key, value) in chapterWave.ItemCount)
                {
                    if (rewards.ContainsKey(key))
                        rewards[key] += value;
                    else
                        rewards.Add(key, value);
                }

                cardNum += chapterWave.CardCount;
            }

            //Check Max
            if (rewards.ContainsKey((int) Bag.Item.Money))
                rewards[(int) Bag.Item.Money] = Math.Min(rewards[(int) Bag.Item.Money], _chapterConfig.CoinMax);
            cardNum = Math.Min(cardNum, _chapterConfig.HeroCardMax);

            return (cardNum, rewards);
        }
    }
}