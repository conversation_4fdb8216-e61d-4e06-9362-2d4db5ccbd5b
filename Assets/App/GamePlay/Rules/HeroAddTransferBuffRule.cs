using System.Collections.Generic;
using App.GamePlay.Buff;
using App.GamePlay.Tiles.Module;
using UnityEngine;

namespace App.GamePlay.Rules
{
    public class HeroAddTransferBuffRule : RuleBase
    {
        protected readonly HeroAttributeField _field1;
        protected readonly float buffDuration1;
        protected readonly float buffValue1;
        
        protected readonly HeroAttributeField _field2;
        protected readonly float buffDuration2;
        protected readonly float buffValue2;

        protected readonly int _distinctID;
        
        public HeroAddTransferBuffRule(
            int typeParam, 
            int value,
            List<float> extraValues)
        {
            _field1 = (HeroAttributeField)extraValues[0];
            buffDuration1 = extraValues[1];
            buffValue1 = extraValues[2];
            
            _field2 = (HeroAttributeField)extraValues[3];
            buffDuration2 = extraValues[4];
            buffValue2 = extraValues[5];

            _distinctID = value;
        }

        protected override void Init()
        {
            RuleCenter.EventSubscriber.Subscribe<HeroCreatedEvent>(OnHeroCreated, RuleCenter.Game);
        }

        protected virtual void OnHeroCreated(HeroCreatedEvent evt)
        {
            var buffData = new HeroTransferBuff.Data()
            {
                Duration = buffDuration1,
                Field1 = _field1,
                Value1 = buffValue1,
                
                DurationTransfer = buffDuration2,
                FieldTransfer1 = _field2,
                ValueTransfer1 = buffValue2,
            };
            
            var displayData = new HeroBuffDisplayData
            {
                EffectPrefab = null,
                TintColor = Color.white
            };
            
            var hero = evt.Hero;
            hero.BuffSet.AddBuff<HeroTransferBuff, HeroTransferBuff.Data>(
                buffData,
                displayData,
                hero,
                _distinctID,
                HeroBuffType.AttackGain);
        }
    }
}