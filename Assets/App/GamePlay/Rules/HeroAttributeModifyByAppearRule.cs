using System.Linq;
using App.Config;
using App.GamePlay.Buff;
using App.GamePlay.Tiles.Module;

namespace App.GamePlay.Rules
{
    public class HeroAttributeModifyByAppearRule : HeroAttributeModifyRule
    {
        private readonly HeroConfigTable _heroConfigTable;
        
        public HeroAttributeModifyByAppearRule(int typeParam, float value, HeroAttributeField attributeField)
            : base(typeParam, value, attributeField)
        {
            _heroConfigTable = ConfigManager.Instance.GetConfig<HeroConfigTable>();
        }
        
        protected override void OnHeroCreated(HeroCreatedEvent evt)
        {
            if (_heroAttackAttributeFilter != AllTargetType &&
                evt.Hero.HeroConfig.AttackAttribute != _heroAttackAttributeFilter)
                return;

            var appearHeroes = evt.Hero.Game.AppearHeroes;
            var count = appearHeroes.Sum(hero =>
            {
                var config = _heroConfigTable.GetRowData(hero.Key);
                return _heroAttackAttributeFilter == AllTargetType ||
                       _heroAttackAttributeFilter == config.AttackAttribute
                    ? 1
                    : 0;
            });

            evt.Hero.BuffSet.ChangeAttribute(_field, _value * count);
        }
    }
}