namespace App.GamePlay.Rules
{
    public abstract class EnemyDeadProcessRule : RuleBase
    {
        protected override void Init()
        {
            RuleCenter.EventSubscriber.Subscribe<EnemyDeadEvent>(OnEnemyDeadEvent, RuleCenter.Game);
        }

        private void OnEnemyDeadEvent(EnemyDeadEvent evt)
        {
            Process(evt);
        }
        
        protected abstract void Process(EnemyDeadEvent evt);
    }
}