using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using XLua;

namespace LuaScript
{
    [RequireComponent(typeof(UnityEngine.UI.Button))]
    public class LuaToggle : LuaBehaviour
    {
        public void SetToggleGroup(LuaToggleGroup group)
        {
            var action = Get<Action<LuaTable>>(nameof(SetToggleGroup));
            action?.Invoke(group.ScriptEnv);
        }
        
        public void Select()
        {
            var action = GetAction(nameof(Select));
            action?.Invoke();
        }
    }
}