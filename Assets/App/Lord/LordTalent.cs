using Newtonsoft.Json;

namespace App.Lord
{
    public class LordTalent
    {
        [JsonProperty("normal")]
        private int _normalLevel;
        public int NormalLevel => _normalLevel;
        
        [JsonProperty("skill")]
        private int _skillLevel;
        public int SkillLevel => _skillLevel;

        public LordTalent()
        {
            
        }

        public void SetNormalLevel(int level)
        {
            _normalLevel = level;
        }

        public void SetSkillLevel(int level)
        {
            _skillLevel = level;
        }
    }
}
