using Newtonsoft.Json;

namespace App.Lord
{
    public class LordEquip
    {   
        [JsonProperty("confId")]
        private int _configId;
        public int ConfigId => _configId;
        
        [JsonProperty("lv")]
        private int _level;
        public int Level => _level;

        public LordEquip()
        {
            
        }
        
        public LordEquip(
            int configId,
            int level)
        {
            _configId = configId;
            _level = level;
        }

        public void UpdateEquipLevel(int level)
        {
            _level = level;
        }

         public void UpdateEquipConfigId(int configId)
        {
            _configId = configId;
        }
    }   
}
