using System.Collections.Generic;
using System.Linq;
using App.GamePlay.ChapterSource;
using App.GamePlay;
using UnityEngine;
using App.Config;

namespace App.Lord
{
    public static class LordUtility
    {
        public enum AdditionalKey
        {
            HeroAmount = 1,
            LordAmount = 2,
        }
        
        public static (LordData, List<int>) GetLordData(
            int id, 
            int levelMode, 
            bool initFullEnergy)
        {
            if (id <= 0)
                return (new LordData(), new List<int>());
            var lordInfo = DB.Lords.GetLordById(id);

            var equips = DB.Lords.GetLordById(id).LordEquips;
            
            var equipInfos = equips.Select(guid => DB.Lords.GetEquipByGuid(guid))
                .ToDictionary(info => info.ConfigId, info => info.Level);
            
            var additional = new Dictionary<int, int>()
            {
                { (int)AdditionalKey.HeroAmount, DB.Hero.GetHeroCountByLevel(1) },
                { (int)AdditionalKey.<PERSON>, DB.Lords.GetLordCount() }
            };
            
            return GetLordData(id, 
                lordInfo.Level, 
                lordInfo.Star, 
                equipInfos, 
                DB.Lords.Talent.NormalLevel,
                DB.Lords.Talent.SkillLevel,
                levelMode,
                initFullEnergy, 
                additional);
        }

        public static (LordData, List<int>) GetLordData(
            int id, 
            int level, 
            int star, 
            Dictionary<int, int> equips,
            int normalTalentLevel, 
            int skillTalentLevel, 
            int levelMode, 
            bool initFullEnergy, 
            Dictionary<int, int> additionalValues)
        {
            if (id <= 0)
                return (new LordData(), new List<int>());
            
            var attributes = new Dictionary<int, float>();
            var skillList = new List<int>();
            var ruleList = new List<int>();
            
            AddLordRoleAttributes(attributes, id, level, star);
            AddLordRoleSkills(skillList, id);
            AddLordRoleSkillAttributes(attributes, id, level, star, additionalValues);
            AddLordEquipAttributesAndRule(attributes, ruleList, levelMode, equips);
            AddLordTalentAttributesAndRule(attributes, ruleList, levelMode, normalTalentLevel, skillTalentLevel);
            
            return (LordData.Create(id, attributes, skillList, initFullEnergy), ruleList);
        }

        public static void AddLordEquipAttributesAndRule(
            Dictionary<int, float> attributes,
            List<int> ruleList,
            int levelMode,
            Dictionary<int, int> srcEquips)
        {
            foreach (var (id, level) in srcEquips)
            {
                var equipConfig = ConfigManager.Instance.GetConfig<LordEquipConfigTable>()
                    .GetRowData(id);

                foreach (var attributeStr in equipConfig.Attributes)
                {
                    var attributeInfo = attributeStr.Split(':');
                    var attributeId = attributeInfo[0].ToInt();
                    var attributeDefaultValue = attributeInfo[1].ToFloat();
                    var attributeAddValue = attributeInfo[2].ToFloat();
                    var addValue = attributeDefaultValue + level * attributeAddValue - attributeAddValue;
                    if (attributes.TryGetValue(attributeId, out float value))
                        attributes[attributeId] = value + addValue;
                    else
                        attributes.Add(attributeId, addValue);
                }

                foreach (var (key, ruleId) in equipConfig.LordRules)
                {
                    if (key > equipConfig.Quality) continue;
                    var ruleConfig = ConfigManager.Instance.GetConfig<RuleConfigTable>().GetRowData(ruleId);
                    if (ruleConfig == null)
                        continue;
                    if (ruleConfig.EffectiveMode.Contains(levelMode))
                        ruleList.Add(ruleId);
                }
            }
        }

        public static void AddLordEquipAttributesAndRule(
            Dictionary<int, float> attributes,
            int id,
            List<int> ruleList,
            int levelMode)
        {
            var equips = DB.Lords.GetLordById(id).LordEquips;

            var equipInfos = equips.Select(guid => DB.Lords.GetEquipByGuid(guid))
                .ToDictionary(info => info.ConfigId, info => info.Level);

            AddLordEquipAttributesAndRule(attributes, ruleList, levelMode, equipInfos);
        }

        //领主自身基础属性

        public static void AddLordRoleAttributes(Dictionary<int, float> attributeMap, int id, int level, int star)
        {
            if (level <= 0)
                return;

            var config = ConfigManager.Instance.GetConfig<LordConfigTable>().GetRowData(id);
            var levelConfig = ConfigManager.Instance.LordLevelRef.GetLevelConfig(id, level);
            var starLevelConfig = ConfigManager.Instance.LordStarLevelRef.GetStarLevelConfig(config.Quality, star);
            var lordConfig = ConfigManager.Instance.GetConfig<LordConfigTable>().GetRowData(id);

            //攻击
            var attributeEnum = (int)LordAttributeField.DamageValue;
            if (attributeMap.TryGetValue(attributeEnum, out var attributeValue))
                attributeMap[attributeEnum] = attributeValue +
                                              Mathf.Floor(levelConfig.AttackValue *
                                                          (1 + starLevelConfig.AttributeUp));
            else
                attributeMap[attributeEnum] =
                    Mathf.Floor(levelConfig.AttackValue * (1 + starLevelConfig.AttributeUp));

            //血量
            attributeEnum = (int)LordAttributeField.TowerHpValue;
            if (attributeMap.TryGetValue(attributeEnum, out attributeValue))
                attributeMap[attributeEnum] = attributeValue +
                                              Mathf.Floor(levelConfig.HpValue * (1 + starLevelConfig.AttributeUp));
            else
                attributeMap[attributeEnum] = Mathf.Floor(levelConfig.HpValue * (1 + starLevelConfig.AttributeUp));

            //能量上限
            attributeEnum = (int)LordAttributeField.EnergyCountValue;
            if (attributeMap.TryGetValue(attributeEnum, out attributeValue))
                attributeMap[attributeEnum] = attributeValue + lordConfig.Energy;
            else
                attributeMap[attributeEnum] = lordConfig.Energy;
        }

        public static void AddLordRoleAttributes(Dictionary<int, float> attributeMap, int id)
        {
            var lord = DB.Lords.GetLordById(id);
            if (lord != null)
            {
                AddLordRoleAttributes(attributeMap, id, lord.Level, lord.Star);
            }
        }

        //领主主动技能
        public static void AddLordRoleSkills(List<int> skillList, int id)
        {
            var config = ConfigManager.Instance.GetConfig<LordConfigTable>().GetRowData(id);
            skillList.AddRange(from skillID in config.Skills
                select ConfigManager.Instance.LordSkillRef.GetCurrentSkillValueConfig(id, skillID)
                into skillValueConfig
                where skillValueConfig != null
                where skillValueConfig.SkillField > 0
                select skillValueConfig.Id);
        }

        //领主被动技能属性
        public static void AddLordRoleSkillAttributes(
            Dictionary<int, float> attributeMap, 
            int id, 
            int level, 
            int star,
            Dictionary<int, int> additionalValues)
        {
            var config = ConfigManager.Instance.GetConfig<LordConfigTable>().GetRowData(id);
            foreach (var attribute in from skillID in config.Skills
                     select GetEffectiveSkillValueConfig(level, star, skillID)
                     into skillValueConfig
                     where skillValueConfig != null
                     where skillValueConfig.Attribute != null
                     from attribute in skillValueConfig.Attribute
                     select attribute)
            {
                AddLordAttributeValue(attributeMap, attribute.Key, attribute.Value, id, level,  additionalValues);
            }
        }

        public static void AddLordRoleSkillAttributes(Dictionary<int, float> attributeMap, int id)
        {
            var lord = DB.Lords.GetLordById(id);
            if (lord != null)
            {
                AddLordRoleSkillAttributes(attributeMap, id, lord.Level, lord.Star, new Dictionary<int, int>()
                {
                    {(int)AdditionalKey.HeroAmount, DB.Hero.GetHeroCountByLevel(1)},
                    {(int)AdditionalKey.LordAmount, DB.Lords.GetLordCount()}
                });
            }
        }


        public static void AddLordTalentAttributesAndRule(
            Dictionary<int, float> attributes,
            List<int> ruleList,
            int levelMode,
            int normalLevel,
            int skillLevel
        )
        {
            var configs = ConfigManager.Instance.GetConfig<LordTalentConfigTable>().Rows;
            foreach (var config in configs)
            {
                if (config.Value.Type == 1)
                {
                    if (normalLevel < config.Value.Id)
                        continue;
                    foreach (var attributeInfo in config.Value.LordTalent)
                    {
                        if (attributes.TryGetValue(attributeInfo.Key, out var value))
                        {
                            attributes[attributeInfo.Key] = value + attributeInfo.Value;
                        }
                        else
                        {
                            attributes.Add(attributeInfo.Key, attributeInfo.Value);
                        }
                    }
                }
                else
                {
                    if (skillLevel < config.Value.Id) continue;
                    var ruleId = config.Value.Rule;
                    var ruleConfig = ConfigManager.Instance.GetConfig<RuleConfigTable>().GetRowData(ruleId);
                    if (ruleConfig == null)
                        continue;
                    if (ruleConfig.EffectiveMode.Contains(levelMode))
                        ruleList.Add(ruleId);
                }
            }
        }

        public static void AddLordTalentAttributesAndRule(
            Dictionary<int, float> attributes,
            List<int> ruleList,
            int levelMode)
        {
            AddLordTalentAttributesAndRule(attributes, ruleList, levelMode, DB.Lords.Talent.NormalLevel,
                DB.Lords.Talent.SkillLevel);
        }

        public static LordSkillValueConfig GetEffectiveSkillValueConfig(int lordLevel, int lordStar, int skillId)
        {
            var skillList = ConfigManager.Instance.LordSkillRef.GetAllSkillValueConfig(skillId);
            if (skillList == null)
                return null;

            var level = 0;
            switch(skillList[0].UnlockType)
            {
                case 1:
                {
                    level = lordLevel;
                };break;
                case 2:
                {
                    level = lordStar;
                };break;
            }

            LordSkillValueConfig config = null;
            foreach (var t in skillList)
            {
                if (t.UnlockParam <= level)
                    config = t;
                else
                    break;
            }

            return config;
        }

        public static void AddAttributeValue(Dictionary<int, float> attributeMap, int key, float value)
        {
            if (attributeMap.TryGetValue(key, out var attributeValue))
                attributeMap[key] = attributeValue + value;
            else
                attributeMap[key] = value;
    
        }
        

        public static void AddLordAttributeValue(
            Dictionary<int, float> attributeMap, 
            int key, 
            float value, 
            int lordID, 
            int lordLevel, 
            Dictionary<int, int> additionalValues)
        {
            switch(key)
            {
                case (int)LordAttributeField.HeroAmountDamageAndHpValue://英雄数量对攻击、血量加成
                {
                    var levelConfig = ConfigManager.Instance.LordLevelRef.GetLevelConfig(lordID, lordLevel);
                    //var attributeValue = DB.Hero.GetHeroCountByLevel(1) * value;
                    var attributeValue = additionalValues.GetValueOrDefault((int)AdditionalKey.HeroAmount) * value;
                    AddAttributeValue(attributeMap, (int)LordAttributeField.DamageValue,  Mathf.Floor(attributeValue * levelConfig.AttackValue));
                    AddAttributeValue(attributeMap, (int)LordAttributeField.TowerHpValue,  Mathf.Floor(attributeValue * levelConfig.HpValue));
                };break;
                case (int)LordAttributeField.LordAmountDamageAndHpValue://领主数量对攻击、血量加成
                {
                    var levelConfig = ConfigManager.Instance.LordLevelRef.GetLevelConfig(lordID, lordLevel);
                    //var attributeValue = DB.Lords.GetLordCount() * value;
                    var attributeValue = additionalValues.GetValueOrDefault((int)AdditionalKey.LordAmount) * value;
                    AddAttributeValue(attributeMap, (int)LordAttributeField.DamageValue,  Mathf.Floor(attributeValue * levelConfig.AttackValue));
                    AddAttributeValue(attributeMap, (int)LordAttributeField.TowerHpValue,  Mathf.Floor(attributeValue * levelConfig.HpValue));
                };break;
                default:
                {
                    AddAttributeValue(attributeMap, key, value);
                };break;
            }
        }
    }
}