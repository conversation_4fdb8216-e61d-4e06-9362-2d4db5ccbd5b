using System.Collections.Generic;
using Newtonsoft.Json;

namespace  App.Lord
{
    public class Lord
    {
        [JsonProperty("equips")]
        private List<int> _lordEquips;
        public List<int> LordEquips => _lordEquips;
        
        [JsonProperty("star")]
        private int _star;
        public int Star => _star;

        [JsonProperty("lv")]
        private int _level;
        public int Level => _level;

        public Lord()
        {
            
        }
        
        public Lord(
            int star,
            int lv,
            List<int> equips)
        {
            _star = star;
            _level = lv;
            _lordEquips = equips;
        }
        
        public void UpdateStar(int star)
        {
            _star = star;
        }

        public void UpdateLevel(int lv)
        {
            _level = lv;
        }

        public void UpdateEquips(List<int> equips)
        {
            _lordEquips = equips;
        }
    }   
}
