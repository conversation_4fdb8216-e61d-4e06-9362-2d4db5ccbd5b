using App.Bag;
using FQDev.AssetBundles;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace App.UI
{
    public class CommonGiftItem : MonoBehaviour
    {
        [SerializeField] private CommonItem _commonItem;
        [SerializeField] private CommonItemHero _commonItemHero;


        public void FreshRewardUI(RewardItem rewardItem)
        {
            // FreshRewardUI(rewardItem.Type, rewardItem.Id, rewardItem.Count, rewardItem.RangeMax);
            if (rewardItem.Type == RewardType.Item)
            {
                if (rewardItem.Id <= (int) Item.HeroCardStart || rewardItem.Id > (int) Item.HeroCardEnd)//临时处理英雄ID封顶为199
                {
                    _commonItem.gameObject.SetActive(true);
                    _commonItemHero.gameObject.SetActive(false);
                    _commonItem.FreshRewardUI(rewardItem);
                }
                else
                {
                    _commonItem.gameObject.SetActive(false);
                    _commonItemHero.gameObject.SetActive(true);
                    _commonItemHero.FreshRewardUI(rewardItem);
                }
            }
            else if (rewardItem.Type == RewardType.Frame)
            {
                _commonItem.gameObject.SetActive(true);
                _commonItemHero.gameObject.SetActive(false);
                _commonItem.FreshRewardUI(rewardItem);                
            }
        }
    }
}