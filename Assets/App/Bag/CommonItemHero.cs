using System;
using System.Linq;
using App.Bag;
using App.Config;
using Language;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace App.UI
{
    public class CommonItemHero : MonoBehaviour
    {
        [SerializeField] private TextMeshProUGUI _textCount;
        [SerializeField] private TextMesh<PERSON><PERSON>UGUI _textName;
        [SerializeField] private TextMeshProUGUI _textCountProgress;
        [SerializeField] private Image _imageNormalBg;
        [SerializeField] private Image _imageIconBg;
        [SerializeField] private Image _imageIcon;
        [SerializeField] private Image _imageQuality;
        [SerializeField] private Image _imageProgress;
        [SerializeField] private GameObject _nodeUpgradeProgress;
        [SerializeField] private GameObject _objProgress;
        [SerializeField] private GameObject _objNew;
        [SerializeField] private GameObject _maxNode;
        [SerializeField] private TextMeshProUGUI _maxLevel_CardCount;

        private RewardItem _rewardItem;

        public void FreshRewardUI(RewardItem rewardItem)
        {
            _rewardItem = rewardItem;
            var heroConfig = ConfigManager.Instance.GetConfig<HeroConfigTable>().Rows
                .FirstOrDefault(a => a.Value.LevelUpItemId == rewardItem.Id).Value;
            if (heroConfig != null)
            {
                if(_textName)_textName.SetLang(heroConfig.Name);
                if(_textCount)_textCount.text = $"X{rewardItem.Count}";

                var heroQualityConfig = ConfigManager.Instance.HeroQualityRef[heroConfig.Id];
                if (heroQualityConfig != null)
                {
                    _imageIconBg.sprite = heroQualityConfig.IconBg.Load<Sprite>();
                    _imageNormalBg.sprite = heroQualityConfig.Background.Load<Sprite>();

                    if (heroQualityConfig.Quality != null)
                    {
                        _imageQuality.gameObject.SetActive(true);
                        if (heroConfig.QualityPlus != null && heroConfig.QualityPlus > 0)
                        {
                            _imageQuality.sprite = heroQualityConfig.QualityPlus.Load<Sprite>();
                        }
                        else
                            _imageQuality.sprite = heroQualityConfig.Quality.Load<Sprite>();

                        _imageQuality.SetNativeSize();
                    }
                    else
                        _imageQuality.gameObject.SetActive(false);
                }

                if (DB.Hero.Contents.TryGetValue(heroConfig.Id, out var level))
                {
                    _imageIcon.sprite = ConfigManager.Instance.HeroGradeRef.GetHeroSpriteByLevel(heroConfig.Id, level);

                    if (_objNew) _objNew.SetActive(false);
                    if (_objProgress)
                    {
                        var heroMaxLevel = ConfigManager.Instance.HeroLevelRef.GetHeroMaxLevel(heroConfig.Id);
                        level = Math.Max(1, level);
                        if (level == heroMaxLevel)
                        {
                            _objProgress.SetActive(false);
                            if (_maxNode)
                                _maxNode.SetActive(true);
                            if (_maxLevel_CardCount)
                                _maxLevel_CardCount.SetLang("Heros_MaxLevel");
                        }
                        else
                        {
                            if (_maxNode)
                                _maxNode.SetActive(false);

                            var levelConfig = level > heroMaxLevel
                                ? null
                                : ConfigManager.Instance.HeroLevelRef[heroConfig.Id, level];
                            if (levelConfig != null)
                            {
                                _objProgress.SetActive(true);
                                levelConfig.Costs.TryGetValue(heroConfig.LevelUpItemId, out var cardCostCount);
                                var count = BagManager.Instance.GetItemCount(heroConfig.LevelUpItemId);
                                if (cardCostCount > 0)
                                {
                                    _imageProgress.fillAmount = (float) count / cardCostCount;
                                    _textCountProgress.text = $"{count}/{cardCostCount}";
                                    _nodeUpgradeProgress.gameObject.SetActive(count >= cardCostCount);
                                }
                                else
                                {
                                    _textCountProgress.text = $"{count}";
                                    _nodeUpgradeProgress.gameObject.SetActive(false);
                                }
                            }
                            else
                            {
                                _objProgress.SetActive(false);
                            }
                        }
                    }
                }
                else
                {
                    _imageIcon.sprite = ConfigManager.Instance.HeroGradeRef.GetHeroSpriteByLevel(heroConfig.Id, 1);

                    if (_objNew) _objNew.SetActive(true);
                    if (_objProgress) _objProgress.SetActive(false);
                }
            }
        }

        public void OnClickShowTips()
        {
            if (_rewardItem == null || _rewardItem.Type == RewardType.None)
                return;

            CommonItemTipsUtil.ShowTips(_rewardItem);
        }
    }
}