using System;
using System.Collections.Generic;
using App.Config;
using App.UI;
using FQDev.EventSystem;
using FQDev.Singleton;
namespace App.Player
{
    public class PlayerManager : Singleton<PlayerManager>, IEventSender
    {
        public int LevelUpNumber { get; set; }//升级数暂存

        public void DBUpdatePlayerLv(int newPlayerLv)
        {
            var origin = DB.User.PlayerLv;
            DB.User.PlayerLv = newPlayerLv;
            LogEvent.I.Track().Add("MaxPlayerlv", newPlayerLv).UserSet();
            // this.DispatchEvent(
            //     Witness<PlayerLevelUpdatedEvent>._,
            //     DB.User.PlayerLv,
            //     origin);
        }

        public void SendLvUpEvent()
        {
            this.DispatchEvent(
                Witness<PlayerLevelUpdatedEvent>._,
                DB.User.PlayerLv,
                DB.User.PlayerLv - LevelUpNumber);
        }

        public Dictionary<int, int> GetLvUpRewards(int curLv, int oldLv)
        {
            var result = new Dictionary<int, int>();
            if (LevelUpNumber <= 0) return result;
            var table = ConfigManager.Instance.GetConfig<UserLevelConfigTable>();
            for (var i = oldLv; i < curLv; i++)
            {
                var config = table.GetRowData(i);
                foreach (var reward in config.AwardKey)
                {
                    if (result.ContainsKey(reward.Id))
                    {
                        result[reward.Id] += reward.Count;
                    }
                    else
                    {
                        result.Add(reward.Id, reward.Count);
                    }
                }
            }
            return result;
        }

        public int GetLvUpCost(int curLv, int oldLv)
        {
            var result = 0;
            var table = ConfigManager.Instance.GetConfig<UserLevelConfigTable>();
            for (var i = oldLv; i < curLv; i++)
            {
                var config = table.GetRowData(i);
                result += config.Exp;
            }

            return result;
        }

        public void ShowLevelUpDialog(int playerLevel, int prePlayerLevel, Action action)
        {
            for (var i = playerLevel; i > prePlayerLevel; i--)
            {
                var config = ConfigManager.Instance.GetConfig<UserLevelConfigTable>().GetRowData(i);
                var dialog = UserLevelUpRewardDialog.Create(config);
                if (i == playerLevel)
                    dialog.OnCloseCallBack = action;
            }
            LevelUpNumber = 0;
        }

        public EventDispatcher Dispatcher => EventDispatcher.Global;
    }

    public class PlayerLevelUpdatedEvent : EventBase<int, int>
    {
        public int Current => Field1;
        public int Previous => Field2;
    }
}


