//-----------------------------------------
//<auto-generated>
//    This code was generated by a tool.
//    Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
//</auto-generated>
//------------------------------------------
// Generated from :TalentConfig.csv

using System.Collections.Generic;
using System.IO;
using System.Text;

public partial class TalentConfig
{
    private int __Id;
    public int Id
    {
        internal set{ __Id = value; }
        get{ return __Id; }
    }
    private int __HeroId;
    public int HeroId
    {
        internal set{ __HeroId = value; }
        get{ return __HeroId; }
    }
    private int __Level;
    public int Level
    {
        internal set{ __Level = value; }
        get{ return __Level; }
    }
    private AssetRef __Icon;
    public AssetRef Icon
    {
        internal set{ __Icon = value; }
        get{ return __Icon; }
    }
    public List<int> NextTalentId = new List<int>();
    private string __Desc;
    public string Desc
    {
        internal set{ __Desc = value; }
        get{ return __Desc; }
    }
}

public class TalentConfigTable : FQDev.CSVConfig.ConfigBase
{
    static string __config_file_name = "TalentConfig";
    public static string ConfigName => __config_file_name;
    static int __col_count = 6;

    private readonly Dictionary<int,TalentConfig> __rows = new Dictionary<int,TalentConfig>();
    public Dictionary<int,TalentConfig> Rows => __rows;
    // key = TalentConfig.Id
    public TalentConfig GetRowData( int key )
    {
        TalentConfig tmp; 
        if( !__rows.TryGetValue( key, out tmp ) )
            LogError("Can't get row data by key:{0} Config:{1}", key, __config_file_name);
        return tmp;
    }

    public int GetRowCount()
    {
        return __rows.Count;
    }

    public override void ParseConfigure( string text )
    {

        __rows.Clear();
        string splace_text = text.Replace("\r","");
        string[] lines = splace_text.Split(new string[]{"\n"}, System.StringSplitOptions.None);
        
        foreach( string line in lines )
        {
            if( line.Length == 0 )
                continue;
            if( line[0] == '\0' ||line[0] == '#' || line[0] == '%' || line[0] == '$')
                continue;


            string[] rowdata = line.Split(new string[]{","}, System.StringSplitOptions.None);
            if( rowdata.Length != __col_count )
            {
                LogError(string.Format("{0}列数与原配置不符", __config_file_name));
                return;
            }
            int counter = 0;
            TalentConfig tmp = new TalentConfig();

            string strId = rowdata[counter++];
            if (!string.IsNullOrEmpty(strId))
                tmp.Id = int.Parse(strId);

            string strHeroId = rowdata[counter++];
            if (!string.IsNullOrEmpty(strHeroId))
                tmp.HeroId = int.Parse(strHeroId);

            string strLevel = rowdata[counter++];
            if (!string.IsNullOrEmpty(strLevel))
                tmp.Level = int.Parse(strLevel);
            tmp.Icon = AssetRef.Parse(rowdata[counter++]);
            do{
                string[] llc = rowdata[counter++].Split(new string[]{"|"} ,System.StringSplitOptions.None);
                foreach( string vvv in llc )
                {
                    if(vvv.Length == 0 ) 
                        continue;
                    tmp.NextTalentId.Add( int.Parse(vvv) );
                }
            }while(false);
            tmp.Desc = rowdata[counter++];
            __rows.Add( tmp.Id, tmp );
        }
    }
}