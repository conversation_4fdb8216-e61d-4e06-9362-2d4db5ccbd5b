//-----------------------------------------
//<auto-generated>
//    This code was generated by a tool.
//    Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
//</auto-generated>
//------------------------------------------
// Generated from :FragmentExchangeConst.csv

using System.Collections.Generic;
using System.IO;
using System.Text;

public class FragmentExchangeConst : FQDev.CSVConfig.ConfigBase
{
    /// <summary>
    /// 多少张万能碎片转化成1张橙卡
    /// </summary>
    public RewardItem UniversalCVROrangeItem => __UniversalCVROrangeItem;
    private RewardItem __UniversalCVROrangeItem;

    /// <summary>
    /// 多少张万能碎片转化成1张紫卡
    /// </summary>
    public RewardItem UniversalCVRPurpleItem => __UniversalCVRPurpleItem;
    private RewardItem __UniversalCVRPurpleItem;


    public override void ParseConfigure( string text )
    {
        var lines = text.Replace("\r", "")
            .Split(new[] {"\n"}, System.StringSplitOptions.None);

        var rawData = new Dictionary<string, string>();
        foreach (var line in lines)
        {
            if( line.Length == 0 )
                continue;
            if( line[0] == '\0' ||line[0] == '#' || line[0] == '%' || line[0] == '$')
                continue;

            var rows = line.Split(new[] {","}, System.StringSplitOptions.None);
            if (rows.Length < 3)
            {
                ErrorLogger?.Invoke(string.Format("CommonConst列数不符"));
                return;
            }
        
            rawData.Add(rows[0], rows[1]);
        }
        
        var raw = "";
        if (rawData.TryGetValue("UniversalCVROrangeItem", out raw))
        {
            __UniversalCVROrangeItem = RewardItem.Parse(raw);
        }
        if (rawData.TryGetValue("UniversalCVRPurpleItem", out raw))
        {
            __UniversalCVRPurpleItem = RewardItem.Parse(raw);
        }
    }
}