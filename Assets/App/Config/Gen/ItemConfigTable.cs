//-----------------------------------------
//<auto-generated>
//    This code was generated by a tool.
//    Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
//</auto-generated>
//------------------------------------------
// Generated from :ItemConfig.csv

using System.Collections.Generic;
using System.IO;
using System.Text;

public partial class ItemConfig
{
    private int __Id;
    public int Id
    {
        internal set{ __Id = value; }
        get{ return __Id; }
    }
    private int __Type;
    public int Type
    {
        internal set{ __Type = value; }
        get{ return __Type; }
    }
    private int __Quality;
    public int Quality
    {
        internal set{ __Quality = value; }
        get{ return __Quality; }
    }
    private int __RandomCardQuality;
    public int RandomCardQuality
    {
        internal set{ __RandomCardQuality = value; }
        get{ return __RandomCardQuality; }
    }
    private string __Remark;
    public string Remark
    {
        internal set{ __Remark = value; }
        get{ return __Remark; }
    }
    private string __Name;
    public string Name
    {
        internal set{ __Name = value; }
        get{ return __Name; }
    }
    private AssetRef __Icon;
    public AssetRef Icon
    {
        internal set{ __Icon = value; }
        get{ return __Icon; }
    }
    private int __LinkType;
    public int LinkType
    {
        internal set{ __LinkType = value; }
        get{ return __LinkType; }
    }
    private string __LinkParam;
    public string LinkParam
    {
        internal set{ __LinkParam = value; }
        get{ return __LinkParam; }
    }
    private string __GetSource;
    public string GetSource
    {
        internal set{ __GetSource = value; }
        get{ return __GetSource; }
    }
    private AssetRef __Background;
    public AssetRef Background
    {
        internal set{ __Background = value; }
        get{ return __Background; }
    }
    private string __Description;
    public string Description
    {
        internal set{ __Description = value; }
        get{ return __Description; }
    }
}

public class ItemConfigTable : FQDev.CSVConfig.ConfigBase
{
    static string __config_file_name = "ItemConfig";
    public static string ConfigName => __config_file_name;
    static int __col_count = 12;

    private readonly Dictionary<int,ItemConfig> __rows = new Dictionary<int,ItemConfig>();
    public Dictionary<int,ItemConfig> Rows => __rows;
    // key = ItemConfig.Id
    public ItemConfig GetRowData( int key )
    {
        ItemConfig tmp; 
        if( !__rows.TryGetValue( key, out tmp ) )
            LogError("Can't get row data by key:{0} Config:{1}", key, __config_file_name);
        return tmp;
    }

    public int GetRowCount()
    {
        return __rows.Count;
    }

    public override void ParseConfigure( string text )
    {

        __rows.Clear();
        string splace_text = text.Replace("\r","");
        string[] lines = splace_text.Split(new string[]{"\n"}, System.StringSplitOptions.None);
        
        foreach( string line in lines )
        {
            if( line.Length == 0 )
                continue;
            if( line[0] == '\0' ||line[0] == '#' || line[0] == '%' || line[0] == '$')
                continue;


            string[] rowdata = line.Split(new string[]{","}, System.StringSplitOptions.None);
            if( rowdata.Length != __col_count )
            {
                LogError(string.Format("{0}列数与原配置不符", __config_file_name));
                return;
            }
            int counter = 0;
            ItemConfig tmp = new ItemConfig();

            string strId = rowdata[counter++];
            if (!string.IsNullOrEmpty(strId))
                tmp.Id = int.Parse(strId);

            string strType = rowdata[counter++];
            if (!string.IsNullOrEmpty(strType))
                tmp.Type = int.Parse(strType);

            string strQuality = rowdata[counter++];
            if (!string.IsNullOrEmpty(strQuality))
                tmp.Quality = int.Parse(strQuality);

            string strRandomCardQuality = rowdata[counter++];
            if (!string.IsNullOrEmpty(strRandomCardQuality))
                tmp.RandomCardQuality = int.Parse(strRandomCardQuality);
            tmp.Remark = rowdata[counter++];
            tmp.Name = rowdata[counter++];
            tmp.Icon = AssetRef.Parse(rowdata[counter++]);

            string strLinkType = rowdata[counter++];
            if (!string.IsNullOrEmpty(strLinkType))
                tmp.LinkType = int.Parse(strLinkType);
            tmp.LinkParam = rowdata[counter++];
            tmp.GetSource = rowdata[counter++];
            tmp.Background = AssetRef.Parse(rowdata[counter++]);
            tmp.Description = rowdata[counter++];
            __rows.Add( tmp.Id, tmp );
        }
    }
}