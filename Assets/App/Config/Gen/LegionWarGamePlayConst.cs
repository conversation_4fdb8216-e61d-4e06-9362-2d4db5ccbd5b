//-----------------------------------------
//<auto-generated>
//    This code was generated by a tool.
//    Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
//</auto-generated>
//------------------------------------------
// Generated from :LegionWarGamePlayConst.csv

using System.Collections.Generic;
using System.IO;
using System.Text;

public class LegionWarGamePlayConst : FQDev.CSVConfig.ConfigBase
{
    /// <summary>
    /// 最少需要1个英雄进攻才能开始
    /// </summary>
    public int AttackHeroMin => __AttackHeroMin;
    private int __AttackHeroMin;

    /// <summary>
    /// 兵营冷却时间倍率
    /// </summary>
    public float BarrackCoolingTimeFactor => __BarrackCoolingTimeFactor;
    private float __BarrackCoolingTimeFactor;

    /// <summary>
    /// 章节ID
    /// </summary>
    public int ChapterId => __ChapterId;
    private int __ChapterId;

    /// <summary>
    /// 战斗失败成员50%代币奖励倍率
    /// </summary>
    public float DefeatTokenRate => __DefeatTokenRate;
    private float __DefeatTokenRate;

    /// <summary>
    /// 防守队伍基础奖励，每个编队20%代币奖励
    /// </summary>
    public float DefenseParticipationRate => __DefenseParticipationRate;
    private float __DefenseParticipationRate;

    /// <summary>
    /// 防守成功奖励递减系数（次数：递减比例）
    /// </summary>
    public Dictionary<int, float> DefenseRewardDecrease => __DefenseRewardDecrease;
    private readonly Dictionary<int, float> __DefenseRewardDecrease = new Dictionary<int, float>();

    /// <summary>
    /// 成功防守队伍奖励，10%代币奖励×成功次数
    /// </summary>
    public float DefenseSuccessRate => __DefenseSuccessRate;
    private float __DefenseSuccessRate;

    /// <summary>
    /// 轻度疲劳敌方RuleId
    /// </summary>
    public List<int> FatigueRivalRules1 => __FatigueRivalRules1;
    private readonly List<int> __FatigueRivalRules1 = new List<int>();

    /// <summary>
    /// 中度疲劳敌方RuleId
    /// </summary>
    public List<int> FatigueRivalRules2 => __FatigueRivalRules2;
    private readonly List<int> __FatigueRivalRules2 = new List<int>();

    /// <summary>
    /// 重度疲劳敌方RuleId
    /// </summary>
    public List<int> FatigueRivalRules3 => __FatigueRivalRules3;
    private readonly List<int> __FatigueRivalRules3 = new List<int>();

    /// <summary>
    /// 极度疲劳敌方RuleId
    /// </summary>
    public List<int> FatigueRivalRules4 => __FatigueRivalRules4;
    private readonly List<int> __FatigueRivalRules4 = new List<int>();

    /// <summary>
    /// 轻度疲劳己方RuleId
    /// </summary>
    public List<int> FatigueSelfRules1 => __FatigueSelfRules1;
    private readonly List<int> __FatigueSelfRules1 = new List<int>();

    /// <summary>
    /// 中度疲劳己方RuleId
    /// </summary>
    public List<int> FatigueSelfRules2 => __FatigueSelfRules2;
    private readonly List<int> __FatigueSelfRules2 = new List<int>();

    /// <summary>
    /// 重度疲劳己方RuleId
    /// </summary>
    public List<int> FatigueSelfRules3 => __FatigueSelfRules3;
    private readonly List<int> __FatigueSelfRules3 = new List<int>();

    /// <summary>
    /// 极度疲劳己方RuleId
    /// </summary>
    public List<int> FatigueSelfRules4 => __FatigueSelfRules4;
    private readonly List<int> __FatigueSelfRules4 = new List<int>();

    /// <summary>
    /// 战斗RuleId
    /// </summary>
    public List<int> FightRules => __FightRules;
    private readonly List<int> __FightRules = new List<int>();

    /// <summary>
    /// 战斗时间
    /// </summary>
    public int FightTime => __FightTime;
    private int __FightTime;

    /// <summary>
    /// 进攻英雄怪物攻击成长速率
    /// </summary>
    public List<float> HeroMonsterATKFactor => __HeroMonsterATKFactor;
    private readonly List<float> __HeroMonsterATKFactor = new List<float>();

    /// <summary>
    /// 英雄怪物攻击成长参数
    /// </summary>
    public float HeroMonsterAtkGrowParam => __HeroMonsterAtkGrowParam;
    private float __HeroMonsterAtkGrowParam;

    /// <summary>
    /// 进攻英雄怪物攻击极限成长倍率
    /// </summary>
    public List<float> HeroMonsterATKLimit => __HeroMonsterATKLimit;
    private readonly List<float> __HeroMonsterATKLimit = new List<float>();

    /// <summary>
    /// 进攻英雄怪物成长到极限值所需时间
    /// </summary>
    public List<int> HeroMonsterGrowthTime => __HeroMonsterGrowthTime;
    private readonly List<int> __HeroMonsterGrowthTime = new List<int>();

    /// <summary>
    /// 进攻英雄怪物生命成长速率
    /// </summary>
    public List<float> HeroMonsterHPFactor => __HeroMonsterHPFactor;
    private readonly List<float> __HeroMonsterHPFactor = new List<float>();

    /// <summary>
    /// 英雄怪物生命成长参数
    /// </summary>
    public float HeroMonsterHPGrowParam => __HeroMonsterHPGrowParam;
    private float __HeroMonsterHPGrowParam;

    /// <summary>
    /// 进攻英雄怪物生命极限成长倍率
    /// </summary>
    public List<float> HeroMonsterHPLimit => __HeroMonsterHPLimit;
    private readonly List<float> __HeroMonsterHPLimit = new List<float>();

    /// <summary>
    /// 敌方英雄怪物图标ID
    /// </summary>
    public AssetRef HeroMonsterIconEnemy => __HeroMonsterIconEnemy;
    private AssetRef __HeroMonsterIconEnemy;

    /// <summary>
    /// 己方英雄怪物图标ID
    /// </summary>
    public AssetRef HeroMonsterIconPlayer => __HeroMonsterIconPlayer;
    private AssetRef __HeroMonsterIconPlayer;

    /// <summary>
    /// 布阵章节ID
    /// </summary>
    public int LineupChapterID => __LineupChapterID;
    private int __LineupChapterID;

    /// <summary>
    /// 布阵RuleId
    /// </summary>
    public List<int> LineupRules => __LineupRules;
    private readonly List<int> __LineupRules = new List<int>();

    /// <summary>
    /// 未参与建筑防守的编队无奖励倍率，开放数值很低的参与奖励
    /// </summary>
    public float NoDefenseParticipationRate => __NoDefenseParticipationRate;
    private float __NoDefenseParticipationRate;

    /// <summary>
    /// 战斗结算奖励道具ID
    /// </summary>
    public int RewardItemId => __RewardItemId;
    private int __RewardItemId;

    /// <summary>
    /// 敌方战斗场景ID
    /// </summary>
    public int RivalFightSceneId => __RivalFightSceneId;
    private int __RivalFightSceneId;

    /// <summary>
    /// 己方战斗场景ID
    /// </summary>
    public int SelfFightSceneId => __SelfFightSceneId;
    private int __SelfFightSceneId;

    /// <summary>
    /// 战斗胜利成员100%代币奖励倍率
    /// </summary>
    public float VictoryTokenRate => __VictoryTokenRate;
    private float __VictoryTokenRate;


    public override void ParseConfigure( string text )
    {
        var lines = text.Replace("\r", "")
            .Split(new[] {"\n"}, System.StringSplitOptions.None);

        var rawData = new Dictionary<string, string>();
        foreach (var line in lines)
        {
            if( line.Length == 0 )
                continue;
            if( line[0] == '\0' ||line[0] == '#' || line[0] == '%' || line[0] == '$')
                continue;

            var rows = line.Split(new[] {","}, System.StringSplitOptions.None);
            if (rows.Length < 3)
            {
                ErrorLogger?.Invoke(string.Format("CommonConst列数不符"));
                return;
            }
        
            rawData.Add(rows[0], rows[1]);
        }
        
        var raw = "";
        if (rawData.TryGetValue("AttackHeroMin", out raw))
        {
            __AttackHeroMin = int.Parse(raw);
        }
        if (rawData.TryGetValue("BarrackCoolingTimeFactor", out raw))
        {
            __BarrackCoolingTimeFactor = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("ChapterId", out raw))
        {
            __ChapterId = int.Parse(raw);
        }
        if (rawData.TryGetValue("DefeatTokenRate", out raw))
        {
            __DefeatTokenRate = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DefenseParticipationRate", out raw))
        {
            __DefenseParticipationRate = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DefenseRewardDecrease", out raw))
        {
            var keyValues = raw.Split(new string[]{"|"}, System.StringSplitOptions.RemoveEmptyEntries);
            __DefenseRewardDecrease.Clear();
            foreach (var kv in keyValues)
            {
                if (string.IsNullOrEmpty(kv))
                    continue;
                var split = kv.Split(':');
                if (split.Length != 2)
                    continue;
                var k = int.Parse(split[0]);
                var v = float.Parse(split[1], System.Globalization.CultureInfo.InvariantCulture);
                __DefenseRewardDecrease.Add(k, v);
            }
        }
        if (rawData.TryGetValue("DefenseSuccessRate", out raw))
        {
            __DefenseSuccessRate = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("FatigueRivalRules1", out raw))
        {
            var fields = raw.Split(new string[]{"|"}, System.StringSplitOptions.RemoveEmptyEntries);
            __FatigueRivalRules1.Clear();
            foreach (var f in fields)
            {
                if (string.IsNullOrEmpty(f))
                    continue;
                __FatigueRivalRules1.Add(int.Parse(f));
            }
        }
        if (rawData.TryGetValue("FatigueRivalRules2", out raw))
        {
            var fields = raw.Split(new string[]{"|"}, System.StringSplitOptions.RemoveEmptyEntries);
            __FatigueRivalRules2.Clear();
            foreach (var f in fields)
            {
                if (string.IsNullOrEmpty(f))
                    continue;
                __FatigueRivalRules2.Add(int.Parse(f));
            }
        }
        if (rawData.TryGetValue("FatigueRivalRules3", out raw))
        {
            var fields = raw.Split(new string[]{"|"}, System.StringSplitOptions.RemoveEmptyEntries);
            __FatigueRivalRules3.Clear();
            foreach (var f in fields)
            {
                if (string.IsNullOrEmpty(f))
                    continue;
                __FatigueRivalRules3.Add(int.Parse(f));
            }
        }
        if (rawData.TryGetValue("FatigueRivalRules4", out raw))
        {
            var fields = raw.Split(new string[]{"|"}, System.StringSplitOptions.RemoveEmptyEntries);
            __FatigueRivalRules4.Clear();
            foreach (var f in fields)
            {
                if (string.IsNullOrEmpty(f))
                    continue;
                __FatigueRivalRules4.Add(int.Parse(f));
            }
        }
        if (rawData.TryGetValue("FatigueSelfRules1", out raw))
        {
            var fields = raw.Split(new string[]{"|"}, System.StringSplitOptions.RemoveEmptyEntries);
            __FatigueSelfRules1.Clear();
            foreach (var f in fields)
            {
                if (string.IsNullOrEmpty(f))
                    continue;
                __FatigueSelfRules1.Add(int.Parse(f));
            }
        }
        if (rawData.TryGetValue("FatigueSelfRules2", out raw))
        {
            var fields = raw.Split(new string[]{"|"}, System.StringSplitOptions.RemoveEmptyEntries);
            __FatigueSelfRules2.Clear();
            foreach (var f in fields)
            {
                if (string.IsNullOrEmpty(f))
                    continue;
                __FatigueSelfRules2.Add(int.Parse(f));
            }
        }
        if (rawData.TryGetValue("FatigueSelfRules3", out raw))
        {
            var fields = raw.Split(new string[]{"|"}, System.StringSplitOptions.RemoveEmptyEntries);
            __FatigueSelfRules3.Clear();
            foreach (var f in fields)
            {
                if (string.IsNullOrEmpty(f))
                    continue;
                __FatigueSelfRules3.Add(int.Parse(f));
            }
        }
        if (rawData.TryGetValue("FatigueSelfRules4", out raw))
        {
            var fields = raw.Split(new string[]{"|"}, System.StringSplitOptions.RemoveEmptyEntries);
            __FatigueSelfRules4.Clear();
            foreach (var f in fields)
            {
                if (string.IsNullOrEmpty(f))
                    continue;
                __FatigueSelfRules4.Add(int.Parse(f));
            }
        }
        if (rawData.TryGetValue("FightRules", out raw))
        {
            var fields = raw.Split(new string[]{"|"}, System.StringSplitOptions.RemoveEmptyEntries);
            __FightRules.Clear();
            foreach (var f in fields)
            {
                if (string.IsNullOrEmpty(f))
                    continue;
                __FightRules.Add(int.Parse(f));
            }
        }
        if (rawData.TryGetValue("FightTime", out raw))
        {
            __FightTime = int.Parse(raw);
        }
        if (rawData.TryGetValue("HeroMonsterATKFactor", out raw))
        {
            var fields = raw.Split(new string[]{"|"}, System.StringSplitOptions.RemoveEmptyEntries);
            __HeroMonsterATKFactor.Clear();
            foreach (var f in fields)
            {
                if (string.IsNullOrEmpty(f))
                    continue;
                __HeroMonsterATKFactor.Add(float.Parse(f, System.Globalization.CultureInfo.InvariantCulture));
            }
        }
        if (rawData.TryGetValue("HeroMonsterAtkGrowParam", out raw))
        {
            __HeroMonsterAtkGrowParam = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("HeroMonsterATKLimit", out raw))
        {
            var fields = raw.Split(new string[]{"|"}, System.StringSplitOptions.RemoveEmptyEntries);
            __HeroMonsterATKLimit.Clear();
            foreach (var f in fields)
            {
                if (string.IsNullOrEmpty(f))
                    continue;
                __HeroMonsterATKLimit.Add(float.Parse(f, System.Globalization.CultureInfo.InvariantCulture));
            }
        }
        if (rawData.TryGetValue("HeroMonsterGrowthTime", out raw))
        {
            var fields = raw.Split(new string[]{"|"}, System.StringSplitOptions.RemoveEmptyEntries);
            __HeroMonsterGrowthTime.Clear();
            foreach (var f in fields)
            {
                if (string.IsNullOrEmpty(f))
                    continue;
                __HeroMonsterGrowthTime.Add(int.Parse(f));
            }
        }
        if (rawData.TryGetValue("HeroMonsterHPFactor", out raw))
        {
            var fields = raw.Split(new string[]{"|"}, System.StringSplitOptions.RemoveEmptyEntries);
            __HeroMonsterHPFactor.Clear();
            foreach (var f in fields)
            {
                if (string.IsNullOrEmpty(f))
                    continue;
                __HeroMonsterHPFactor.Add(float.Parse(f, System.Globalization.CultureInfo.InvariantCulture));
            }
        }
        if (rawData.TryGetValue("HeroMonsterHPGrowParam", out raw))
        {
            __HeroMonsterHPGrowParam = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("HeroMonsterHPLimit", out raw))
        {
            var fields = raw.Split(new string[]{"|"}, System.StringSplitOptions.RemoveEmptyEntries);
            __HeroMonsterHPLimit.Clear();
            foreach (var f in fields)
            {
                if (string.IsNullOrEmpty(f))
                    continue;
                __HeroMonsterHPLimit.Add(float.Parse(f, System.Globalization.CultureInfo.InvariantCulture));
            }
        }
        if (rawData.TryGetValue("HeroMonsterIconEnemy", out raw))
        {
            __HeroMonsterIconEnemy = AssetRef.Parse(raw);
        }
        if (rawData.TryGetValue("HeroMonsterIconPlayer", out raw))
        {
            __HeroMonsterIconPlayer = AssetRef.Parse(raw);
        }
        if (rawData.TryGetValue("LineupChapterID", out raw))
        {
            __LineupChapterID = int.Parse(raw);
        }
        if (rawData.TryGetValue("LineupRules", out raw))
        {
            var fields = raw.Split(new string[]{"|"}, System.StringSplitOptions.RemoveEmptyEntries);
            __LineupRules.Clear();
            foreach (var f in fields)
            {
                if (string.IsNullOrEmpty(f))
                    continue;
                __LineupRules.Add(int.Parse(f));
            }
        }
        if (rawData.TryGetValue("NoDefenseParticipationRate", out raw))
        {
            __NoDefenseParticipationRate = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("RewardItemId", out raw))
        {
            __RewardItemId = int.Parse(raw);
        }
        if (rawData.TryGetValue("RivalFightSceneId", out raw))
        {
            __RivalFightSceneId = int.Parse(raw);
        }
        if (rawData.TryGetValue("SelfFightSceneId", out raw))
        {
            __SelfFightSceneId = int.Parse(raw);
        }
        if (rawData.TryGetValue("VictoryTokenRate", out raw))
        {
            __VictoryTokenRate = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
    }
}