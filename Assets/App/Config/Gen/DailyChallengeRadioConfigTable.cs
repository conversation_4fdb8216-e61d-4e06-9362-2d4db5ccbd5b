//-----------------------------------------
//<auto-generated>
//    This code was generated by a tool.
//    Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
//</auto-generated>
//------------------------------------------
// Generated from :DailyChallengeRadioConfig.csv

using System.Collections.Generic;
using System.IO;
using System.Text;

public partial class DailyChallengeRadioConfig
{
    private int __Id;
    public int Id
    {
        internal set{ __Id = value; }
        get{ return __Id; }
    }
    public List<float> EnemyHpRadio = new List<float>();
    private float __RewardRadio;
    public float RewardRadio
    {
        set{ __RewardRadio = value; }
        get{ return __RewardRadio; }
    }
}

public class DailyChallengeRadioConfigTable : FQDev.CSVConfig.ConfigBase
{
    static string __config_file_name = "DailyChallengeRadioConfig";
    public static string ConfigName => __config_file_name;
    static int __col_count = 3;

    private readonly Dictionary<int,DailyChallengeRadioConfig> __rows = new Dictionary<int,DailyChallengeRadioConfig>();
    public Dictionary<int,DailyChallengeRadioConfig> Rows => __rows;
    // key = DailyChallengeRadioConfig.Id
    public DailyChallengeRadioConfig GetRowData( int key )
    {
        DailyChallengeRadioConfig tmp; 
        if( !__rows.TryGetValue( key, out tmp ) )
            LogError("Can't get row data by key:{0} Config:{1}", key, __config_file_name);
        return tmp;
    }

    public int GetRowCount()
    {
        return __rows.Count;
    }

    public override void ParseConfigure( string text )
    {

        __rows.Clear();
        string splace_text = text.Replace("\r","");
        string[] lines = splace_text.Split(new string[]{"\n"}, System.StringSplitOptions.None);
        
        foreach( string line in lines )
        {
            if( line.Length == 0 )
                continue;
            if( line[0] == '\0' ||line[0] == '#' || line[0] == '%' || line[0] == '$')
                continue;


            string[] rowdata = line.Split(new string[]{","}, System.StringSplitOptions.None);
            if( rowdata.Length != __col_count )
            {
                LogError(string.Format("{0}列数与原配置不符", __config_file_name));
                return;
            }
            int counter = 0;
            DailyChallengeRadioConfig tmp = new DailyChallengeRadioConfig();

            string strId = rowdata[counter++];
            if (!string.IsNullOrEmpty(strId))
                tmp.Id = int.Parse(strId);
            do{
                string[] llc = rowdata[counter++].Split(new string[]{"|"} ,System.StringSplitOptions.None);
                foreach( string vvv in llc )
                {
                    if(vvv.Length == 0 ) 
                        continue;
                    tmp.EnemyHpRadio.Add( float.Parse(vvv, System.Globalization.CultureInfo.InvariantCulture) );
                }
            }while(false);

            string strRewardRadio = rowdata[counter++];
            if (!string.IsNullOrEmpty(strRewardRadio))
                tmp.RewardRadio = float.Parse(strRewardRadio, System.Globalization.CultureInfo.InvariantCulture);
            __rows.Add( tmp.Id, tmp );
        }
    }
}