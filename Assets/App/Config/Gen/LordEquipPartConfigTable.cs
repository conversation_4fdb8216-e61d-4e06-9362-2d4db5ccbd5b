//-----------------------------------------
//<auto-generated>
//    This code was generated by a tool.
//    Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
//</auto-generated>
//------------------------------------------
// Generated from :LordEquipPartConfig.csv

using System.Collections.Generic;
using System.IO;
using System.Text;

public partial class LordEquipPartConfig
{
    private int __Id;
    public int Id
    {
        internal set{ __Id = value; }
        get{ return __Id; }
    }
    private string __Name;
    public string Name
    {
        internal set{ __Name = value; }
        get{ return __Name; }
    }
    private AssetRef __Icon;
    public AssetRef Icon
    {
        internal set{ __Icon = value; }
        get{ return __Icon; }
    }
    private int __LevelMaterial;
    public int LevelMaterial
    {
        internal set{ __LevelMaterial = value; }
        get{ return __LevelMaterial; }
    }
    public List<int> RefineMaterial = new List<int>();
}

public class LordEquipPartConfigTable : FQDev.CSVConfig.ConfigBase
{
    static string __config_file_name = "LordEquipPartConfig";
    public static string ConfigName => __config_file_name;
    static int __col_count = 5;

    private readonly Dictionary<int,LordEquipPartConfig> __rows = new Dictionary<int,LordEquipPartConfig>();
    public Dictionary<int,LordEquipPartConfig> Rows => __rows;
    // key = LordEquipPartConfig.Id
    public LordEquipPartConfig GetRowData( int key )
    {
        LordEquipPartConfig tmp; 
        if( !__rows.TryGetValue( key, out tmp ) )
            LogError("Can't get row data by key:{0} Config:{1}", key, __config_file_name);
        return tmp;
    }

    public int GetRowCount()
    {
        return __rows.Count;
    }

    public override void ParseConfigure( string text )
    {

        __rows.Clear();
        string splace_text = text.Replace("\r","");
        string[] lines = splace_text.Split(new string[]{"\n"}, System.StringSplitOptions.None);
        
        foreach( string line in lines )
        {
            if( line.Length == 0 )
                continue;
            if( line[0] == '\0' ||line[0] == '#' || line[0] == '%' || line[0] == '$')
                continue;


            string[] rowdata = line.Split(new string[]{","}, System.StringSplitOptions.None);
            if( rowdata.Length != __col_count )
            {
                LogError(string.Format("{0}列数与原配置不符", __config_file_name));
                return;
            }
            int counter = 0;
            LordEquipPartConfig tmp = new LordEquipPartConfig();

            string strId = rowdata[counter++];
            if (!string.IsNullOrEmpty(strId))
                tmp.Id = int.Parse(strId);
            tmp.Name = rowdata[counter++];
            tmp.Icon = AssetRef.Parse(rowdata[counter++]);

            string strLevelMaterial = rowdata[counter++];
            if (!string.IsNullOrEmpty(strLevelMaterial))
                tmp.LevelMaterial = int.Parse(strLevelMaterial);
            do{
                string[] llc = rowdata[counter++].Split(new string[]{"|"} ,System.StringSplitOptions.None);
                foreach( string vvv in llc )
                {
                    if(vvv.Length == 0 ) 
                        continue;
                    tmp.RefineMaterial.Add( int.Parse(vvv) );
                }
            }while(false);
            __rows.Add( tmp.Id, tmp );
        }
    }
}