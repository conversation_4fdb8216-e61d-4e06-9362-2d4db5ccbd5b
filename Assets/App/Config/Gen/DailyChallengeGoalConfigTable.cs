//-----------------------------------------
//<auto-generated>
//    This code was generated by a tool.
//    Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
//</auto-generated>
//------------------------------------------
// Generated from :DailyChallengeGoalConfig.csv

using System.Collections.Generic;
using System.IO;
using System.Text;

public partial class DailyChallengeGoalConfig
{
    private int __Id;
    public int Id
    {
        internal set{ __Id = value; }
        get{ return __Id; }
    }
    private string __Name;
    public string Name
    {
        internal set{ __Name = value; }
        get{ return __Name; }
    }
    private int __TaskType;
    public int TaskType
    {
        internal set{ __TaskType = value; }
        get{ return __TaskType; }
    }
    private string __Desc;
    public string Desc
    {
        internal set{ __Desc = value; }
        get{ return __Desc; }
    }
    private AssetRef __TaskIcon;
    public AssetRef TaskIcon
    {
        internal set{ __TaskIcon = value; }
        get{ return __TaskIcon; }
    }
    private int __TagetParam;
    public int TagetParam
    {
        internal set{ __TagetParam = value; }
        get{ return __TagetParam; }
    }
    private float __TargetCount;
    public float TargetCount
    {
        set{ __TargetCount = value; }
        get{ return __TargetCount; }
    }
    private int __Weight;
    public int Weight
    {
        internal set{ __Weight = value; }
        get{ return __Weight; }
    }
    public List<RewardItem> Award = new List<RewardItem>();
    private string __Comment;
    public string Comment
    {
        internal set{ __Comment = value; }
        get{ return __Comment; }
    }
}

public class DailyChallengeGoalConfigTable : FQDev.CSVConfig.ConfigBase
{
    static string __config_file_name = "DailyChallengeGoalConfig";
    public static string ConfigName => __config_file_name;
    static int __col_count = 10;

    private readonly Dictionary<int,DailyChallengeGoalConfig> __rows = new Dictionary<int,DailyChallengeGoalConfig>();
    public Dictionary<int,DailyChallengeGoalConfig> Rows => __rows;
    // key = DailyChallengeGoalConfig.Id
    public DailyChallengeGoalConfig GetRowData( int key )
    {
        DailyChallengeGoalConfig tmp; 
        if( !__rows.TryGetValue( key, out tmp ) )
            LogError("Can't get row data by key:{0} Config:{1}", key, __config_file_name);
        return tmp;
    }

    public int GetRowCount()
    {
        return __rows.Count;
    }

    public override void ParseConfigure( string text )
    {

        __rows.Clear();
        string splace_text = text.Replace("\r","");
        string[] lines = splace_text.Split(new string[]{"\n"}, System.StringSplitOptions.None);
        
        foreach( string line in lines )
        {
            if( line.Length == 0 )
                continue;
            if( line[0] == '\0' ||line[0] == '#' || line[0] == '%' || line[0] == '$')
                continue;


            string[] rowdata = line.Split(new string[]{","}, System.StringSplitOptions.None);
            if( rowdata.Length != __col_count )
            {
                LogError(string.Format("{0}列数与原配置不符", __config_file_name));
                return;
            }
            int counter = 0;
            DailyChallengeGoalConfig tmp = new DailyChallengeGoalConfig();

            string strId = rowdata[counter++];
            if (!string.IsNullOrEmpty(strId))
                tmp.Id = int.Parse(strId);
            tmp.Name = rowdata[counter++];

            string strTaskType = rowdata[counter++];
            if (!string.IsNullOrEmpty(strTaskType))
                tmp.TaskType = int.Parse(strTaskType);
            tmp.Desc = rowdata[counter++];
            tmp.TaskIcon = AssetRef.Parse(rowdata[counter++]);

            string strTagetParam = rowdata[counter++];
            if (!string.IsNullOrEmpty(strTagetParam))
                tmp.TagetParam = int.Parse(strTagetParam);

            string strTargetCount = rowdata[counter++];
            if (!string.IsNullOrEmpty(strTargetCount))
                tmp.TargetCount = float.Parse(strTargetCount, System.Globalization.CultureInfo.InvariantCulture);

            string strWeight = rowdata[counter++];
            if (!string.IsNullOrEmpty(strWeight))
                tmp.Weight = int.Parse(strWeight);
            do{
                string[] llc = rowdata[counter++].Split(new string[]{"|"} ,System.StringSplitOptions.None);
                foreach( string vvv in llc )
                {
                    if(vvv.Length == 0 ) 
                        continue;
                    tmp.Award.Add( RewardItem.Parse(vvv) );
                }
            }while(false);
            tmp.Comment = rowdata[counter++];
            __rows.Add( tmp.Id, tmp );
        }
    }
}