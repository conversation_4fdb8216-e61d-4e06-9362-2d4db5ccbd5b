//-----------------------------------------
//<auto-generated>
//    This code was generated by a tool.
//    Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
//</auto-generated>
//------------------------------------------
// Generated from :GameViewConst.csv

using System.Collections.Generic;
using System.IO;
using System.Text;

public class GameViewConst : FQDev.CSVConfig.ConfigBase
{
    /// <summary>
    /// 底板圆角遮罩——左下
    /// </summary>
    public AssetRef BoardCornerMaskLD => __BoardCornerMaskLD;
    private AssetRef __BoardCornerMaskLD;

    /// <summary>
    /// 底板圆角遮罩——左上
    /// </summary>
    public AssetRef BoardCornerMaskLU => __BoardCornerMaskLU;
    private AssetRef __BoardCornerMaskLU;

    /// <summary>
    /// 底板圆角遮罩——右下
    /// </summary>
    public AssetRef BoardCornerMaskRD => __BoardCornerMaskRD;
    private AssetRef __BoardCornerMaskRD;

    /// <summary>
    /// 底板圆角遮罩——右上
    /// </summary>
    public AssetRef BoardCornerMaskRU => __BoardCornerMaskRU;
    private AssetRef __BoardCornerMaskRU;

    /// <summary>
    /// 战斗开始时蒙版层颜色
    /// </summary>
    public ColorConfig BoardFightMaskColor => __BoardFightMaskColor;
    private ColorConfig __BoardFightMaskColor;

    /// <summary>
    /// Boss出场特效
    /// </summary>
    public AssetRef BossAppearEffect => __BossAppearEffect;
    private AssetRef __BossAppearEffect;

    /// <summary>
    /// boss警告位置图
    /// </summary>
    public AssetRef BossWaringBackground => __BossWaringBackground;
    private AssetRef __BossWaringBackground;

    /// <summary>
    /// 点击高亮持续时间
    /// </summary>
    public float ClickHighlightDuration => __ClickHighlightDuration;
    private float __ClickHighlightDuration;

    /// <summary>
    /// 技能金币图标
    /// </summary>
    public AssetRef CoinIcon => __CoinIcon;
    private AssetRef __CoinIcon;

    /// <summary>
    /// 暴击伤害
    /// </summary>
    public float CritDamage => __CritDamage;
    private float __CritDamage;

    /// <summary>
    /// 伤害数值字符比例
    /// </summary>
    public float DamageCriticScale => __DamageCriticScale;
    private float __DamageCriticScale;

    /// <summary>
    /// 伤害数值字间距
    /// </summary>
    public float DamageCriticSpacingX => __DamageCriticSpacingX;
    private float __DamageCriticSpacingX;

    /// <summary>
    /// 伤害数字显示时长
    /// </summary>
    public float DamageDisplayDuration => __DamageDisplayDuration;
    private float __DamageDisplayDuration;

    /// <summary>
    /// 伤害数字渐隐延迟
    /// </summary>
    public float DamageFadeDelay => __DamageFadeDelay;
    private float __DamageFadeDelay;

    /// <summary>
    /// 伤害数值字符比例
    /// </summary>
    public float DamageFontScale => __DamageFontScale;
    private float __DamageFontScale;

    /// <summary>
    /// 伤害数值字间距
    /// </summary>
    public float DamageFontSpacingX => __DamageFontSpacingX;
    private float __DamageFontSpacingX;

    /// <summary>
    /// 伤害图标和数字间距
    /// </summary>
    public float DamageIconSpacingX => __DamageIconSpacingX;
    private float __DamageIconSpacingX;

    /// <summary>
    /// 伤害数值字符比例
    /// </summary>
    public float DamageKillScale => __DamageKillScale;
    private float __DamageKillScale;

    /// <summary>
    /// 伤害数值字间距
    /// </summary>
    public float DamageKillSpacingX => __DamageKillSpacingX;
    private float __DamageKillSpacingX;

    /// <summary>
    /// miss伤害数字比例
    /// </summary>
    public float DamageMissScale => __DamageMissScale;
    private float __DamageMissScale;

    /// <summary>
    /// miss伤害数值字间距
    /// </summary>
    public float DamageMissSpacingX => __DamageMissSpacingX;
    private float __DamageMissSpacingX;

    /// <summary>
    /// 伤害数字移动速度
    /// </summary>
    public float DamageMoveSpeed => __DamageMoveSpeed;
    private float __DamageMoveSpeed;

    /// <summary>
    /// 抗性伤害数字比例
    /// </summary>
    public float DamageResistanceScale => __DamageResistanceScale;
    private float __DamageResistanceScale;

    /// <summary>
    /// 抗性伤害数值字间距
    /// </summary>
    public float DamageResistanceSpacingX => __DamageResistanceSpacingX;
    private float __DamageResistanceSpacingX;

    /// <summary>
    /// 伤害统计模块是否展示
    /// </summary>
    public bool DamageStatisticsUnlock => __DamageStatisticsUnlock;
    private bool __DamageStatisticsUnlock;

    /// <summary>
    /// 开启统计标识的章节（通关）
    /// </summary>
    public int DamageStatisticsUnlockCondition => __DamageStatisticsUnlockCondition;
    private int __DamageStatisticsUnlockCondition;

    /// <summary>
    /// 弱点伤害数字比例
    /// </summary>
    public float DamageVulnerabilityScale => __DamageVulnerabilityScale;
    private float __DamageVulnerabilityScale;

    /// <summary>
    /// 弱点伤害数值字间距
    /// </summary>
    public float DamageVulnerabilitySpacingX => __DamageVulnerabilitySpacingX;
    private float __DamageVulnerabilitySpacingX;

    /// <summary>
    /// 伤害数字结束缩放大小参数（Sin（value））
    /// </summary>
    public float DamageZoomEndSin => __DamageZoomEndSin;
    private float __DamageZoomEndSin;

    /// <summary>
    /// 伤害数字缩放速度倍速
    /// </summary>
    public float DamageZoomSpeed => __DamageZoomSpeed;
    private float __DamageZoomSpeed;

    /// <summary>
    /// 伤害数字初始缩放大小参数（Sin（value））
    /// </summary>
    public float DamageZoomStartSin => __DamageZoomStartSin;
    private float __DamageZoomStartSin;

    /// <summary>
    /// 默认局内UI
    /// </summary>
    public string DefaultGamePlayLayerPath => __DefaultGamePlayLayerPath;
    private string __DefaultGamePlayLayerPath;

    /// <summary>
    /// 敌人出场特效
    /// </summary>
    public AssetRef EnemyAppearEffect => __EnemyAppearEffect;
    private AssetRef __EnemyAppearEffect;

    /// <summary>
    /// 敌方出场特效时长
    /// </summary>
    public float EnemyAppearEffectDuration => __EnemyAppearEffectDuration;
    private float __EnemyAppearEffectDuration;

    /// <summary>
    /// 敌人攻击城墙特效
    /// </summary>
    public AssetRef EnemyHitTowerEffect => __EnemyHitTowerEffect;
    private AssetRef __EnemyHitTowerEffect;

    /// <summary>
    /// 敌人介绍界面出入时长
    /// </summary>
    public float EnemyInfoTipInOutDuration => __EnemyInfoTipInOutDuration;
    private float __EnemyInfoTipInOutDuration;

    /// <summary>
    /// 敌人介绍界面展示开关
    /// </summary>
    public int EnemyInfoTipShow => __EnemyInfoTipShow;
    private int __EnemyInfoTipShow;

    /// <summary>
    /// 敌人介绍界面停留时长
    /// </summary>
    public float EnemyInfoTipStayDuration => __EnemyInfoTipStayDuration;
    private float __EnemyInfoTipStayDuration;

    /// <summary>
    /// 战斗退出相机缩放持续时长
    /// </summary>
    public float FightEndZoomDuration => __FightEndZoomDuration;
    private float __FightEndZoomDuration;

    /// <summary>
    /// 战斗进入相机缩放持续时长
    /// </summary>
    public float FightStartZoomDuration => __FightStartZoomDuration;
    private float __FightStartZoomDuration;

    /// <summary>
    /// 战斗阶段相机视口大小
    /// </summary>
    public float FightStateCameraSize => __FightStateCameraSize;
    private float __FightStateCameraSize;

    /// <summary>
    /// 战斗阶段相机坐标Y
    /// </summary>
    public float FightStateCameraY => __FightStateCameraY;
    private float __FightStateCameraY;

    /// <summary>
    /// 援护地块图
    /// </summary>
    public AssetRef GuardianTileSlotImage => __GuardianTileSlotImage;
    private AssetRef __GuardianTileSlotImage;

    /// <summary>
    /// 战斗阶段相机视口大小
    /// </summary>
    public float GuildWarFightStateCameraSize => __GuildWarFightStateCameraSize;
    private float __GuildWarFightStateCameraSize;

    /// <summary>
    /// 战斗阶段相机坐标Y
    /// </summary>
    public float GuildWarFightStateCameraY => __GuildWarFightStateCameraY;
    private float __GuildWarFightStateCameraY;

    /// <summary>
    /// 英雄死亡通用图
    /// </summary>
    public AssetRef HeroDieEffect => __HeroDieEffect;
    private AssetRef __HeroDieEffect;

    /// <summary>
    /// 英雄最高级常驻特效
    /// </summary>
    public AssetRef HeroMaxGradeEffect => __HeroMaxGradeEffect;
    private AssetRef __HeroMaxGradeEffect;

    /// <summary>
    /// 英雄最高级提示特效
    /// </summary>
    public AssetRef HeroMaxGradeHintEffect => __HeroMaxGradeHintEffect;
    private AssetRef __HeroMaxGradeHintEffect;

    /// <summary>
    /// 长按消除操作长按时间
    /// </summary>
    public float Hold2DestroyDuration => __Hold2DestroyDuration;
    private float __Hold2DestroyDuration;

    /// <summary>
    /// 长按消除操作特效
    /// </summary>
    public AssetRef Hold2DestroyEffect => __Hold2DestroyEffect;
    private AssetRef __Hold2DestroyEffect;

    /// <summary>
    /// 三消阶段相机视口大小
    /// </summary>
    public float MergeStateCameraSize => __MergeStateCameraSize;
    private float __MergeStateCameraSize;

    /// <summary>
    /// 三消阶段相机坐标Y
    /// </summary>
    public float MergeStateCameraY => __MergeStateCameraY;
    private float __MergeStateCameraY;

    /// <summary>
    /// 消除后填充面板延迟时长
    /// </summary>
    public float RefillDelayPostMerge => __RefillDelayPostMerge;
    private float __RefillDelayPostMerge;

    /// <summary>
    /// 转场传送入特效
    /// </summary>
    public AssetRef SceneTransferInPortalEffect => __SceneTransferInPortalEffect;
    private AssetRef __SceneTransferInPortalEffect;

    /// <summary>
    /// 转场传送出特效
    /// </summary>
    public AssetRef SceneTransferOutPortalEffect => __SceneTransferOutPortalEffect;
    private AssetRef __SceneTransferOutPortalEffect;

    /// <summary>
    /// 转场前队形重组时长
    /// </summary>
    public float SceneTransferReformationDuration => __SceneTransferReformationDuration;
    private float __SceneTransferReformationDuration;

    /// <summary>
    /// 转场传送英雄间时间间隔
    /// </summary>
    public float SceneTrasnferGap => __SceneTrasnferGap;
    private float __SceneTrasnferGap;

    /// <summary>
    /// 技能宝箱预制体
    /// </summary>
    public AssetRef SkillBoxTilePrefab => __SkillBoxTilePrefab;
    private AssetRef __SkillBoxTilePrefab;

    /// <summary>
    /// Sprite地块预制体
    /// </summary>
    public AssetRef SpriteTilePrefab => __SpriteTilePrefab;
    private AssetRef __SpriteTilePrefab;

    /// <summary>
    /// 生成特效
    /// </summary>
    public AssetRef TileCreateEffect => __TileCreateEffect;
    private AssetRef __TileCreateEffect;

    /// <summary>
    /// 消除特效
    /// </summary>
    public AssetRef TileDestroyEffect => __TileDestroyEffect;
    private AssetRef __TileDestroyEffect;

    /// <summary>
    /// 物品掉落移动时长
    /// </summary>
    public float TileItemDropDuration => __TileItemDropDuration;
    private float __TileItemDropDuration;

    /// <summary>
    /// 物品合成生成表现播放延迟
    /// </summary>
    public float TileItemMergeCreateEffectDelay => __TileItemMergeCreateEffectDelay;
    private float __TileItemMergeCreateEffectDelay;

    /// <summary>
    /// 物品拖拽移动时长
    /// </summary>
    public float TileItemMoveDuration => __TileItemMoveDuration;
    private float __TileItemMoveDuration;

    /// <summary>
    /// 物品缩放生成时长
    /// </summary>
    public float TileItemZoomCreateDuration => __TileItemZoomCreateDuration;
    private float __TileItemZoomCreateDuration;

    /// <summary>
    /// 物品缩放消失时长
    /// </summary>
    public float TileItemZoomDestroyDuration => __TileItemZoomDestroyDuration;
    private float __TileItemZoomDestroyDuration;

    /// <summary>
    /// 领主术士恶魔宝箱预制体
    /// </summary>
    public AssetRef WarlockBoxTilePrefab => __WarlockBoxTilePrefab;
    private AssetRef __WarlockBoxTilePrefab;


    public override void ParseConfigure( string text )
    {
        var lines = text.Replace("\r", "")
            .Split(new[] {"\n"}, System.StringSplitOptions.None);

        var rawData = new Dictionary<string, string>();
        foreach (var line in lines)
        {
            if( line.Length == 0 )
                continue;
            if( line[0] == '\0' ||line[0] == '#' || line[0] == '%' || line[0] == '$')
                continue;

            var rows = line.Split(new[] {","}, System.StringSplitOptions.None);
            if (rows.Length < 3)
            {
                ErrorLogger?.Invoke(string.Format("CommonConst列数不符"));
                return;
            }
        
            rawData.Add(rows[0], rows[1]);
        }
        
        var raw = "";
        if (rawData.TryGetValue("BoardCornerMaskLD", out raw))
        {
            __BoardCornerMaskLD = AssetRef.Parse(raw);
        }
        if (rawData.TryGetValue("BoardCornerMaskLU", out raw))
        {
            __BoardCornerMaskLU = AssetRef.Parse(raw);
        }
        if (rawData.TryGetValue("BoardCornerMaskRD", out raw))
        {
            __BoardCornerMaskRD = AssetRef.Parse(raw);
        }
        if (rawData.TryGetValue("BoardCornerMaskRU", out raw))
        {
            __BoardCornerMaskRU = AssetRef.Parse(raw);
        }
        if (rawData.TryGetValue("BoardFightMaskColor", out raw))
        {
            __BoardFightMaskColor = ColorConfig.Parse(raw);
        }
        if (rawData.TryGetValue("BossAppearEffect", out raw))
        {
            __BossAppearEffect = AssetRef.Parse(raw);
        }
        if (rawData.TryGetValue("BossWaringBackground", out raw))
        {
            __BossWaringBackground = AssetRef.Parse(raw);
        }
        if (rawData.TryGetValue("ClickHighlightDuration", out raw))
        {
            __ClickHighlightDuration = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("CoinIcon", out raw))
        {
            __CoinIcon = AssetRef.Parse(raw);
        }
        if (rawData.TryGetValue("CritDamage", out raw))
        {
            __CritDamage = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DamageCriticScale", out raw))
        {
            __DamageCriticScale = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DamageCriticSpacingX", out raw))
        {
            __DamageCriticSpacingX = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DamageDisplayDuration", out raw))
        {
            __DamageDisplayDuration = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DamageFadeDelay", out raw))
        {
            __DamageFadeDelay = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DamageFontScale", out raw))
        {
            __DamageFontScale = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DamageFontSpacingX", out raw))
        {
            __DamageFontSpacingX = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DamageIconSpacingX", out raw))
        {
            __DamageIconSpacingX = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DamageKillScale", out raw))
        {
            __DamageKillScale = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DamageKillSpacingX", out raw))
        {
            __DamageKillSpacingX = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DamageMissScale", out raw))
        {
            __DamageMissScale = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DamageMissSpacingX", out raw))
        {
            __DamageMissSpacingX = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DamageMoveSpeed", out raw))
        {
            __DamageMoveSpeed = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DamageResistanceScale", out raw))
        {
            __DamageResistanceScale = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DamageResistanceSpacingX", out raw))
        {
            __DamageResistanceSpacingX = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DamageStatisticsUnlock", out raw))
        {
            __DamageStatisticsUnlock = string.Equals(bool.TrueString, raw, System.StringComparison.InvariantCultureIgnoreCase);
        }
        if (rawData.TryGetValue("DamageStatisticsUnlockCondition", out raw))
        {
            __DamageStatisticsUnlockCondition = int.Parse(raw);
        }
        if (rawData.TryGetValue("DamageVulnerabilityScale", out raw))
        {
            __DamageVulnerabilityScale = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DamageVulnerabilitySpacingX", out raw))
        {
            __DamageVulnerabilitySpacingX = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DamageZoomEndSin", out raw))
        {
            __DamageZoomEndSin = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DamageZoomSpeed", out raw))
        {
            __DamageZoomSpeed = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DamageZoomStartSin", out raw))
        {
            __DamageZoomStartSin = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DefaultGamePlayLayerPath", out raw))
        {
            __DefaultGamePlayLayerPath = raw;
        }
        if (rawData.TryGetValue("EnemyAppearEffect", out raw))
        {
            __EnemyAppearEffect = AssetRef.Parse(raw);
        }
        if (rawData.TryGetValue("EnemyAppearEffectDuration", out raw))
        {
            __EnemyAppearEffectDuration = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("EnemyHitTowerEffect", out raw))
        {
            __EnemyHitTowerEffect = AssetRef.Parse(raw);
        }
        if (rawData.TryGetValue("EnemyInfoTipInOutDuration", out raw))
        {
            __EnemyInfoTipInOutDuration = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("EnemyInfoTipShow", out raw))
        {
            __EnemyInfoTipShow = int.Parse(raw);
        }
        if (rawData.TryGetValue("EnemyInfoTipStayDuration", out raw))
        {
            __EnemyInfoTipStayDuration = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("FightEndZoomDuration", out raw))
        {
            __FightEndZoomDuration = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("FightStartZoomDuration", out raw))
        {
            __FightStartZoomDuration = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("FightStateCameraSize", out raw))
        {
            __FightStateCameraSize = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("FightStateCameraY", out raw))
        {
            __FightStateCameraY = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("GuardianTileSlotImage", out raw))
        {
            __GuardianTileSlotImage = AssetRef.Parse(raw);
        }
        if (rawData.TryGetValue("GuildWarFightStateCameraSize", out raw))
        {
            __GuildWarFightStateCameraSize = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("GuildWarFightStateCameraY", out raw))
        {
            __GuildWarFightStateCameraY = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("HeroDieEffect", out raw))
        {
            __HeroDieEffect = AssetRef.Parse(raw);
        }
        if (rawData.TryGetValue("HeroMaxGradeEffect", out raw))
        {
            __HeroMaxGradeEffect = AssetRef.Parse(raw);
        }
        if (rawData.TryGetValue("HeroMaxGradeHintEffect", out raw))
        {
            __HeroMaxGradeHintEffect = AssetRef.Parse(raw);
        }
        if (rawData.TryGetValue("Hold2DestroyDuration", out raw))
        {
            __Hold2DestroyDuration = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("Hold2DestroyEffect", out raw))
        {
            __Hold2DestroyEffect = AssetRef.Parse(raw);
        }
        if (rawData.TryGetValue("MergeStateCameraSize", out raw))
        {
            __MergeStateCameraSize = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("MergeStateCameraY", out raw))
        {
            __MergeStateCameraY = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("RefillDelayPostMerge", out raw))
        {
            __RefillDelayPostMerge = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("SceneTransferInPortalEffect", out raw))
        {
            __SceneTransferInPortalEffect = AssetRef.Parse(raw);
        }
        if (rawData.TryGetValue("SceneTransferOutPortalEffect", out raw))
        {
            __SceneTransferOutPortalEffect = AssetRef.Parse(raw);
        }
        if (rawData.TryGetValue("SceneTransferReformationDuration", out raw))
        {
            __SceneTransferReformationDuration = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("SceneTrasnferGap", out raw))
        {
            __SceneTrasnferGap = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("SkillBoxTilePrefab", out raw))
        {
            __SkillBoxTilePrefab = AssetRef.Parse(raw);
        }
        if (rawData.TryGetValue("SpriteTilePrefab", out raw))
        {
            __SpriteTilePrefab = AssetRef.Parse(raw);
        }
        if (rawData.TryGetValue("TileCreateEffect", out raw))
        {
            __TileCreateEffect = AssetRef.Parse(raw);
        }
        if (rawData.TryGetValue("TileDestroyEffect", out raw))
        {
            __TileDestroyEffect = AssetRef.Parse(raw);
        }
        if (rawData.TryGetValue("TileItemDropDuration", out raw))
        {
            __TileItemDropDuration = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("TileItemMergeCreateEffectDelay", out raw))
        {
            __TileItemMergeCreateEffectDelay = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("TileItemMoveDuration", out raw))
        {
            __TileItemMoveDuration = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("TileItemZoomCreateDuration", out raw))
        {
            __TileItemZoomCreateDuration = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("TileItemZoomDestroyDuration", out raw))
        {
            __TileItemZoomDestroyDuration = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("WarlockBoxTilePrefab", out raw))
        {
            __WarlockBoxTilePrefab = AssetRef.Parse(raw);
        }
    }
}