//-----------------------------------------
//<auto-generated>
//    This code was generated by a tool.
//    Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
//</auto-generated>
//------------------------------------------
// Generated from :LordConst.csv

using System.Collections.Generic;
using System.IO;
using System.Text;

public class LordConst : FQDev.CSVConfig.ConfigBase
{
    /// <summary>
    /// 默认领主ID
    /// </summary>
    public int DefultLordId => __DefultLordId;
    private int __DefultLordId;

    /// <summary>
    /// 领主解锁章节
    /// </summary>
    public int LordUnlockChapter => __LordUnlockChapter;
    private int __LordUnlockChapter;


    public override void ParseConfigure( string text )
    {
        var lines = text.Replace("\r", "")
            .Split(new[] {"\n"}, System.StringSplitOptions.None);

        var rawData = new Dictionary<string, string>();
        foreach (var line in lines)
        {
            if( line.Length == 0 )
                continue;
            if( line[0] == '\0' ||line[0] == '#' || line[0] == '%' || line[0] == '$')
                continue;

            var rows = line.Split(new[] {","}, System.StringSplitOptions.None);
            if (rows.Length < 3)
            {
                ErrorLogger?.Invoke(string.Format("CommonConst列数不符"));
                return;
            }
        
            rawData.Add(rows[0], rows[1]);
        }
        
        var raw = "";
        if (rawData.TryGetValue("DefultLordId", out raw))
        {
            __DefultLordId = int.Parse(raw);
        }
        if (rawData.TryGetValue("LordUnlockChapter", out raw))
        {
            __LordUnlockChapter = int.Parse(raw);
        }
    }
}