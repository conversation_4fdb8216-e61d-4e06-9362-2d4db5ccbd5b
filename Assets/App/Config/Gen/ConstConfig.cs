//-----------------------------------------
//<auto-generated>
//    This code was generated by a tool.
//    Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
//</auto-generated>
//------------------------------------------
// Generated from :ConstConfig.csv

using System.Collections.Generic;
using System.IO;
using System.Text;

public class ConstConfig : FQDev.CSVConfig.ConfigBase
{
    /// <summary>
    /// 事务的天数页签总数量 
    /// </summary>
    public int ActivityExchangePage => __ActivityExchangePage;
    private int __ActivityExchangePage;

    /// <summary>
    /// 活动道具红点提示指定值
    /// </summary>
    public List<int> ActivityExchangeRedPoint => __ActivityExchangeRedPoint;
    private readonly List<int> __ActivityExchangeRedPoint = new List<int>();

    /// <summary>
    /// 战力系统引导解锁章节（通过）
    /// </summary>
    public int BattlePowerGuideUnlockChapter => __BattlePowerGuideUnlockChapter;
    private int __BattlePowerGuideUnlockChapter;

    /// <summary>
    /// 关卡基金解锁条件（到达）
    /// </summary>
    public int ChapterFundUnlockedCondition => __ChapterFundUnlockedCondition;
    private int __ChapterFundUnlockedCondition;

    /// <summary>
    /// 章节礼包解锁条件（通关）
    /// </summary>
    public int ChapterGiftUnlockedCondition => __ChapterGiftUnlockedCondition;
    private int __ChapterGiftUnlockedCondition;

    /// <summary>
    /// 删除账号输入文本多语言key
    /// </summary>
    public string DeleteAccountInput => __DeleteAccountInput;
    private string __DeleteAccountInput;

    /// <summary>
    /// 获得魔王卡后对应品质角色转化成金币的数量
    /// </summary>
    public Dictionary<int, int> DemonKingExtraTransformationCoin => __DemonKingExtraTransformationCoin;
    private readonly Dictionary<int, int> __DemonKingExtraTransformationCoin = new Dictionary<int, int>();

    /// <summary>
    /// 魔王石
    /// </summary>
    public int DemonKingTransferItemID => __DemonKingTransferItemID;
    private int __DemonKingTransferItemID;

    /// <summary>
    /// Discord默认链接
    /// </summary>
    public string DiscordDefaultUrl => __DiscordDefaultUrl;
    private string __DiscordDefaultUrl;

    /// <summary>
    /// Discord跳转链接
    /// </summary>
    public string DiscordPath => __DiscordPath;
    private string __DiscordPath;

    /// <summary>
    /// 抽卡公式1 值1
    /// </summary>
    public float DrawCard1Value1 => __DrawCard1Value1;
    private float __DrawCard1Value1;

    /// <summary>
    /// 抽卡公式1 值2
    /// </summary>
    public float DrawCard1Value2 => __DrawCard1Value2;
    private float __DrawCard1Value2;

    /// <summary>
    /// 抽卡公式1 值3
    /// </summary>
    public float DrawCard1Value3 => __DrawCard1Value3;
    private float __DrawCard1Value3;

    /// <summary>
    /// 抽卡公式1 值4
    /// </summary>
    public float DrawCard1Value4 => __DrawCard1Value4;
    private float __DrawCard1Value4;

    /// <summary>
    /// 抽卡公式1 值5
    /// </summary>
    public float DrawCard1Value5 => __DrawCard1Value5;
    private float __DrawCard1Value5;

    /// <summary>
    /// 抽卡解锁条件（到达）
    /// </summary>
    public int DrawCardUnlockedCondition => __DrawCardUnlockedCondition;
    private int __DrawCardUnlockedCondition;

    /// <summary>
    /// 魔王试炼每一页关卡数
    /// </summary>
    public int ElementalTrialPageLevelCount => __ElementalTrialPageLevelCount;
    private int __ElementalTrialPageLevelCount;

    /// <summary>
    /// 魔王试炼开启章节（通过）
    /// </summary>
    public int ElementalTrialUnlockChapter => __ElementalTrialUnlockChapter;
    private int __ElementalTrialUnlockChapter;

    /// <summary>
    /// 魔纹解锁章节（通过）
    /// </summary>
    public int ElementTalentUnlockChapter => __ElementTalentUnlockChapter;
    private int __ElementTalentUnlockChapter;

    /// <summary>
    /// 每次开始游戏时体力消耗量
    /// </summary>
    public int EnergyCost => __EnergyCost;
    private int __EnergyCost;

    /// <summary>
    /// 看广告恢复体力值
    /// </summary>
    public int EnergyRecoverAd => __EnergyRecoverAd;
    private int __EnergyRecoverAd;

    /// <summary>
    /// 每日看广告次数
    /// </summary>
    public int EnergyRecoverAdTime => __EnergyRecoverAdTime;
    private int __EnergyRecoverAdTime;

    /// <summary>
    /// 钻石购买体力钻石消耗量
    /// </summary>
    public int EnergyRecoverCostDiamond => __EnergyRecoverCostDiamond;
    private int __EnergyRecoverCostDiamond;

    /// <summary>
    /// 金币购买体力金币消耗量
    /// </summary>
    public int EnergyRecoverCostGold => __EnergyRecoverCostGold;
    private int __EnergyRecoverCostGold;

    /// <summary>
    /// 体力恢复周期时间(s)
    /// </summary>
    public int EnergyRecoverDuration => __EnergyRecoverDuration;
    private int __EnergyRecoverDuration;

    /// <summary>
    /// 体力恢复最大值
    /// </summary>
    public int EnergyRecoverMax => __EnergyRecoverMax;
    private int __EnergyRecoverMax;

    /// <summary>
    /// 购买恢复体力值
    /// </summary>
    public int EnergyRecoverPurchase => __EnergyRecoverPurchase;
    private int __EnergyRecoverPurchase;

    /// <summary>
    /// 每日购买恢复体力次数
    /// </summary>
    public int EnergyRecoverPurchaseTime => __EnergyRecoverPurchaseTime;
    private int __EnergyRecoverPurchaseTime;

    /// <summary>
    /// 首充奖励
    /// </summary>
    public string FirstRechargeReward => __FirstRechargeReward;
    private string __FirstRechargeReward;

    /// <summary>
    /// 首充奖励解锁条件（到达）
    /// </summary>
    public int FirstRechargeUnlockChapter => __FirstRechargeUnlockChapter;
    private int __FirstRechargeUnlockChapter;

    /// <summary>
    /// 三档礼包限时（礼包type：限时时间）
    /// </summary>
    public string GatheringGiftTime => __GatheringGiftTime;
    private string __GatheringGiftTime;

    /// <summary>
    /// 礼包主页最大数量
    /// </summary>
    public int GiftEntranceCount => __GiftEntranceCount;
    private int __GiftEntranceCount;

    /// <summary>
    /// 去广告礼包的giftId
    /// </summary>
    public int GiftNoAdsId => __GiftNoAdsId;
    private int __GiftNoAdsId;

    /// <summary>
    /// 日月周礼包的开启章节
    /// </summary>
    public int GiftPeriodChapterLimit => __GiftPeriodChapterLimit;
    private int __GiftPeriodChapterLimit;

    /// <summary>
    /// 分页常驻礼包（礼包原有的时长为入口存在时长）
    /// </summary>
    public List<int> GiftPermanent => __GiftPermanent;
    private readonly List<int> __GiftPermanent = new List<int>();

    /// <summary>
    /// 成长基金解锁条件（到达）
    /// </summary>
    public int GrowthFundUnlockedCondition => __GrowthFundUnlockedCondition;
    private int __GrowthFundUnlockedCondition;

    /// <summary>
    /// 英雄装备充值消耗
    /// </summary>
    public string HeroEquipmentResetCost => __HeroEquipmentResetCost;
    private string __HeroEquipmentResetCost;

    /// <summary>
    /// 英雄装备功能解锁章节
    /// </summary>
    public int HeroEquipmentUnlockChapter => __HeroEquipmentUnlockChapter;
    private int __HeroEquipmentUnlockChapter;

    /// <summary>
    /// 默认解锁英雄列表
    /// </summary>
    public List<int> HeroInit => __HeroInit;
    private readonly List<int> __HeroInit = new List<int>();

    /// <summary>
    /// 是否使用金币消费
    /// </summary>
    public bool IsEnergyUseGold => __IsEnergyUseGold;
    private bool __IsEnergyUseGold;

    /// <summary>
    /// 主页功能入口最大展示数量
    /// </summary>
    public int MainEntranceCount => __MainEntranceCount;
    private int __MainEntranceCount;

    /// <summary>
    /// 达到章节关闭（新手礼包弹窗）
    /// </summary>
    public int NewcomerGiftClose => __NewcomerGiftClose;
    private int __NewcomerGiftClose;

    /// <summary>
    /// 达到章节开启（新手礼包弹窗）
    /// </summary>
    public int NewcomerGiftOpen => __NewcomerGiftOpen;
    private int __NewcomerGiftOpen;

    /// <summary>
    /// 新手礼包配置(礼包ID|时间_秒|到达章节解锁)
    /// </summary>
    public List<int> NovicePackInfo => __NovicePackInfo;
    private readonly List<int> __NovicePackInfo = new List<int>();

    /// <summary>
    /// 开启战斗故事
    /// </summary>
    public bool OpenFightStory => __OpenFightStory;
    private bool __OpenFightStory;

    /// <summary>
    /// 是否开启去广告礼包
    /// </summary>
    public bool OpenGiftNoAds => __OpenGiftNoAds;
    private bool __OpenGiftNoAds;

    /// <summary>
    /// 后台返回游戏时检测资源版本并弹窗提示
    /// </summary>
    public bool OpenResVersionTipWhenReturnGame => __OpenResVersionTipWhenReturnGame;
    private bool __OpenResVersionTipWhenReturnGame;

    /// <summary>
    /// 离线挂机奖励解锁条件（通关）
    /// </summary>
    public int PatrolRevenueUnlockedCondition => __PatrolRevenueUnlockedCondition;
    private int __PatrolRevenueUnlockedCondition;

    /// <summary>
    /// 随机礼包4个槽位分别开启限制：达到X章节
    /// </summary>
    public List<int> PeriodGiftGroupLimit => __PeriodGiftGroupLimit;
    private readonly List<int> __PeriodGiftGroupLimit = new List<int>();

    /// <summary>
    /// 金猪解锁章节（通关）
    /// </summary>
    public int PiggyBankUnlockCondition => __PiggyBankUnlockCondition;
    private int __PiggyBankUnlockCondition;

    /// <summary>
    /// 排行榜解锁章节（通过）
    /// </summary>
    public int RankListUnlockChapter => __RankListUnlockChapter;
    private int __RankListUnlockChapter;

    /// <summary>
    /// 右边入口最大数量
    /// </summary>
    public int RightEntranceCount => __RightEntranceCount;
    private int __RightEntranceCount;

    /// <summary>
    /// 七天乐解锁条件（到达）
    /// </summary>
    public int SevenDaysUnlockedCondition => __SevenDaysUnlockedCondition;
    private int __SevenDaysUnlockedCondition;

    /// <summary>
    /// 商店购买钻石商品最多显示数量
    /// </summary>
    public int StoreBuyGemProductCount => __StoreBuyGemProductCount;
    private int __StoreBuyGemProductCount;

    /// <summary>
    /// 商店对换金币商品最多显示数量
    /// </summary>
    public int StoreExchangeGoldProductCount => __StoreExchangeGoldProductCount;
    private int __StoreExchangeGoldProductCount;

    /// <summary>
    /// 援护英雄系统开启章节
    /// </summary>
    public int SupportSkillsUnlockChapter => __SupportSkillsUnlockChapter;
    private int __SupportSkillsUnlockChapter;

    /// <summary>
    /// 旅行商人结束时间
    /// </summary>
    public int TravelingMerchantExpireAt => __TravelingMerchantExpireAt;
    private int __TravelingMerchantExpireAt;

    /// <summary>
    /// 旅行商人开启时间
    /// </summary>
    public int TravelingMerchantStartAt => __TravelingMerchantStartAt;
    private int __TravelingMerchantStartAt;

    /// <summary>
    /// 旅行商人解锁章节（达到）
    /// </summary>
    public int TravelingMerchantUnlockCondition => __TravelingMerchantUnlockCondition;
    private int __TravelingMerchantUnlockCondition;

    /// <summary>
    /// 引导跳过最小步骤数
    /// </summary>
    public int TutorialsSkipMinStep => __TutorialsSkipMinStep;
    private int __TutorialsSkipMinStep;

    /// <summary>
    /// 玩家初始获得item
    /// </summary>
    public string UserInit => __UserInit;
    private string __UserInit;


    public override void ParseConfigure( string text )
    {
        var lines = text.Replace("\r", "")
            .Split(new[] {"\n"}, System.StringSplitOptions.None);

        var rawData = new Dictionary<string, string>();
        foreach (var line in lines)
        {
            if( line.Length == 0 )
                continue;
            if( line[0] == '\0' ||line[0] == '#' || line[0] == '%' || line[0] == '$')
                continue;

            var rows = line.Split(new[] {","}, System.StringSplitOptions.None);
            if (rows.Length < 3)
            {
                ErrorLogger?.Invoke(string.Format("CommonConst列数不符"));
                return;
            }
        
            rawData.Add(rows[0], rows[1]);
        }
        
        var raw = "";
        if (rawData.TryGetValue("ActivityExchangePage", out raw))
        {
            __ActivityExchangePage = int.Parse(raw);
        }
        if (rawData.TryGetValue("ActivityExchangeRedPoint", out raw))
        {
            var fields = raw.Split(new string[]{"|"}, System.StringSplitOptions.RemoveEmptyEntries);
            __ActivityExchangeRedPoint.Clear();
            foreach (var f in fields)
            {
                if (string.IsNullOrEmpty(f))
                    continue;
                __ActivityExchangeRedPoint.Add(int.Parse(f));
            }
        }
        if (rawData.TryGetValue("BattlePowerGuideUnlockChapter", out raw))
        {
            __BattlePowerGuideUnlockChapter = int.Parse(raw);
        }
        if (rawData.TryGetValue("ChapterFundUnlockedCondition", out raw))
        {
            __ChapterFundUnlockedCondition = int.Parse(raw);
        }
        if (rawData.TryGetValue("ChapterGiftUnlockedCondition", out raw))
        {
            __ChapterGiftUnlockedCondition = int.Parse(raw);
        }
        if (rawData.TryGetValue("DeleteAccountInput", out raw))
        {
            __DeleteAccountInput = raw;
        }
        if (rawData.TryGetValue("DemonKingExtraTransformationCoin", out raw))
        {
            var keyValues = raw.Split(new string[]{"|"}, System.StringSplitOptions.RemoveEmptyEntries);
            __DemonKingExtraTransformationCoin.Clear();
            foreach (var kv in keyValues)
            {
                if (string.IsNullOrEmpty(kv))
                    continue;
                var split = kv.Split(':');
                if (split.Length != 2)
                    continue;
                var k = int.Parse(split[0]);
                var v = int.Parse(split[1]);
                __DemonKingExtraTransformationCoin.Add(k, v);
            }
        }
        if (rawData.TryGetValue("DemonKingTransferItemID", out raw))
        {
            __DemonKingTransferItemID = int.Parse(raw);
        }
        if (rawData.TryGetValue("DiscordDefaultUrl", out raw))
        {
            __DiscordDefaultUrl = raw;
        }
        if (rawData.TryGetValue("DiscordPath", out raw))
        {
            __DiscordPath = raw;
        }
        if (rawData.TryGetValue("DrawCard1Value1", out raw))
        {
            __DrawCard1Value1 = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DrawCard1Value2", out raw))
        {
            __DrawCard1Value2 = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DrawCard1Value3", out raw))
        {
            __DrawCard1Value3 = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DrawCard1Value4", out raw))
        {
            __DrawCard1Value4 = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DrawCard1Value5", out raw))
        {
            __DrawCard1Value5 = float.Parse(raw, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (rawData.TryGetValue("DrawCardUnlockedCondition", out raw))
        {
            __DrawCardUnlockedCondition = int.Parse(raw);
        }
        if (rawData.TryGetValue("ElementalTrialPageLevelCount", out raw))
        {
            __ElementalTrialPageLevelCount = int.Parse(raw);
        }
        if (rawData.TryGetValue("ElementalTrialUnlockChapter", out raw))
        {
            __ElementalTrialUnlockChapter = int.Parse(raw);
        }
        if (rawData.TryGetValue("ElementTalentUnlockChapter", out raw))
        {
            __ElementTalentUnlockChapter = int.Parse(raw);
        }
        if (rawData.TryGetValue("EnergyCost", out raw))
        {
            __EnergyCost = int.Parse(raw);
        }
        if (rawData.TryGetValue("EnergyRecoverAd", out raw))
        {
            __EnergyRecoverAd = int.Parse(raw);
        }
        if (rawData.TryGetValue("EnergyRecoverAdTime", out raw))
        {
            __EnergyRecoverAdTime = int.Parse(raw);
        }
        if (rawData.TryGetValue("EnergyRecoverCostDiamond", out raw))
        {
            __EnergyRecoverCostDiamond = int.Parse(raw);
        }
        if (rawData.TryGetValue("EnergyRecoverCostGold", out raw))
        {
            __EnergyRecoverCostGold = int.Parse(raw);
        }
        if (rawData.TryGetValue("EnergyRecoverDuration", out raw))
        {
            __EnergyRecoverDuration = int.Parse(raw);
        }
        if (rawData.TryGetValue("EnergyRecoverMax", out raw))
        {
            __EnergyRecoverMax = int.Parse(raw);
        }
        if (rawData.TryGetValue("EnergyRecoverPurchase", out raw))
        {
            __EnergyRecoverPurchase = int.Parse(raw);
        }
        if (rawData.TryGetValue("EnergyRecoverPurchaseTime", out raw))
        {
            __EnergyRecoverPurchaseTime = int.Parse(raw);
        }
        if (rawData.TryGetValue("FirstRechargeReward", out raw))
        {
            __FirstRechargeReward = raw;
        }
        if (rawData.TryGetValue("FirstRechargeUnlockChapter", out raw))
        {
            __FirstRechargeUnlockChapter = int.Parse(raw);
        }
        if (rawData.TryGetValue("GatheringGiftTime", out raw))
        {
            __GatheringGiftTime = raw;
        }
        if (rawData.TryGetValue("GiftEntranceCount", out raw))
        {
            __GiftEntranceCount = int.Parse(raw);
        }
        if (rawData.TryGetValue("GiftNoAdsId", out raw))
        {
            __GiftNoAdsId = int.Parse(raw);
        }
        if (rawData.TryGetValue("GiftPeriodChapterLimit", out raw))
        {
            __GiftPeriodChapterLimit = int.Parse(raw);
        }
        if (rawData.TryGetValue("GiftPermanent", out raw))
        {
            var fields = raw.Split(new string[]{"|"}, System.StringSplitOptions.RemoveEmptyEntries);
            __GiftPermanent.Clear();
            foreach (var f in fields)
            {
                if (string.IsNullOrEmpty(f))
                    continue;
                __GiftPermanent.Add(int.Parse(f));
            }
        }
        if (rawData.TryGetValue("GrowthFundUnlockedCondition", out raw))
        {
            __GrowthFundUnlockedCondition = int.Parse(raw);
        }
        if (rawData.TryGetValue("HeroEquipmentResetCost", out raw))
        {
            __HeroEquipmentResetCost = raw;
        }
        if (rawData.TryGetValue("HeroEquipmentUnlockChapter", out raw))
        {
            __HeroEquipmentUnlockChapter = int.Parse(raw);
        }
        if (rawData.TryGetValue("HeroInit", out raw))
        {
            var fields = raw.Split(new string[]{"|"}, System.StringSplitOptions.RemoveEmptyEntries);
            __HeroInit.Clear();
            foreach (var f in fields)
            {
                if (string.IsNullOrEmpty(f))
                    continue;
                __HeroInit.Add(int.Parse(f));
            }
        }
        if (rawData.TryGetValue("IsEnergyUseGold", out raw))
        {
            __IsEnergyUseGold = string.Equals(bool.TrueString, raw, System.StringComparison.InvariantCultureIgnoreCase);
        }
        if (rawData.TryGetValue("MainEntranceCount", out raw))
        {
            __MainEntranceCount = int.Parse(raw);
        }
        if (rawData.TryGetValue("NewcomerGiftClose", out raw))
        {
            __NewcomerGiftClose = int.Parse(raw);
        }
        if (rawData.TryGetValue("NewcomerGiftOpen", out raw))
        {
            __NewcomerGiftOpen = int.Parse(raw);
        }
        if (rawData.TryGetValue("NovicePackInfo", out raw))
        {
            var fields = raw.Split(new string[]{"|"}, System.StringSplitOptions.RemoveEmptyEntries);
            __NovicePackInfo.Clear();
            foreach (var f in fields)
            {
                if (string.IsNullOrEmpty(f))
                    continue;
                __NovicePackInfo.Add(int.Parse(f));
            }
        }
        if (rawData.TryGetValue("OpenFightStory", out raw))
        {
            __OpenFightStory = string.Equals(bool.TrueString, raw, System.StringComparison.InvariantCultureIgnoreCase);
        }
        if (rawData.TryGetValue("OpenGiftNoAds", out raw))
        {
            __OpenGiftNoAds = string.Equals(bool.TrueString, raw, System.StringComparison.InvariantCultureIgnoreCase);
        }
        if (rawData.TryGetValue("OpenResVersionTipWhenReturnGame", out raw))
        {
            __OpenResVersionTipWhenReturnGame = string.Equals(bool.TrueString, raw, System.StringComparison.InvariantCultureIgnoreCase);
        }
        if (rawData.TryGetValue("PatrolRevenueUnlockedCondition", out raw))
        {
            __PatrolRevenueUnlockedCondition = int.Parse(raw);
        }
        if (rawData.TryGetValue("PeriodGiftGroupLimit", out raw))
        {
            var fields = raw.Split(new string[]{"|"}, System.StringSplitOptions.RemoveEmptyEntries);
            __PeriodGiftGroupLimit.Clear();
            foreach (var f in fields)
            {
                if (string.IsNullOrEmpty(f))
                    continue;
                __PeriodGiftGroupLimit.Add(int.Parse(f));
            }
        }
        if (rawData.TryGetValue("PiggyBankUnlockCondition", out raw))
        {
            __PiggyBankUnlockCondition = int.Parse(raw);
        }
        if (rawData.TryGetValue("RankListUnlockChapter", out raw))
        {
            __RankListUnlockChapter = int.Parse(raw);
        }
        if (rawData.TryGetValue("RightEntranceCount", out raw))
        {
            __RightEntranceCount = int.Parse(raw);
        }
        if (rawData.TryGetValue("SevenDaysUnlockedCondition", out raw))
        {
            __SevenDaysUnlockedCondition = int.Parse(raw);
        }
        if (rawData.TryGetValue("StoreBuyGemProductCount", out raw))
        {
            __StoreBuyGemProductCount = int.Parse(raw);
        }
        if (rawData.TryGetValue("StoreExchangeGoldProductCount", out raw))
        {
            __StoreExchangeGoldProductCount = int.Parse(raw);
        }
        if (rawData.TryGetValue("SupportSkillsUnlockChapter", out raw))
        {
            __SupportSkillsUnlockChapter = int.Parse(raw);
        }
        if (rawData.TryGetValue("TravelingMerchantExpireAt", out raw))
        {
            __TravelingMerchantExpireAt = int.Parse(raw);
        }
        if (rawData.TryGetValue("TravelingMerchantStartAt", out raw))
        {
            __TravelingMerchantStartAt = int.Parse(raw);
        }
        if (rawData.TryGetValue("TravelingMerchantUnlockCondition", out raw))
        {
            __TravelingMerchantUnlockCondition = int.Parse(raw);
        }
        if (rawData.TryGetValue("TutorialsSkipMinStep", out raw))
        {
            __TutorialsSkipMinStep = int.Parse(raw);
        }
        if (rawData.TryGetValue("UserInit", out raw))
        {
            __UserInit = raw;
        }
    }
}