//-----------------------------------------
//<auto-generated>
//    This code was generated by a tool.
//    Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
//</auto-generated>
//------------------------------------------
// Generated from :ElementTalentConfig.csv

using System.Collections.Generic;
using System.IO;
using System.Text;

public partial class ElementTalentConfig
{
    private int __Id;
    public int Id
    {
        internal set{ __Id = value; }
        get{ return __Id; }
    }
    private string __Name;
    public string Name
    {
        internal set{ __Name = value; }
        get{ return __Name; }
    }
    private AssetRef __Bg;
    public AssetRef Bg
    {
        internal set{ __Bg = value; }
        get{ return __Bg; }
    }
    private AssetRef __Prefab;
    public AssetRef Prefab
    {
        internal set{ __Prefab = value; }
        get{ return __Prefab; }
    }
    private AssetRef __Icon;
    public AssetRef Icon
    {
        internal set{ __Icon = value; }
        get{ return __Icon; }
    }
}

public class ElementTalentConfigTable : FQDev.CSVConfig.ConfigBase
{
    static string __config_file_name = "ElementTalentConfig";
    public static string ConfigName => __config_file_name;
    static int __col_count = 5;

    private readonly Dictionary<int,ElementTalentConfig> __rows = new Dictionary<int,ElementTalentConfig>();
    public Dictionary<int,ElementTalentConfig> Rows => __rows;
    // key = ElementTalentConfig.Id
    public ElementTalentConfig GetRowData( int key )
    {
        ElementTalentConfig tmp; 
        if( !__rows.TryGetValue( key, out tmp ) )
            LogError("Can't get row data by key:{0} Config:{1}", key, __config_file_name);
        return tmp;
    }

    public int GetRowCount()
    {
        return __rows.Count;
    }

    public override void ParseConfigure( string text )
    {

        __rows.Clear();
        string splace_text = text.Replace("\r","");
        string[] lines = splace_text.Split(new string[]{"\n"}, System.StringSplitOptions.None);
        
        foreach( string line in lines )
        {
            if( line.Length == 0 )
                continue;
            if( line[0] == '\0' ||line[0] == '#' || line[0] == '%' || line[0] == '$')
                continue;


            string[] rowdata = line.Split(new string[]{","}, System.StringSplitOptions.None);
            if( rowdata.Length != __col_count )
            {
                LogError(string.Format("{0}列数与原配置不符", __config_file_name));
                return;
            }
            int counter = 0;
            ElementTalentConfig tmp = new ElementTalentConfig();

            string strId = rowdata[counter++];
            if (!string.IsNullOrEmpty(strId))
                tmp.Id = int.Parse(strId);
            tmp.Name = rowdata[counter++];
            tmp.Bg = AssetRef.Parse(rowdata[counter++]);
            tmp.Prefab = AssetRef.Parse(rowdata[counter++]);
            tmp.Icon = AssetRef.Parse(rowdata[counter++]);
            __rows.Add( tmp.Id, tmp );
        }
    }
}