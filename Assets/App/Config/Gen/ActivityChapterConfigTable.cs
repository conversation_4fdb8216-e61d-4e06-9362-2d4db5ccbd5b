//-----------------------------------------
//<auto-generated>
//    This code was generated by a tool.
//    Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
//</auto-generated>
//------------------------------------------
// Generated from :ActivityChapterConfig.csv

using System.Collections.Generic;
using System.IO;
using System.Text;

public partial class ActivityChapterConfig
{
    private int __Id;
    public int Id
    {
        internal set{ __Id = value; }
        get{ return __Id; }
    }
    private string __Name;
    public string Name
    {
        internal set{ __Name = value; }
        get{ return __Name; }
    }
    private AssetRef __Background;
    public AssetRef Background
    {
        internal set{ __Background = value; }
        get{ return __Background; }
    }
    private AssetRef __Icon;
    public AssetRef Icon
    {
        internal set{ __Icon = value; }
        get{ return __Icon; }
    }
    public List<int> Weekdays = new List<int>();
    public List<string> StartDuration = new List<string>();
    private string __Desc;
    public string Desc
    {
        internal set{ __Desc = value; }
        get{ return __Desc; }
    }
    public Dictionary<int,int> Spend = new Dictionary<int,int>();
    private int __MaxCount;
    public int MaxCount
    {
        internal set{ __MaxCount = value; }
        get{ return __MaxCount; }
    }
    public List<int> ChapterIds = new List<int>();
    private int __CanClaim;
    public int CanClaim
    {
        internal set{ __CanClaim = value; }
        get{ return __CanClaim; }
    }
    private string __Award;
    public string Award
    {
        internal set{ __Award = value; }
        get{ return __Award; }
    }
    private AssetRef __EntryBg;
    public AssetRef EntryBg
    {
        internal set{ __EntryBg = value; }
        get{ return __EntryBg; }
    }
    private AssetRef __EntryTitleBg;
    public AssetRef EntryTitleBg
    {
        internal set{ __EntryTitleBg = value; }
        get{ return __EntryTitleBg; }
    }
    public List<float> Times = new List<float>();
    private int __TicketId;
    public int TicketId
    {
        internal set{ __TicketId = value; }
        get{ return __TicketId; }
    }
}

public class ActivityChapterConfigTable : FQDev.CSVConfig.ConfigBase
{
    static string __config_file_name = "ActivityChapterConfig";
    public static string ConfigName => __config_file_name;
    static int __col_count = 16;

    private readonly Dictionary<int,ActivityChapterConfig> __rows = new Dictionary<int,ActivityChapterConfig>();
    public Dictionary<int,ActivityChapterConfig> Rows => __rows;
    // key = ActivityChapterConfig.Id
    public ActivityChapterConfig GetRowData( int key )
    {
        ActivityChapterConfig tmp; 
        if( !__rows.TryGetValue( key, out tmp ) )
            LogError("Can't get row data by key:{0} Config:{1}", key, __config_file_name);
        return tmp;
    }

    public int GetRowCount()
    {
        return __rows.Count;
    }

    public override void ParseConfigure( string text )
    {

        __rows.Clear();
        string splace_text = text.Replace("\r","");
        string[] lines = splace_text.Split(new string[]{"\n"}, System.StringSplitOptions.None);
        
        foreach( string line in lines )
        {
            if( line.Length == 0 )
                continue;
            if( line[0] == '\0' ||line[0] == '#' || line[0] == '%' || line[0] == '$')
                continue;


            string[] rowdata = line.Split(new string[]{","}, System.StringSplitOptions.None);
            if( rowdata.Length != __col_count )
            {
                LogError(string.Format("{0}列数与原配置不符", __config_file_name));
                return;
            }
            int counter = 0;
            ActivityChapterConfig tmp = new ActivityChapterConfig();

            string strId = rowdata[counter++];
            if (!string.IsNullOrEmpty(strId))
                tmp.Id = int.Parse(strId);
            tmp.Name = rowdata[counter++];
            tmp.Background = AssetRef.Parse(rowdata[counter++]);
            tmp.Icon = AssetRef.Parse(rowdata[counter++]);
            do{
                string[] llc = rowdata[counter++].Split(new string[]{"|"} ,System.StringSplitOptions.None);
                foreach( string vvv in llc )
                {
                    if(vvv.Length == 0 ) 
                        continue;
                    tmp.Weekdays.Add( int.Parse(vvv) );
                }
            }while(false);
            do{
                string[] llc = rowdata[counter++].Split(new string[]{"|"} ,System.StringSplitOptions.None);
                foreach( string vvv in llc )
                {
                    if(vvv.Length == 0 ) 
                        continue;
                    tmp.StartDuration.Add( vvv );
                }
            }while(false);
            tmp.Desc = rowdata[counter++];
            do{
                string[] v1 = rowdata[counter++].Split(new string[]{ "|"},System.StringSplitOptions.None);
                foreach( string layer1 in v1 )
                {
                    if( layer1.Length == 0 )
                        continue;
                    string[] v2 = layer1.Split(new string[]{":"},System.StringSplitOptions.None);
                    if( v2.Length != 2 )
                        continue;
                    tmp.Spend.Add( int.Parse(v2[0]),int.Parse(v2[1]));
                }
            }while(false);

            string strMaxCount = rowdata[counter++];
            if (!string.IsNullOrEmpty(strMaxCount))
                tmp.MaxCount = int.Parse(strMaxCount);
            do{
                string[] llc = rowdata[counter++].Split(new string[]{"|"} ,System.StringSplitOptions.None);
                foreach( string vvv in llc )
                {
                    if(vvv.Length == 0 ) 
                        continue;
                    tmp.ChapterIds.Add( int.Parse(vvv) );
                }
            }while(false);

            string strCanClaim = rowdata[counter++];
            if (!string.IsNullOrEmpty(strCanClaim))
                tmp.CanClaim = int.Parse(strCanClaim);
            tmp.Award = rowdata[counter++];
            tmp.EntryBg = AssetRef.Parse(rowdata[counter++]);
            tmp.EntryTitleBg = AssetRef.Parse(rowdata[counter++]);
            do{
                string[] llc = rowdata[counter++].Split(new string[]{"|"} ,System.StringSplitOptions.None);
                foreach( string vvv in llc )
                {
                    if(vvv.Length == 0 ) 
                        continue;
                    tmp.Times.Add( float.Parse(vvv, System.Globalization.CultureInfo.InvariantCulture) );
                }
            }while(false);

            string strTicketId = rowdata[counter++];
            if (!string.IsNullOrEmpty(strTicketId))
                tmp.TicketId = int.Parse(strTicketId);
            __rows.Add( tmp.Id, tmp );
        }
    }
}