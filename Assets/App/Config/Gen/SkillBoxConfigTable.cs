//-----------------------------------------
//<auto-generated>
//    This code was generated by a tool.
//    Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
//</auto-generated>
//------------------------------------------
// Generated from :SkillBoxConfig.csv

using System.Collections.Generic;
using System.IO;
using System.Text;

public class SkillBoxConfig
{
    private int __Id;
    public int Id
    {
        internal set{ __Id = value; }
        get{ return __Id; }
    }
    public List<int> Grades = new List<int>();
    private int __IncludeDownGrade;
    public int IncludeDownGrade
    {
        internal set{ __IncludeDownGrade = value; }
        get{ return __IncludeDownGrade; }
    }
    private int __RequireTag;
    public int RequireTag
    {
        internal set{ __RequireTag = value; }
        get{ return __RequireTag; }
    }
}

public class SkillBoxConfigTable : FQDev.CSVConfig.ConfigBase
{
    static string __config_file_name = "SkillBoxConfig";
    public static string ConfigName => __config_file_name;
    static int __col_count = 4;

    private readonly Dictionary<int,SkillBoxConfig> __rows = new Dictionary<int,SkillBoxConfig>();
    public Dictionary<int,SkillBoxConfig> Rows => __rows;
    // key = SkillBoxConfig.Id
    public SkillBoxConfig GetRowData( int key )
    {
        SkillBoxConfig tmp; 
        if( !__rows.TryGetValue( key, out tmp ) )
            LogError("Can't get row data by key:{0} Config:{1}", key, __config_file_name);
        return tmp;
    }

    public int GetRowCount()
    {
        return __rows.Count;
    }

    public override void ParseConfigure( string text )
    {

        __rows.Clear();
        string splace_text = text.Replace("\r","");
        string[] lines = splace_text.Split(new string[]{"\n"}, System.StringSplitOptions.None);
        
        foreach( string line in lines )
        {
            if( line.Length == 0 )
                continue;
            if( line[0] == '\0' ||line[0] == '#' || line[0] == '%' || line[0] == '$')
                continue;


            string[] rowdata = line.Split(new string[]{","}, System.StringSplitOptions.None);
            if( rowdata.Length != __col_count )
            {
                LogError(string.Format("{0}列数与原配置不符", __config_file_name));
                return;
            }
            int counter = 0;
            SkillBoxConfig tmp = new SkillBoxConfig();

            string strId = rowdata[counter++];
            if (!string.IsNullOrEmpty(strId))
                tmp.Id = int.Parse(strId);
            do{
                string[] llc = rowdata[counter++].Split(new string[]{"|"} ,System.StringSplitOptions.None);
                foreach( string vvv in llc )
                {
                    if(vvv.Length == 0 ) 
                        continue;
                    tmp.Grades.Add( int.Parse(vvv) );
                }
            }while(false);

            string strIncludeDownGrade = rowdata[counter++];
            if (!string.IsNullOrEmpty(strIncludeDownGrade))
                tmp.IncludeDownGrade = int.Parse(strIncludeDownGrade);

            string strRequireTag = rowdata[counter++];
            if (!string.IsNullOrEmpty(strRequireTag))
                tmp.RequireTag = int.Parse(strRequireTag);
            __rows.Add( tmp.Id, tmp );
        }
        PostProcess();
    }

    protected virtual void PostProcess() {}
}