//-----------------------------------------
//<auto-generated>
//    This code was generated by a tool.
//    Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
//</auto-generated>
//------------------------------------------
// Generated from :ChapterWave.csv

using System.Collections.Generic;
using System.IO;
using System.Text;

public partial class ChapterWave
{
    private int __Day;
    public int Day
    {
        internal set{ __Day = value; }
        get{ return __Day; }
    }
    private int __Type;
    public int Type
    {
        internal set{ __Type = value; }
        get{ return __Type; }
    }
    public List<IntPair> SkillBoxes = new List<IntPair>();
    public List<int> StepRange = new List<int>();
    public List<App.GamePlay.EnemyGroupInfo> EnemyGroup = new List<App.GamePlay.EnemyGroupInfo>();
    public List<App.GamePlay.FixedEnemyInfo> FixedEnemyGroup = new List<App.GamePlay.FixedEnemyInfo>();
    public Dictionary<int,float> ResistanceFactor = new Dictionary<int,float>();
    public Dictionary<int,float> WeaknessFactor = new Dictionary<int,float>();
    private float __HpRatio;
    public float HpRatio
    {
        set{ __HpRatio = value; }
        get{ return __HpRatio; }
    }
    private float __AttackRatio;
    public float AttackRatio
    {
        set{ __AttackRatio = value; }
        get{ return __AttackRatio; }
    }
    private float __StepHpRatioAdded;
    public float StepHpRatioAdded
    {
        set{ __StepHpRatioAdded = value; }
        get{ return __StepHpRatioAdded; }
    }
    private float __StepAttackRatioAdded;
    public float StepAttackRatioAdded
    {
        set{ __StepAttackRatioAdded = value; }
        get{ return __StepAttackRatioAdded; }
    }
    private int __CardCount;
    public int CardCount
    {
        internal set{ __CardCount = value; }
        get{ return __CardCount; }
    }
    public Dictionary<int,int> ItemCount = new Dictionary<int,int>();
    private int __TotalExp;
    public int TotalExp
    {
        internal set{ __TotalExp = value; }
        get{ return __TotalExp; }
    }
}

public class ChapterWaveTable : FQDev.CSVConfig.ConfigBase
{
    static string __config_file_name = "ChapterWave";
    public static string ConfigName => __config_file_name;
    static int __col_count = 15;

    private readonly Dictionary<int,ChapterWave> __rows = new Dictionary<int,ChapterWave>();
    public Dictionary<int,ChapterWave> Rows => __rows;
    // key = ChapterWave.Day
    public ChapterWave GetRowData( int key )
    {
        ChapterWave tmp; 
        if( !__rows.TryGetValue( key, out tmp ) )
            LogError("Can't get row data by key:{0} Config:{1}", key, __config_file_name);
        return tmp;
    }

    public int GetRowCount()
    {
        return __rows.Count;
    }

    public override void ParseConfigure( string text )
    {

        __rows.Clear();
        string splace_text = text.Replace("\r","");
        string[] lines = splace_text.Split(new string[]{"\n"}, System.StringSplitOptions.None);
        
        foreach( string line in lines )
        {
            if( line.Length == 0 )
                continue;
            if( line[0] == '\0' ||line[0] == '#' || line[0] == '%' || line[0] == '$')
                continue;


            string[] rowdata = line.Split(new string[]{","}, System.StringSplitOptions.None);
            if( rowdata.Length != __col_count )
            {
                LogError(string.Format("{0}列数与原配置不符", __config_file_name));
                return;
            }
            int counter = 0;
            ChapterWave tmp = new ChapterWave();

            string strDay = rowdata[counter++];
            if (!string.IsNullOrEmpty(strDay))
                tmp.Day = int.Parse(strDay);

            string strType = rowdata[counter++];
            if (!string.IsNullOrEmpty(strType))
                tmp.Type = int.Parse(strType);
            do{
                string[] llc = rowdata[counter++].Split(new string[]{"|"} ,System.StringSplitOptions.None);
                foreach( string vvv in llc )
                {
                    if(vvv.Length == 0 ) 
                        continue;
                    tmp.SkillBoxes.Add( IntPair.Parse(vvv) );
                }
            }while(false);
            do{
                string[] llc = rowdata[counter++].Split(new string[]{"|"} ,System.StringSplitOptions.None);
                foreach( string vvv in llc )
                {
                    if(vvv.Length == 0 ) 
                        continue;
                    tmp.StepRange.Add( int.Parse(vvv) );
                }
            }while(false);
            do{
                string[] llc = rowdata[counter++].Split(new string[]{"|"} ,System.StringSplitOptions.None);
                foreach( string vvv in llc )
                {
                    if(vvv.Length == 0 ) 
                        continue;
                    tmp.EnemyGroup.Add( App.GamePlay.EnemyGroupInfo.Parse(vvv) );
                }
            }while(false);
            do{
                string[] llc = rowdata[counter++].Split(new string[]{"|"} ,System.StringSplitOptions.None);
                foreach( string vvv in llc )
                {
                    if(vvv.Length == 0 ) 
                        continue;
                    tmp.FixedEnemyGroup.Add( App.GamePlay.FixedEnemyInfo.Parse(vvv) );
                }
            }while(false);
            do{
                string[] v1 = rowdata[counter++].Split(new string[]{ "|"},System.StringSplitOptions.None);
                foreach( string layer1 in v1 )
                {
                    if( layer1.Length == 0 )
                        continue;
                    string[] v2 = layer1.Split(new string[]{":"},System.StringSplitOptions.None);
                    if( v2.Length != 2 )
                        continue;
                    tmp.ResistanceFactor.Add( int.Parse(v2[0]),float.Parse(v2[1], System.Globalization.CultureInfo.InvariantCulture));
                }
            }while(false);
            do{
                string[] v1 = rowdata[counter++].Split(new string[]{ "|"},System.StringSplitOptions.None);
                foreach( string layer1 in v1 )
                {
                    if( layer1.Length == 0 )
                        continue;
                    string[] v2 = layer1.Split(new string[]{":"},System.StringSplitOptions.None);
                    if( v2.Length != 2 )
                        continue;
                    tmp.WeaknessFactor.Add( int.Parse(v2[0]),float.Parse(v2[1], System.Globalization.CultureInfo.InvariantCulture));
                }
            }while(false);

            string strHpRatio = rowdata[counter++];
            if (!string.IsNullOrEmpty(strHpRatio))
                tmp.HpRatio = float.Parse(strHpRatio, System.Globalization.CultureInfo.InvariantCulture);

            string strAttackRatio = rowdata[counter++];
            if (!string.IsNullOrEmpty(strAttackRatio))
                tmp.AttackRatio = float.Parse(strAttackRatio, System.Globalization.CultureInfo.InvariantCulture);

            string strStepHpRatioAdded = rowdata[counter++];
            if (!string.IsNullOrEmpty(strStepHpRatioAdded))
                tmp.StepHpRatioAdded = float.Parse(strStepHpRatioAdded, System.Globalization.CultureInfo.InvariantCulture);

            string strStepAttackRatioAdded = rowdata[counter++];
            if (!string.IsNullOrEmpty(strStepAttackRatioAdded))
                tmp.StepAttackRatioAdded = float.Parse(strStepAttackRatioAdded, System.Globalization.CultureInfo.InvariantCulture);

            string strCardCount = rowdata[counter++];
            if (!string.IsNullOrEmpty(strCardCount))
                tmp.CardCount = int.Parse(strCardCount);
            do{
                string[] v1 = rowdata[counter++].Split(new string[]{ "|"},System.StringSplitOptions.None);
                foreach( string layer1 in v1 )
                {
                    if( layer1.Length == 0 )
                        continue;
                    string[] v2 = layer1.Split(new string[]{":"},System.StringSplitOptions.None);
                    if( v2.Length != 2 )
                        continue;
                    tmp.ItemCount.Add( int.Parse(v2[0]),int.Parse(v2[1]));
                }
            }while(false);

            string strTotalExp = rowdata[counter++];
            if (!string.IsNullOrEmpty(strTotalExp))
                tmp.TotalExp = int.Parse(strTotalExp);
            __rows.Add( tmp.Day, tmp );
        }
    }
}