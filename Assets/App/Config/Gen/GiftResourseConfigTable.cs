//-----------------------------------------
//<auto-generated>
//    This code was generated by a tool.
//    Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
//</auto-generated>
//------------------------------------------
// Generated from :GiftResourseConfig.csv

using System.Collections.Generic;
using System.IO;
using System.Text;

public partial class GiftResourseConfig
{
    private int __Id;
    public int Id
    {
        internal set{ __Id = value; }
        get{ return __Id; }
    }
    private string __Desc;
    public string Desc
    {
        internal set{ __Desc = value; }
        get{ return __Desc; }
    }
    private AssetRef __Head;
    public AssetRef Head
    {
        internal set{ __Head = value; }
        get{ return __Head; }
    }
}

public class GiftResourseConfigTable : FQDev.CSVConfig.ConfigBase
{
    static string __config_file_name = "GiftResourseConfig";
    public static string ConfigName => __config_file_name;
    static int __col_count = 3;

    private readonly Dictionary<int,GiftResourseConfig> __rows = new Dictionary<int,GiftResourseConfig>();
    public Dictionary<int,GiftResourseConfig> Rows => __rows;
    // key = GiftResourseConfig.Id
    public GiftResourseConfig GetRowData( int key )
    {
        GiftResourseConfig tmp; 
        if( !__rows.TryGetValue( key, out tmp ) )
            LogError("Can't get row data by key:{0} Config:{1}", key, __config_file_name);
        return tmp;
    }

    public int GetRowCount()
    {
        return __rows.Count;
    }

    public override void ParseConfigure( string text )
    {

        __rows.Clear();
        string splace_text = text.Replace("\r","");
        string[] lines = splace_text.Split(new string[]{"\n"}, System.StringSplitOptions.None);
        
        foreach( string line in lines )
        {
            if( line.Length == 0 )
                continue;
            if( line[0] == '\0' ||line[0] == '#' || line[0] == '%' || line[0] == '$')
                continue;


            string[] rowdata = line.Split(new string[]{","}, System.StringSplitOptions.None);
            if( rowdata.Length != __col_count )
            {
                LogError(string.Format("{0}列数与原配置不符", __config_file_name));
                return;
            }
            int counter = 0;
            GiftResourseConfig tmp = new GiftResourseConfig();

            string strId = rowdata[counter++];
            if (!string.IsNullOrEmpty(strId))
                tmp.Id = int.Parse(strId);
            tmp.Desc = rowdata[counter++];
            tmp.Head = AssetRef.Parse(rowdata[counter++]);
            __rows.Add( tmp.Id, tmp );
        }
    }
}