//-----------------------------------------
//<auto-generated>
//    This code was generated by a tool.
//    Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
//</auto-generated>
//------------------------------------------
// Generated from :SkillCoreConfig.csv

using System.Collections.Generic;
using System.IO;
using System.Text;

public partial class SkillCoreConfig
{
    private int __Id;
    public int Id
    {
        internal set{ __Id = value; }
        get{ return __Id; }
    }
    private int __HeroId;
    public int HeroId
    {
        internal set{ __HeroId = value; }
        get{ return __HeroId; }
    }
    private int __UnlockLevel;
    public int UnlockLevel
    {
        internal set{ __UnlockLevel = value; }
        get{ return __UnlockLevel; }
    }
    private int __Quality;
    public int Quality
    {
        internal set{ __Quality = value; }
        get{ return __Quality; }
    }
    private AssetRef __Icon;
    public AssetRef Icon
    {
        internal set{ __Icon = value; }
        get{ return __Icon; }
    }
    private string __Name;
    public string Name
    {
        internal set{ __Name = value; }
        get{ return __Name; }
    }
    private string __Des;
    public string Des
    {
        internal set{ __Des = value; }
        get{ return __Des; }
    }
    private string __Sketch;
    public string Sketch
    {
        internal set{ __Sketch = value; }
        get{ return __Sketch; }
    }
    public List<int> RelationHeroes = new List<int>();
}

public class SkillCoreConfigTable : FQDev.CSVConfig.ConfigBase
{
    static string __config_file_name = "SkillCoreConfig";
    public static string ConfigName => __config_file_name;
    static int __col_count = 9;

    private readonly Dictionary<int,SkillCoreConfig> __rows = new Dictionary<int,SkillCoreConfig>();
    public Dictionary<int,SkillCoreConfig> Rows => __rows;
    // key = SkillCoreConfig.Id
    public SkillCoreConfig GetRowData( int key )
    {
        SkillCoreConfig tmp; 
        if( !__rows.TryGetValue( key, out tmp ) )
            LogError("Can't get row data by key:{0} Config:{1}", key, __config_file_name);
        return tmp;
    }

    public int GetRowCount()
    {
        return __rows.Count;
    }

    public override void ParseConfigure( string text )
    {

        __rows.Clear();
        string splace_text = text.Replace("\r","");
        string[] lines = splace_text.Split(new string[]{"\n"}, System.StringSplitOptions.None);
        
        foreach( string line in lines )
        {
            if( line.Length == 0 )
                continue;
            if( line[0] == '\0' ||line[0] == '#' || line[0] == '%' || line[0] == '$')
                continue;


            string[] rowdata = line.Split(new string[]{","}, System.StringSplitOptions.None);
            if( rowdata.Length != __col_count )
            {
                LogError(string.Format("{0}列数与原配置不符", __config_file_name));
                return;
            }
            int counter = 0;
            SkillCoreConfig tmp = new SkillCoreConfig();

            string strId = rowdata[counter++];
            if (!string.IsNullOrEmpty(strId))
                tmp.Id = int.Parse(strId);

            string strHeroId = rowdata[counter++];
            if (!string.IsNullOrEmpty(strHeroId))
                tmp.HeroId = int.Parse(strHeroId);

            string strUnlockLevel = rowdata[counter++];
            if (!string.IsNullOrEmpty(strUnlockLevel))
                tmp.UnlockLevel = int.Parse(strUnlockLevel);

            string strQuality = rowdata[counter++];
            if (!string.IsNullOrEmpty(strQuality))
                tmp.Quality = int.Parse(strQuality);
            tmp.Icon = AssetRef.Parse(rowdata[counter++]);
            tmp.Name = rowdata[counter++];
            tmp.Des = rowdata[counter++];
            tmp.Sketch = rowdata[counter++];
            do{
                string[] llc = rowdata[counter++].Split(new string[]{"|"} ,System.StringSplitOptions.None);
                foreach( string vvv in llc )
                {
                    if(vvv.Length == 0 ) 
                        continue;
                    tmp.RelationHeroes.Add( int.Parse(vvv) );
                }
            }while(false);
            __rows.Add( tmp.Id, tmp );
        }
    }
}