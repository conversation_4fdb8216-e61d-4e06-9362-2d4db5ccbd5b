//-----------------------------------------
//<auto-generated>
//    This code was generated by a tool.
//    Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
//</auto-generated>
//------------------------------------------
// Generated from :LordConfig.csv

using System.Collections.Generic;
using System.IO;
using System.Text;

public partial class LordConfig
{
    private int __Id;
    public int Id
    {
        internal set{ __Id = value; }
        get{ return __Id; }
    }
    private string __Name;
    public string Name
    {
        internal set{ __Name = value; }
        get{ return __Name; }
    }
    private AssetRef __Skin;
    public AssetRef Skin
    {
        internal set{ __Skin = value; }
        get{ return __Skin; }
    }
    private AssetRef __InGameSkin;
    public AssetRef InGameSkin
    {
        internal set{ __InGameSkin = value; }
        get{ return __InGameSkin; }
    }
    private AssetRef __InGamePrefab;
    public AssetRef InGamePrefab
    {
        internal set{ __InGamePrefab = value; }
        get{ return __InGamePrefab; }
    }
    private int __HidePlatformInGame;
    public int HidePlatformInGame
    {
        internal set{ __HidePlatformInGame = value; }
        get{ return __HidePlatformInGame; }
    }
    private AssetRef __InGamePlatform;
    public AssetRef InGamePlatform
    {
        internal set{ __InGamePlatform = value; }
        get{ return __InGamePlatform; }
    }
    private int __AttackAttribute;
    public int AttackAttribute
    {
        internal set{ __AttackAttribute = value; }
        get{ return __AttackAttribute; }
    }
    private int __Quality;
    public int Quality
    {
        internal set{ __Quality = value; }
        get{ return __Quality; }
    }
    private int __Energy;
    public int Energy
    {
        internal set{ __Energy = value; }
        get{ return __Energy; }
    }
    private string __Desc;
    public string Desc
    {
        internal set{ __Desc = value; }
        get{ return __Desc; }
    }
    private string __SourceDesc;
    public string SourceDesc
    {
        internal set{ __SourceDesc = value; }
        get{ return __SourceDesc; }
    }
    public List<int> Skills = new List<int>();
    public Dictionary<int,int> UnlockCost = new Dictionary<int,int>();
    private int __FragmentId;
    public int FragmentId
    {
        internal set{ __FragmentId = value; }
        get{ return __FragmentId; }
    }
    private AssetRef __FightBehavior;
    public AssetRef FightBehavior
    {
        internal set{ __FightBehavior = value; }
        get{ return __FightBehavior; }
    }
    public List<string> AiQueue = new List<string>();
}

public class LordConfigTable : FQDev.CSVConfig.ConfigBase
{
    static string __config_file_name = "LordConfig";
    public static string ConfigName => __config_file_name;
    static int __col_count = 17;

    private readonly Dictionary<int,LordConfig> __rows = new Dictionary<int,LordConfig>();
    public Dictionary<int,LordConfig> Rows => __rows;
    // key = LordConfig.Id
    public LordConfig GetRowData( int key )
    {
        LordConfig tmp; 
        if( !__rows.TryGetValue( key, out tmp ) )
            LogError("Can't get row data by key:{0} Config:{1}", key, __config_file_name);
        return tmp;
    }

    public int GetRowCount()
    {
        return __rows.Count;
    }

    public override void ParseConfigure( string text )
    {

        __rows.Clear();
        string splace_text = text.Replace("\r","");
        string[] lines = splace_text.Split(new string[]{"\n"}, System.StringSplitOptions.None);
        
        foreach( string line in lines )
        {
            if( line.Length == 0 )
                continue;
            if( line[0] == '\0' ||line[0] == '#' || line[0] == '%' || line[0] == '$')
                continue;


            string[] rowdata = line.Split(new string[]{","}, System.StringSplitOptions.None);
            if( rowdata.Length != __col_count )
            {
                LogError(string.Format("{0}列数与原配置不符", __config_file_name));
                return;
            }
            int counter = 0;
            LordConfig tmp = new LordConfig();

            string strId = rowdata[counter++];
            if (!string.IsNullOrEmpty(strId))
                tmp.Id = int.Parse(strId);
            tmp.Name = rowdata[counter++];
            tmp.Skin = AssetRef.Parse(rowdata[counter++]);
            tmp.InGameSkin = AssetRef.Parse(rowdata[counter++]);
            tmp.InGamePrefab = AssetRef.Parse(rowdata[counter++]);

            string strHidePlatformInGame = rowdata[counter++];
            if (!string.IsNullOrEmpty(strHidePlatformInGame))
                tmp.HidePlatformInGame = int.Parse(strHidePlatformInGame);
            tmp.InGamePlatform = AssetRef.Parse(rowdata[counter++]);

            string strAttackAttribute = rowdata[counter++];
            if (!string.IsNullOrEmpty(strAttackAttribute))
                tmp.AttackAttribute = int.Parse(strAttackAttribute);

            string strQuality = rowdata[counter++];
            if (!string.IsNullOrEmpty(strQuality))
                tmp.Quality = int.Parse(strQuality);

            string strEnergy = rowdata[counter++];
            if (!string.IsNullOrEmpty(strEnergy))
                tmp.Energy = int.Parse(strEnergy);
            tmp.Desc = rowdata[counter++];
            tmp.SourceDesc = rowdata[counter++];
            do{
                string[] llc = rowdata[counter++].Split(new string[]{"|"} ,System.StringSplitOptions.None);
                foreach( string vvv in llc )
                {
                    if(vvv.Length == 0 ) 
                        continue;
                    tmp.Skills.Add( int.Parse(vvv) );
                }
            }while(false);
            do{
                string[] v1 = rowdata[counter++].Split(new string[]{ "|"},System.StringSplitOptions.None);
                foreach( string layer1 in v1 )
                {
                    if( layer1.Length == 0 )
                        continue;
                    string[] v2 = layer1.Split(new string[]{":"},System.StringSplitOptions.None);
                    if( v2.Length != 2 )
                        continue;
                    tmp.UnlockCost.Add( int.Parse(v2[0]),int.Parse(v2[1]));
                }
            }while(false);

            string strFragmentId = rowdata[counter++];
            if (!string.IsNullOrEmpty(strFragmentId))
                tmp.FragmentId = int.Parse(strFragmentId);
            tmp.FightBehavior = AssetRef.Parse(rowdata[counter++]);
            do{
                string[] llc = rowdata[counter++].Split(new string[]{"|"} ,System.StringSplitOptions.None);
                foreach( string vvv in llc )
                {
                    if(vvv.Length == 0 ) 
                        continue;
                    tmp.AiQueue.Add( vvv );
                }
            }while(false);
            __rows.Add( tmp.Id, tmp );
        }
    }
}