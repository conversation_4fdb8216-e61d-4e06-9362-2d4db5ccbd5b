//-----------------------------------------
//<auto-generated>
//    This code was generated by a tool.
//    Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
//</auto-generated>
//------------------------------------------
// Generated from :TutorialsConfig.csv

using System.Collections.Generic;
using System.IO;
using System.Text;

public class TutorialsConfig
{
    private string __Id;
    public string Id
    {
        internal set{ __Id = value; }
        get{ return __Id; }
    }
    private AssetRef __Asset;
    public AssetRef Asset
    {
        internal set{ __Asset = value; }
        get{ return __Asset; }
    }
}

public class TutorialsConfigTable : FQDev.CSVConfig.ConfigBase
{
    static string __config_file_name = "TutorialsConfig";
    public static string ConfigName => __config_file_name;
    static int __col_count = 2;

    private readonly Dictionary<string,TutorialsConfig> __rows = new Dictionary<string,TutorialsConfig>();
    public Dictionary<string,TutorialsConfig> Rows => __rows;
    // key = TutorialsConfig.Id
    public TutorialsConfig GetRowData( string key )
    {
        TutorialsConfig tmp; 
        if( !__rows.TryGetValue( key, out tmp ) )
            LogError("Can't get row data by key:{0} Config:{1}", key, __config_file_name);
        return tmp;
    }

    public int GetRowCount()
    {
        return __rows.Count;
    }

    public override void ParseConfigure( string text )
    {

        __rows.Clear();
        string splace_text = text.Replace("\r","");
        string[] lines = splace_text.Split(new string[]{"\n"}, System.StringSplitOptions.None);
        
        foreach( string line in lines )
        {
            if( line.Length == 0 )
                continue;
            if( line[0] == '\0' ||line[0] == '#' || line[0] == '%' || line[0] == '$')
                continue;


            string[] rowdata = line.Split(new string[]{","}, System.StringSplitOptions.None);
            if( rowdata.Length != __col_count )
            {
                LogError(string.Format("{0}列数与原配置不符", __config_file_name));
                return;
            }
            int counter = 0;
            TutorialsConfig tmp = new TutorialsConfig();
            tmp.Id = rowdata[counter++];
            tmp.Asset = AssetRef.Parse(rowdata[counter++]);
            __rows.Add( tmp.Id, tmp );
        }
        PostProcess();
    }

    protected virtual void PostProcess() {}
}