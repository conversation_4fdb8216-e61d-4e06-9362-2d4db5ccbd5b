//-----------------------------------------
//<auto-generated>
//    This code was generated by a tool.
//    Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
//</auto-generated>
//------------------------------------------
// Generated from :CampConfig.csv

using System.Collections.Generic;
using System.IO;
using System.Text;

public partial class CampConfig
{
    private int __Id;
    public int Id
    {
        internal set{ __Id = value; }
        get{ return __Id; }
    }
    private string __Name;
    public string Name
    {
        internal set{ __Name = value; }
        get{ return __Name; }
    }
    private AssetRef __Icon;
    public AssetRef Icon
    {
        internal set{ __Icon = value; }
        get{ return __Icon; }
    }
    private string __Des;
    public string Des
    {
        internal set{ __Des = value; }
        get{ return __Des; }
    }
    private AssetRef __Picture;
    public AssetRef Picture
    {
        internal set{ __Picture = value; }
        get{ return __Picture; }
    }
    public Dictionary<int,int> Skills = new Dictionary<int,int>();
}

public class CampConfigTable : FQDev.CSVConfig.ConfigBase
{
    static string __config_file_name = "CampConfig";
    public static string ConfigName => __config_file_name;
    static int __col_count = 6;

    private readonly Dictionary<int,CampConfig> __rows = new Dictionary<int,CampConfig>();
    public Dictionary<int,CampConfig> Rows => __rows;
    // key = CampConfig.Id
    public CampConfig GetRowData( int key )
    {
        CampConfig tmp; 
        if( !__rows.TryGetValue( key, out tmp ) )
            LogError("Can't get row data by key:{0} Config:{1}", key, __config_file_name);
        return tmp;
    }

    public int GetRowCount()
    {
        return __rows.Count;
    }

    public override void ParseConfigure( string text )
    {

        __rows.Clear();
        string splace_text = text.Replace("\r","");
        string[] lines = splace_text.Split(new string[]{"\n"}, System.StringSplitOptions.None);
        
        foreach( string line in lines )
        {
            if( line.Length == 0 )
                continue;
            if( line[0] == '\0' ||line[0] == '#' || line[0] == '%' || line[0] == '$')
                continue;


            string[] rowdata = line.Split(new string[]{","}, System.StringSplitOptions.None);
            if( rowdata.Length != __col_count )
            {
                LogError(string.Format("{0}列数与原配置不符", __config_file_name));
                return;
            }
            int counter = 0;
            CampConfig tmp = new CampConfig();

            string strId = rowdata[counter++];
            if (!string.IsNullOrEmpty(strId))
                tmp.Id = int.Parse(strId);
            tmp.Name = rowdata[counter++];
            tmp.Icon = AssetRef.Parse(rowdata[counter++]);
            tmp.Des = rowdata[counter++];
            tmp.Picture = AssetRef.Parse(rowdata[counter++]);
            do{
                string[] v1 = rowdata[counter++].Split(new string[]{ "|"},System.StringSplitOptions.None);
                foreach( string layer1 in v1 )
                {
                    if( layer1.Length == 0 )
                        continue;
                    string[] v2 = layer1.Split(new string[]{":"},System.StringSplitOptions.None);
                    if( v2.Length != 2 )
                        continue;
                    tmp.Skills.Add( int.Parse(v2[0]),int.Parse(v2[1]));
                }
            }while(false);
            __rows.Add( tmp.Id, tmp );
        }
    }
}