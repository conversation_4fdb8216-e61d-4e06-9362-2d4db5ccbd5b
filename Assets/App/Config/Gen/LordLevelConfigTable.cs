//-----------------------------------------
//<auto-generated>
//    This code was generated by a tool.
//    Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
//</auto-generated>
//------------------------------------------
// Generated from :LordLevelConfig.csv

using System.Collections.Generic;
using System.IO;
using System.Text;

public partial class LordLevelConfig
{
    private int __Id;
    public int Id
    {
        internal set{ __Id = value; }
        get{ return __Id; }
    }
    private int __CommanderId;
    public int CommanderId
    {
        internal set{ __CommanderId = value; }
        get{ return __CommanderId; }
    }
    private int __Level;
    public int Level
    {
        internal set{ __Level = value; }
        get{ return __Level; }
    }
    private int __AttackValue;
    public int AttackValue
    {
        internal set{ __AttackValue = value; }
        get{ return __AttackValue; }
    }
    private int __HpValue;
    public int HpValue
    {
        internal set{ __HpValue = value; }
        get{ return __HpValue; }
    }
    public Dictionary<int,int> LevelUpCost = new Dictionary<int,int>();
    private int __BattlePower;
    public int BattlePower
    {
        internal set{ __BattlePower = value; }
        get{ return __BattlePower; }
    }
}

public class LordLevelConfigTable : FQDev.CSVConfig.ConfigBase
{
    static string __config_file_name = "LordLevelConfig";
    public static string ConfigName => __config_file_name;
    static int __col_count = 7;

    private readonly Dictionary<int,LordLevelConfig> __rows = new Dictionary<int,LordLevelConfig>();
    public Dictionary<int,LordLevelConfig> Rows => __rows;
    // key = LordLevelConfig.Id
    public LordLevelConfig GetRowData( int key )
    {
        LordLevelConfig tmp; 
        if( !__rows.TryGetValue( key, out tmp ) )
            LogError("Can't get row data by key:{0} Config:{1}", key, __config_file_name);
        return tmp;
    }

    public int GetRowCount()
    {
        return __rows.Count;
    }

    public override void ParseConfigure( string text )
    {

        __rows.Clear();
        string splace_text = text.Replace("\r","");
        string[] lines = splace_text.Split(new string[]{"\n"}, System.StringSplitOptions.None);
        
        foreach( string line in lines )
        {
            if( line.Length == 0 )
                continue;
            if( line[0] == '\0' ||line[0] == '#' || line[0] == '%' || line[0] == '$')
                continue;


            string[] rowdata = line.Split(new string[]{","}, System.StringSplitOptions.None);
            if( rowdata.Length != __col_count )
            {
                LogError(string.Format("{0}列数与原配置不符", __config_file_name));
                return;
            }
            int counter = 0;
            LordLevelConfig tmp = new LordLevelConfig();

            string strId = rowdata[counter++];
            if (!string.IsNullOrEmpty(strId))
                tmp.Id = int.Parse(strId);

            string strCommanderId = rowdata[counter++];
            if (!string.IsNullOrEmpty(strCommanderId))
                tmp.CommanderId = int.Parse(strCommanderId);

            string strLevel = rowdata[counter++];
            if (!string.IsNullOrEmpty(strLevel))
                tmp.Level = int.Parse(strLevel);

            string strAttackValue = rowdata[counter++];
            if (!string.IsNullOrEmpty(strAttackValue))
                tmp.AttackValue = int.Parse(strAttackValue);

            string strHpValue = rowdata[counter++];
            if (!string.IsNullOrEmpty(strHpValue))
                tmp.HpValue = int.Parse(strHpValue);
            do{
                string[] v1 = rowdata[counter++].Split(new string[]{ "|"},System.StringSplitOptions.None);
                foreach( string layer1 in v1 )
                {
                    if( layer1.Length == 0 )
                        continue;
                    string[] v2 = layer1.Split(new string[]{":"},System.StringSplitOptions.None);
                    if( v2.Length != 2 )
                        continue;
                    tmp.LevelUpCost.Add( int.Parse(v2[0]),int.Parse(v2[1]));
                }
            }while(false);

            string strBattlePower = rowdata[counter++];
            if (!string.IsNullOrEmpty(strBattlePower))
                tmp.BattlePower = int.Parse(strBattlePower);
            __rows.Add( tmp.Id, tmp );
        }
    }
}