//-----------------------------------------
//<auto-generated>
//    This code was generated by a tool.
//    Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
//</auto-generated>
//------------------------------------------
// Generated from :TreasureConst.csv

using System.Collections.Generic;
using System.IO;
using System.Text;

public class TreasureConst : FQDev.CSVConfig.ConfigBase
{
    /// <summary>
    /// 宝物誓约系统开启关卡（通过）
    /// </summary>
    public int TreasureOathUnlockChapter => __TreasureOathUnlockChapter;
    private int __TreasureOathUnlockChapter;

    /// <summary>
    /// 宝物星级上限
    /// </summary>
    public int TreasureStarLimit => __TreasureStarLimit;
    private int __TreasureStarLimit;

    /// <summary>
    /// 宝物升星限制
    /// </summary>
    public int TreasureStarUpLimit => __TreasureStarUpLimit;
    private int __TreasureStarUpLimit;

    /// <summary>
    /// 宝物系统开启关卡（通过）
    /// </summary>
    public int TreasureUnlockChapter => __TreasureUnlockChapter;
    private int __TreasureUnlockChapter;


    public override void ParseConfigure( string text )
    {
        var lines = text.Replace("\r", "")
            .Split(new[] {"\n"}, System.StringSplitOptions.None);

        var rawData = new Dictionary<string, string>();
        foreach (var line in lines)
        {
            if( line.Length == 0 )
                continue;
            if( line[0] == '\0' ||line[0] == '#' || line[0] == '%' || line[0] == '$')
                continue;

            var rows = line.Split(new[] {","}, System.StringSplitOptions.None);
            if (rows.Length < 3)
            {
                ErrorLogger?.Invoke(string.Format("CommonConst列数不符"));
                return;
            }
        
            rawData.Add(rows[0], rows[1]);
        }
        
        var raw = "";
        if (rawData.TryGetValue("TreasureOathUnlockChapter", out raw))
        {
            __TreasureOathUnlockChapter = int.Parse(raw);
        }
        if (rawData.TryGetValue("TreasureStarLimit", out raw))
        {
            __TreasureStarLimit = int.Parse(raw);
        }
        if (rawData.TryGetValue("TreasureStarUpLimit", out raw))
        {
            __TreasureStarUpLimit = int.Parse(raw);
        }
        if (rawData.TryGetValue("TreasureUnlockChapter", out raw))
        {
            __TreasureUnlockChapter = int.Parse(raw);
        }
    }
}