using System.Collections.Generic;

namespace App.Config.Ref
{
    public class HeroQualityRef
    {
        private readonly Dictionary<int, HeroQualityConfig> _qualityRef;

        public HeroQualityRef(HeroConfigTable heroConfigTable, HeroQualityConfigTable qualityConfigTable)
        {
            _qualityRef = new Dictionary<int, HeroQualityConfig>();
            foreach (var (heroId, heroConfig) in heroConfigTable.Rows)
            {
                var qualityConfig = qualityConfigTable.GetRowData(heroConfig.Quality);
                _qualityRef[heroId] = qualityConfig;
            }
        }

        public HeroQualityConfig this[int heroID] => _qualityRef[heroID];

    }
}