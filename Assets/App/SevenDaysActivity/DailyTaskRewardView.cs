using System;
using System.Collections.Generic;
using System.Linq;
using App.Config;
using DG.Tweening;
using FQDev.EventSystem;
using UnityEngine;

namespace GameModule.SevenDaysActivity
{
    public class DailyTaskRewardView : MonoBehaviour
    {
        [SerializeField] private SevenDaysLayer _controller;
        [SerializeField] private Transform _content;
        [SerializeField] protected DailyTaskRewardItem _dailyTaskCell;

        private Stack<DailyTaskRewardItem> _cache;
        private List<DailyTaskRewardItem> _items;
        private int _day;

        private EventSubscriber _eventSubscriber;

        private void Awake()
        {
            _eventSubscriber = new EventSubscriber();
            _eventSubscriber.Subscribe<SevenDaysTaskStateChangedEvent>(evt =>
            {
                var config = SevenDaysActivityManager.Instance.TaskConfigTable.GetRowData(evt.Id);
                if (config.Day == _day)
                {
                    foreach (var item in _items)
                        item.RefreshUI();
                    
                    SortItems();
                }
            });
        }

        private void OnDestroy()
        {
            _eventSubscriber.Dispose();
        }

        public void Initialize()
        {
            _cache ??= new Stack<DailyTaskRewardItem>();
            _items ??= new List<DailyTaskRewardItem>();
            _dailyTaskCell.gameObject.SetActive(false);
        }

        public void ConversionPage(int page)
        {
            foreach (var item in _items)
            {
                _cache.Push(item);
                item.gameObject.SetActive(false);
            }
            _items.Clear();

            _day = page + 1;
            var configs = ConfigManager.Instance.GetConfig<SevenDaysTaskConfigTable>().Rows
                .Where(a => a.Value.Day == _day);

            foreach (var kv in configs)
            {
                var item = GetOrCreateItem();
                _items.Add(item);
                item.Initialize(kv.Value);
            }
            SortItems();
            
            var index = 0;
            foreach (var item in _items)
            {
                item.transform.DOKill(true);
                item.transform.DOScaleX(0, 0.3f).From().SetDelay(index++ * 0.05f);
            }
        }

        private void SortItems()
        {
            _items.Sort((a, b) =>
            {
                if (a.ClaimState != b.ClaimState)
                    return ((int)b.ClaimState).CompareTo((int)a.ClaimState);
                return a.Config.Id.CompareTo(b.Config.Id);
            });

            for (var i = 0; i < _items.Count; ++i)
            {
                _items[i].transform.SetSiblingIndex(i);
            }
        }

        private DailyTaskRewardItem GetOrCreateItem()
        {
            DailyTaskRewardItem item;
            if (_cache.Count <= 0)
            {
                item = GameObject.Instantiate(
                    _dailyTaskCell, 
                    _content, 
                    false);
            }
            else
            {
                item = _cache.Pop();
            }
            item.gameObject.SetActive(true);
            return item;
        }
    }
}