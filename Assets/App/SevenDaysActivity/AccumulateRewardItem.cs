using System.Collections.Generic;
using App.Bag;
using App.Config;
using App.UI;
using App.UI.Hero;
using App.UI.Home;
using FQDev.EventSystem;
using FQDev.UI.Layer;
using GameModule.SevenDaysActivity;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Button = FQDev.UI.Button;

public class AccumulateRewardItem : MonoBehaviour
{
    [SerializeField] private Button _buttonClaim;
    [SerializeField] private Image _imageChest;
    [SerializeField] private GameObject _redDot;
    [SerializeField] private TextMeshProUGUI _needValue;
    [SerializeField] private GameObject _awardPoint;

    [SerializeField] private CanvasGroup _heroCanvasGroup;
    [SerializeField] private Image _heroBackground1;
    [SerializeField] private Image _heroBackground2;
    [SerializeField] private Image _heroIcon;
    
    public SevenDaysChestConfig Config { get; private set; }

    private void Awake()
    {
        _buttonClaim.AddClick(ClickClaimAward);
        _buttonClaim.AddUnClick(ClickShowAward);
        _heroCanvasGroup.gameObject.SetActive(false);
    }

    public void Initialize(SevenDaysChestConfig config)
    {
        Config = config;
        _needValue.text = config.Score.ToString();

        var item = config.Award.First();
        if (item is {Type: RewardType.Item, Id: > (int) Item.HeroCardStart, Id: < (int) Item.HeroCardEnd})
        {
            _imageChest.gameObject.SetActive(false);
            _heroCanvasGroup.gameObject.SetActive(true);

            var heroId = item.Id;
            _heroBackground1.sprite = ConfigManager.Instance.HeroQualityRef[heroId].Background.Load<Sprite>();
            _heroBackground2.sprite = ConfigManager.Instance.HeroQualityRef[heroId].IconBg.Load<Sprite>();
            _heroIcon.sprite = ConfigManager.Instance.HeroGradeRef.GetHeroSpriteByLevel(heroId, 2);
        }
        
        ResetUI();
    }

    public void ResetUI()
    {
        var state = SevenDaysActivityManager.Instance.GetChestState(Config.Id);
        _buttonClaim.interactable = state != SevenDaysState.NotClaimable;
        if (state == SevenDaysState.Claimed)
        {
            _buttonClaim.RemoveClick(ClickClaimAward);
            _imageChest.SetAlpha(0.5f);
            _heroCanvasGroup.alpha = 0.5f;
        }
        
        _redDot.SetActive(state == SevenDaysState.Claimable);
    }

    private void ClickClaimAward()
    {
        SevenDaysActivityManager.Instance.ClaimChestReward(
            Config.Id,
            data =>
            {
                ResetUI();
            });
    }

    private void ClickShowAward()
    {
        if (_heroBackground1.gameObject.activeSelf)
        {
            var item = Config.Award.First();
            CommonItemTipsUtil.ShowTips(item);
            
            return;
        }
        var detail = LayerManager.Instance.LoadContent(
            (int) LayerTag.Popup,
            "ui/home_levelpanel/ChapterBoxDetail");
        var chapterBoxDetail = detail.GetComponent<ChapterBoxDetail>();
        chapterBoxDetail.Initialize(Config.Award, true);
        chapterBoxDetail.Content.position = _awardPoint.transform.position;
    }
}