
using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using FQDev.DataCenter;
using FQDev.EventSystem;
using App.Hero;

// ReSharper disable once CheckNamespace
namespace UserData {
    [JsonObject(MemberSerialization.OptIn)]
    public class Skin : BaseSkin, IEventSender {
        public Skin() : base() { }
        public EventDispatcher Dispatcher => EventDispatcher.Global;

        public bool HeroSkinBought(int id)
        {
            int count = Bought.Count;
            for(int i = 0; i < count; ++i)
            {
                if (Bought[i] == id)
                    return true;
            }

            return false;
        }

        public void NotifyUpdateSkin()
        {
            this.DispatchEvent(Witness<HeroSkinUpdate>._, 0);
        }
    }
}
