local ConfigManager = require "config_manager"
local _eventSubscriber = (require "event_subscriber").New()
local EventCenter = require "event_center"
local GuildWarManager = require "guild_war_manager"
local GameLineupManager = require "game_lineup_manager"

---@field heroItem1 CS.LuaScript.LuaBehaviour
---@field heroItem2 CS.LuaScript.LuaBehaviour
---@field heroItem3 CS.LuaScript.LuaBehaviour
---@field heroItem4 CS.LuaScript.LuaBehaviour
---@field noInfo CS.UnityEngine.GameObject
---@field mapRoot CS.UnityEngine.RectTransform
---@field mapObject CS.UnityEngine.GameObject

---@field lordPlatImage CS.UnityEngine.UI.Image
---@field lordImage CS.UnityEngine.UI.Image
---@field lordLevel CS.TMPro.TextMeshProUGUI
---@field starRoot CS.UnityEngine.RectTransform
---@field skillNode CS.UnityEngine.RectTransform

local _skillItemList = {}
local _starItemList = {}
local _heroList
local _heroLevelList = {}
local _heroIDList = {}
local _supports
local _supportsLevelList = {}
local _lordId = 0
local _loadLevel = 0
local _loadStar = 0
local _boardDataStr
local _skillDataStr
local _guildActivityConst
local _csEventSubscriber
local _baseData = nil
local _playerData = nil
local _score
local _skinData

local _baseInfoHasLoad = false
local _skillInfoHasLoad = false

local function CloseUI()
    self:GetComponent(typeof(CS.LuaScript.LuaLayerContent)):Close()
end

local function SetActiveEx(tr, state)
    if tr then
        if state then
            tr.gameObject:SetActive(true)
            local pos = tr.transform.localPosition
            if pos.x < -100000 then
                pos.x = pos.x + 1000000;
                tr.transform.localPosition = pos;
            end
        else
            local pos = tr.transform.localPosition
            if pos.x > -100000 then
                pos.x = pos.x - 1000000;
                tr.transform.localPosition = pos;
            end
        end
    end
end

local function LoadStarCount(count)
    if count <= 0 then
        skillNode.gameObject:SetActive(false)
        return
    end

    skillNode.gameObject:SetActive(true)
    for i = 1, count do
        local go = nil
        if i > #_starItemList then
            go = CS.UnityEngine.GameObject.Instantiate(starItem, starRoot)
            table.insert(_starItemList, go)
        else
            go = _starItemList[i]
        end
    end

    for i = 1, #_starItemList do
        _starItemList[i].gameObject:SetActive(i <= count)
    end
end

local function LoadSkillList(id)
    local config = ConfigManager.Tables.LordConfig[id]
    local skillList = {}
    for i = 1, #config.skills do
        local skillConfig = ConfigManager.Tables.LordSkillConfig[config.skills[i]]
        if skillConfig.active_skill_slot and skillConfig.active_skill_slot > 0 then
            table.insert(skillList, config.skills[i])
        end
    end
    for i = 1, #skillList do
        local item = nil
        if i > #_skillItemList then
            local go = CS.LuaScript.LuaAssetOperator.InstantiatePrefab(
                    "ui/pvparena",
                    "LordRoleSkillItem2",
                    skillNode)
            item = go:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
            table.insert(_skillItemList, item)
        else
            item = _skillItemList[i]
        end
        item:CallFunctionTable("SetData",
                {
                    id = skillList[i],
                    level = _loadLevel,
                    star = _loadStar
                })
    end

    for i = 1, #_skillItemList do
        _skillItemList[i].gameObject:SetActive(i <= #skillList)
    end
end

local function LoadLordRoleInfo(
        lordID, 
        loadLevel,
        lordStar)
    local config = ConfigManager.Tables.LordConfig[lordID]
    lordPlatImage.sprite = config.in_game_platform:Load(typeof(CS.UnityEngine.Sprite))
    lordImage.sprite = config.in_game_skin:Load(typeof(CS.UnityEngine.Sprite))
    lordLevel.text = CS.System.String.Format(CS.LanguageManager.Instance:GetLanguage("hero_level_text", "hero_level_text"), loadLevel)
    LoadSkillList(lordID)
    LoadStarCount(lordStar)
end

local function LoadHeroFormation()
    CommonFormation:CallFunctionTable("Initialize",
        {
            getFormation = function() 
                return _heroLevelList
            end,
            getGuardianFormation = function() 
                return _supportsLevelList
            end,
            skinMap = _skinData,
            hidePower = true,
            cancelSetFormation = true,
            isSelf = _baseData.uid == CS.DB.User.Id
        })
end

local function LoadAvatar()
    local frame = math.max(1, _playerData.frame)
    local avatar = math.max(1, _playerData.avatar)
    local avatarConfig = ConfigManager.Tables.AvatarConfig[avatar]
    local frameConfig = ConfigManager.Tables.AvatarFrameConfig[frame]
    
    if avatarConfig then
        local spriteAtlas = CS.LuaScript.LuaAssetOperator.LoadAsset("ui/avataricon", "avataratlas", typeof(CS.UnityEngine.U2D.SpriteAtlas))
        Avatar.sprite = spriteAtlas:GetSprite(avatarConfig.icon)
    end

    if frameConfig then
        if frameConfig.anim then
            local go = CS.LuaScript.LuaAssetOperator.InstantiatePrefab(
                frameConfig.anim.AssetBundlePath,
                frameConfig.anim.AssetName,
                Frame.transform)

            local rect = go:GetComponent(typeof(CS.UnityEngine.RectTransform))
            rect.anchoredPosition = CS.UnityEngine.Vector2(0, 0)
            Frame:SetAlpha(0);
            go.transform:SetScale(0.45)
        else
            Frame.sprite = frameConfig.icon:Load(typeof(CS.UnityEngine.Sprite))
            Frame:SetAlpha(255);
        end
    end
end

local function LoadPlayerInfo()
    Name.text = _playerData.name
    LoadAvatar()
end

local function LoadInfo()
    LoadLordRoleInfo(_lordId, _loadLevel, _loadStar)
    LoadHeroFormation()
    LoadPlayerInfo()
    noInfo:SetActive(#_boardDataStr == 0)
    mapObject.gameObject:SetActive(#_boardDataStr > 0)
    
    if #_boardDataStr == 0 then
        return
    end

    local gameData = CS.App.GamePlay.GameData.Deserialize(_boardDataStr)
    if gameData == nil then
        return
    end
    
    for i = 0, gameData.BoardItems.Length - 1 do
        local go = CS.LuaScript.LuaAssetOperator.InstantiatePrefab(
            "ui/pvparena",
            "MapItem",
            mapRoot)
        local luaItem = go:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
        luaItem.ScriptEnv.SetData(
            i,
            gameData,
            _heroIDList)
    end
end

function OnClickShowBaseInfo()
    if _lordId <= 0 then
        return
    end
    SetActiveEx(FormationNode, true)
    SetActiveEx(SkillNode, false)
    if _baseInfoHasLoad then
        return
    end

    _baseInfoHasLoad = true
    LoadInfo()
end

function OnClickShowSkillInfo()
    if _lordId <= 0 then
        return
    end
    SetActiveEx(FormationNode, false)
    SetActiveEx(SkillNode, true)
    if _skillInfoHasLoad then
        return
    end

    _skillInfoHasLoad = true
    for i = 1, #_heroList  do
        local id = _heroList[i].id
        local level = _heroList[i].level
        local go = HeroDetailNode:GetChild(i - 1).gameObject
        local item = go:GetComponent(typeof(CS.App.GamePlay.UI.HeroSkillDetailsItem))
        local heroConfig = ConfigManager.Tables.HeroConfig[id]
        local skillList = {}
        for limit,skillID in pairs(heroConfig.skills) do
            if level >= limit then
                local skillGroup = CS.App.Config.ConfigManager.Instance.SkillRef:GetSkillGroup(skillID)
                if skillGroup then
                    for _,skillValueConfig in pairs(skillGroup.SkillValueConfigs) do
                        table.insert(skillList, skillValueConfig)
                    end
                end
            end
        end

        item:SetHeroSkill(id, skillList, i - 1, nil, true)
    end
    
end

local function LoadHeroSkinList(heroList)
    for i = 1, #heroList do
        local heroID = heroList[i]
        if heroID > 0 then
            local skinID = CS.App.Hero.SkinManager.Instance:GetHeroSkinID(heroID)
            if skinID <= 0 then
                skinID = CS.App.Hero.SkinManager.Instance:GetHeroDefaultSkinID(heroID)
            end
            _skinData[tostring(heroID)] = skinID
        end
    end
end

local function LoadEmpty()
    SetActiveEx(EmptyNode, true)
    SetActiveEx(InfoNode, false)
end

local function SetInfo(
        heroList,
        lordId,
        lordLv,
        lordStar,
        supports,
        boardDataStr,
        skillDataStr,
        skinData,
        playerData)

    _supports = supports
    _heroList = heroList
    _lordId = lordId
    _loadLevel = lordLv
    _loadStar = lordStar
    _boardDataStr = boardDataStr
    _skillDataStr = skillDataStr

    _playerData = playerData

    _heroIDList = {}
    _heroLevelList = {}

    _supportsLevelList = {}
    local supportIDList = {}
    for i = 1, #supports do
        local data = supports[i]
        _supportsLevelList[data.id] = data.level
        table.insert(supportIDList, data.id)
    end


    _skinData = skinData
    
    for i = 1, #heroList do
        local data = heroList[i]
        table.insert(_heroIDList, data.id)
        _heroLevelList[data.id] = data.level
    end
    SetActiveEx(EmptyNode, false)
    SetActiveEx(InfoNode, true)
    OnClickShowBaseInfo()
end

local function SetPlayerFormation(data)
    if not data then
        return
    end
    
    local playerData = 
    {
        name = data.name,
        avatar = data.avatar,
        frame = data.frame,
        level = data.lv,
    }
    
    SetInfo(
            data.heroes,
            data.lord,
            data.lordLv,
            data.lordStar,
            data.supports,
            data.boardItems,
            data.skills,
            data.skins,
            playerData
    )
end

function OnClickReLineup()
    GameLineupManager.StartLineup(CS.App.GamePlay.LevelMode.GuildWarLineUp, function(formation)
        GuildWarManager.ReLineupComplete(formation)
    end)
end

function Initialize(data, showReLineupBtn, showLocked, order)
    _baseData = data
    LoadEmpty()
    if _baseData == nil then
        return
    end
    SetPlayerFormation(_baseData)
    ReLineupBtn.gameObject:SetActive(showReLineupBtn)
    Locked.gameObject:SetActive(showLocked)
    OrderText.text = order
end

function Awake()
    _guildActivityConst = ConfigManager.Tables.GuildActivityConst
    _csEventSubscriber = CS.FQDev.EventSystem.EventSubscriber()
    _csEventSubscriber:Subscribe(typeof(CS.App.UI.Home.HomeNavigatorTurnEvent), CloseUI)
end

function OnDestroy()
    _csEventSubscriber:Dispose()
    _eventSubscriber:Dispose()
end
