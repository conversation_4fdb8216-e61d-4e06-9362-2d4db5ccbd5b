local ConfigManager = require "config_manager"

local QualityText =
{
    [1] = "Lord_Equip_Quality_2",
    [2] = "Lord_Equip_Quality_3",
    [3] = "Lord_Equip_Quality_4",
}

local SourceText =
{
    "Skill_Detail_Tips_5",
    "Skill_Detail_Tips_6",
}

local function RefreshDesRect()
    CS.UnityEngine.UI.LayoutRebuilder.MarkLayoutForRebuild(DesRect)
end

function SetData(skillValueID)
    local skillValueConfig = ConfigManager.Tables.SkillValueConfig[skillValueID]
    local skillID = skillValueConfig.skill_id
    SkillItem:FreshUI(CS.App.Config.ConfigManager.Instance:GetConfig(typeof(CS.SkillValueConfigTable)):GetRowData(skillValueID))
    local skillGroup = CS.App.Config.ConfigManager.Instance.SkillRef:GetSkillGroup(skillID)
    local heroID = skillGroup.HeroID

    if heroID > 0 then
        local heroConfig = ConfigManager.Tables.HeroConfig[heroID]
        for level,id in pairs(heroConfig.skills) do
            if id == skillID then
                UnlockLevel.text = level
                break
            end
        end
    end

    Quality.text = CS.LanguageManager.Instance:GetLanguage(
        QualityText[skillValueConfig.grade],
        QualityText[skillValueConfig.grade])

    Source.text = CS.LanguageManager.Instance:GetLanguage("Skill_Detail_Tips_3", "Skill_Detail_Tips_3")
    for i = 1, #SourceText do
        if i > 1 then
            Source.text = Source.text.."/"
        end
        Source.text = Source.text..CS.LanguageManager.Instance:GetLanguage(SourceText[i], SourceText[i])
    end

    local game = CS.App.GamePlay.GameManager.Instance.SelfGame;
    if game and game.SkillSet and heroID > -1 then
        local value = game.SkillSet:GetHeroFieldValue(heroID,
            skillGroup.SkillConfig.FieldId);
        Des:SetLang(skillGroup.SkillConfig.Name, value)
    else
        Des:SetLang(skillGroup.SkillConfig.Name, skillValueConfig.value_min)
    end

    local hasRelation = false
    for _,id in pairs(skillGroup.SkillConfig.RelationHeroes) do
        local go = CS.UnityEngine.GameObject.Instantiate(HeadItemPrefab, HeroHeadNode)
        go:SetActive(true)
        local item = go:GetComponent(typeof(CS.UnityEngine.UI.Image))
        item.sprite = CS.FQDev.AssetBundles.AssetBundleManager.Instance:LoadAsset(
            "res/heroexres/headicons", 
            "headicon_"..id, 
            typeof(CS.UnityEngine.Sprite))
        
        hasRelation = true
    end
    RelationHeroNode:SetActive(hasRelation)
    RefreshDesRect()
end

function SetCoreSkill(skillID)
    local skillConfig = ConfigManager.Tables.SkillCoreConfig[skillID]
    SkillItem:SetData(skillID)
    UnlockLevel.text = skillConfig.unlock_level
    Quality.text = CS.LanguageManager.Instance:GetLanguage(
        QualityText[skillConfig.quality],
        QualityText[skillConfig.quality])
    Des.text = CS.LanguageManager.Instance:GetLanguage(skillConfig.des, skillConfig.des)
    local hasRelation = false
    for _,id in pairs(skillConfig.relation_heroes) do
        local go = CS.UnityEngine.GameObject.Instantiate(HeadItemPrefab, HeroHeadNode)
        go:SetActive(true)
        local item = go:GetComponent(typeof(CS.UnityEngine.UI.Image))
        item.sprite = CS.FQDev.AssetBundles.AssetBundleManager.Instance:LoadAsset(
            "res/heroexres/headicons",
            "headicon_"..id,
            typeof(CS.UnityEngine.Sprite))

        hasRelation = true
    end
    RelationHeroNode:SetActive(hasRelation)
    RefreshDesRect()
end

function SetCampSkill(skillID)
    local skillConfig = ConfigManager.Tables.SkillCampConfig[skillID]
    SkillItem:SetData(skillID)
    UnlockLevel.text = skillConfig.unlock_level
    Quality.text = CS.LanguageManager.Instance:GetLanguage(
        QualityText[skillConfig.quality],
        QualityText[skillConfig.quality])
    Des.text = CS.LanguageManager.Instance:GetLanguage(skillConfig.des, skillConfig.des)
    local hasRelation = false
    local camp = skillConfig.camp
    for id, config in pairs(ConfigManager.Tables.HeroConfig) do
        if config.lock_invisible == 0 and config.hero_camp.Contains(camp) then
            local go = CS.UnityEngine.GameObject.Instantiate(HeadItemPrefab, HeroHeadNode)
            go:SetActive(true)
            local item = go:GetComponent(typeof(CS.UnityEngine.UI.Image))
            item.sprite = CS.FQDev.AssetBundles.AssetBundleManager.Instance:LoadAsset(
                "res/heroexres/headicons",
                "headicon_"..id,
                typeof(CS.UnityEngine.Sprite))

            hasRelation = true
        end
    end
    RelationHeroNode:SetActive(hasRelation)
    
    local lordRelation = false
    for id, config in pairs(ConfigManager.Tables.LordConfig) do
        if config.lord_camp.Contains(camp) then
            local go = CS.UnityEngine.GameObject.Instantiate(HeadItemPrefab, LordHeadNode)
            go:SetActive(true)
            local item = go:GetComponent(typeof(CS.UnityEngine.UI.Image))
            item.sprite = CS.FQDev.AssetBundles.AssetBundleManager.Instance:LoadAsset(
                "res/lordexres/headicons",
                "lordicon_"..id,
                typeof(CS.UnityEngine.Sprite))

            lordRelation = true
        end
    end
    LordHeadNode:SetActive(lordRelation)
    
    RefreshDesRect()
end

function Awake()

end


