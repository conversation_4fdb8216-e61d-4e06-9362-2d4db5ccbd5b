local _eventSubscriber = (require "event_subscriber").New()
local EventCenter = require "event_center"
local ConfigManager = require "config_manager"
local TableUtil = require "table_util"
local TacticalOrderManager = require "tactical_order_manager"
local DOTween = CS.DG.Tweening.DOTween

local _game = nil
local _progress = 0
local _config = nil
local _skillItemList = {}
local _sequence = nil

local function SetActiveEx(tr, state)
    if tr then
        if state then
            local pos = tr.transform.localPosition
            if pos.x < -100000 then
                pos.x = pos.x + 1000000;
                tr.transform.localPosition = pos;
            end
        else
            local pos = tr.transform.localPosition
            if pos.x > -100000 then
                pos.x = pos.x - 1000000;
                tr.transform.localPosition = pos;
            end
        end
    end
end

local function PlayAnim()
    TitleNode:SetActive(true)
    SetActiveEx(InfoNode, false)
    if _sequence then
        _sequence:Kill() 
    end

    _sequence = DOTween.Sequence()
    _sequence:AppendInterval(2)
    _sequence:AppendCallback(function()
        TitleNode:SetActive(false)
        SetActiveEx(InfoNode, true)
    end)
end

local function LoadSkillInfo()
    for i = 1, #_config.tactical_order_cumul_time do
        local item = nil
        if i > #_skillItemList then
            local go = CS.LuaScript.LuaAssetOperator.InstantiatePrefab(
                    "ui/gameplay",
                    "OrderSkillItem",
                    SkillNode)
            item = go:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
            table.insert(_skillItemList, item)
        else
            item = _skillItemList[i]
        end
        local skillIdString = TacticalOrderManager.GetSkillIdByNameAndIndex(_config.id, i)
        item:CallFunctionTable("SetData", {game = _game, index = i, progress = _progress, condition = _config.tactical_order_cumul_time[i], id = tonumber(skillIdString), orderConfig = _config})
    end

    for i = 1, #_skillItemList do
        _skillItemList[i].gameObject:SetActive(i <= #_config.tactical_order_cumul_time)
    end
end

function Initialize(game, progress, id)
    _game = game
    _progress = progress
    _config = ConfigManager.Tables.TacticalOrderConfig[id]

    LoadSkillInfo()
    PlayAnim()
end


function Awake()
    
end

function OnDestroy()
    if _sequence then
        _sequence:Kill() 
    end
end