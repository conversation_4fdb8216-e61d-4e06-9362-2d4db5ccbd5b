local ConfigManager = require "config_manager"
local ActivityManager = require "explore_activity_manager"
local TableUtil = require "table_util"

local _itemData = nil
local _activityId
local _config = nil
local rewardItems = {}

local function LoadTaskInfo()
    local targetValue = ActivityManager.GetTaskDataByTypeID(_activityId, _config.type)
    local finish = false
    if _config.taget_param then
        finish = targetValue >= _config.target_count
        if finish then
            TaskProgress.value = 1
            ProgressText.text = "1/1"
        else
            TaskProgress.value = 0
            ProgressText.text = "0/1"
        end
    else
        TaskProgress.value = targetValue / _config.target_count
        ProgressText.text = targetValue.."/".._config.target_count
        finish = targetValue >= _config.target_count
    end

    local hasGet = ActivityManager.CheckTaskFinish(_activityId, _config.id)

    if hasGet then
        GetButton.gameObject:SetActive(false)
        TaskProgress.gameObject:SetActive(false)
    else
        if finish then
            GetButton.gameObject:SetActive(true)
            TaskProgress.gameObject:SetActive(false)
        else
            GetButton.gameObject:SetActive(false)
            TaskProgress.gameObject:SetActive(true)
        end
    end
    Received:SetActive(hasGet)
end

function SetData(data)
    _itemData = data
    _config = data.config
    _activityId = data.activityId
    local description = CS.LanguageManager.Instance:GetLanguage(_config.desc, _config.desc)
    if _config.taget_param then
        description = string.format(description, CS.IAPManager.Instance:GetLocalizedPriceByInappId(_config.taget_param))
    else
        description = string.format(description, _config.target_count)
    end
    TaskDes.text = description

    for _, v in pairs(rewardItems) do
        v.gameObject:SetActive(false)
    end

    for i, v in pairs(_config.rewards) do
        if rewardItems[i] == nil then
            local go  = CS.UnityEngine.GameObject.Instantiate(RewardItem, Content, false)
            local luaScript = go:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
            rewardItems[i] = luaScript
        end
        rewardItems[i].gameObject:SetActive(true)
        rewardItems[i].ScriptEnv.SetReward(v)
    end

    LoadTaskInfo()
end

local function SendClickGetTaskReward()
    CS.LogEvent.I:Track("Activity_Exchange_Task_Click")
      :IsNew()
      :AddMaxChapter()
      :Add("ActivityExchangeTaskID", _config.id)
      :Add("ActivityId", _activityId)
      :Send()
end

local function OnClickGetReward()
    ActivityManager.ClaimTaskReward(_activityId, _config.id)
    SendClickGetTaskReward()
end

function Awake()
    GetButton:AddClick(OnClickGetReward)
end