local TableUtil = require "table_util"
local EventCenter = require "event_center"
local ElementTrialManager = require "element_trial_manager"
local ConfigManager = require "config_manager"

local _gridView

local _itemCount = 0

local _currentTowerId
local _flag = true

local _pageCount

local _moveToIndex
local _lastIndex
function GetItemPrefab()
    return ItemPrefab
end

function GetItemCount()
    return _itemCount
end

function OnItemShown(item, index)
  
    local param = {}
    param.tower = _currentTowerId
    param.page = (_pageCount + 1) - index
    param.totalPage = _pageCount

    local listItem = item:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    listItem:CallFunctionTable("Initialize", param)

    if _flag then
        _gridView:MoveToItem(_moveToIndex)
        _flag = false

        Trans.anchoredPosition =  Trans.anchoredPosition + CS.UnityEngine.Vector2(0 , 500)
    end

    if _lastIndex ~= nil then
        if index < _lastIndex  then
            item.transform:SetAsFirstSibling()
        elseif  index > _lastIndex then
            item.transform:SetAsLastSibling()
        end
    end
  
    _lastIndex = index
    end


function Initialize(towerId)
    _currentTowerId = towerId
    --首先得到页数
     _pageCount = ElementTrialManager.GetPageCount(towerId)
    _itemCount = _pageCount
    
    _gridView:StopMovement()
    _gridView:ReloadContent()

   
end

function MoveToItem(index)
    _moveToIndex = index
    _flag= true
    _gridView:ReloadContent()
end
function Awake()
    _gridView = self:GetComponent(typeof(CS.App.UI.Utility.UIGridView))
end

function OnDestroy()
  
end