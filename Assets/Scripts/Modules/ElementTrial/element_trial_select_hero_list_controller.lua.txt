local ConfigManager = require "config_manager"
local CSConfigManager = CS.App.Config.ConfigManager
local TableUtil = require "table_util"

local _init = false
local _allHeroItems = {} -- heroId
local _gridView
local _heroFilterContent
local _layer
local _onNormalFormation

local _config

function GetItemPrefab()
    return HeroItemPrefab
end

function GetItemCount()
    return #_allHeroItems + 1 -- 多一个空item
end

function OnItemShown(item, index)
    item:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
        .ScriptEnv
        .RefreshUI(index == 1 and 0 or _allHeroItems[index - 1], _onNormalFormation, _layer)
end

local function CheckAttackAttribute(Id)
    local config = ConfigManager.Tables.HeroConfig[Id]
    local limit = _config.element_limit
    for _,v in pairs(limit) do
        if config.attack_attribute == v then
            return true
        end
    end
    return false
end

local function SortHeroes(idA, idB)
    local configA = ConfigManager.Tables.HeroConfig[idA]
    local configB = ConfigManager.Tables.HeroConfig[idB]

    -- 1. 等级高的优先
    local foundA, levelA = CS.DB.Hero.Contents:TryGetValue(idA)
    levelA = foundA and levelA or 0

    local foundB, levelB = CS.DB.Hero.Contents:TryGetValue(idB)
    levelB = foundB and levelB or 0

    if levelA ~= levelB then
        return levelA > levelB
    end

    -- 2. Quality高的优先
    if configA.quality ~= configB.quality then
        return configA.quality > configB.quality
    end

    -- 3. id小的优先
    return idA < idB
end

-- 函数：计算交集
local function HasIntersection(t1, t2)
    if not t1 or not t2 then
        return false
    end

    local set = {}
    for _, v in ipairs(t1) do
        set[v] = true
    end

    for _, v in ipairs(t2) do
        if set[v] then
            return true
        end
    end

    return false
end

local function CheckFilter(id)
    if _heroFilterContent and _heroFilterContent.HeroFilter then

        local _, qualities = _heroFilterContent.HeroFilter:TryGetValue(CS.App.UI.Hero.HeroFilterType.Quality)
        local _, attributes = _heroFilterContent.HeroFilter:TryGetValue(CS.App.UI.Hero.HeroFilterType.Attribute)
        local _, camps = _heroFilterContent.HeroFilter:TryGetValue(CS.App.UI.Hero.HeroFilterType.Camp)

        local config = ConfigManager.Tables.HeroConfig[id]

        local qualityMatch = qualities == nil or qualities.Count == 0 or TableUtil.Contains(qualities, config.quality)
        local attributeMatch = attributes == nil or attributes.Count == 0 or TableUtil.Contains(attributes, config.attack_attribute)
        local campMatch = camps == nil or camps.Count == 0 or HasIntersection(camps, config.hero_camp)

        return qualityMatch and attributeMatch and campMatch
    else
        return true
    end
end

local function SetFilterContent()
    local heroDemonKingRef = CSConfigManager.Instance.HeroDemonKingRef
    _heroFilterContent:ClearResourceHeroes()
    local dbHero = CS.DB.Hero.Contents:Get()
    for k, v in pairs(dbHero) do
        -- 只有当所有条件都满足时，才执行添加操作
        if v > 0 and CheckAttackAttribute(k) and not heroDemonKingRef:HeroIsTransfered(k) then
            local isDemonKing = heroDemonKingRef:IsDemonKingHero(k)
            if not isDemonKing then
                -- 不是魔王英雄，直接添加
                _heroFilterContent:AddResourceHero(k, v)
            else
                -- 是魔王英雄，需要检查其原始英雄是否已转生
                local originHeroID = heroDemonKingRef:GetOriginHeroIDByDemonKingHeroID(k)
                if heroDemonKingRef:HeroIsTransfered(originHeroID) then
                    _heroFilterContent:AddResourceHero(k, v)
                end
            end
        end
    end
end

local function SetItems()
    _allHeroItems = {}

    for k, v in pairs(_heroFilterContent.ResourceHeroes) do
        -- 只有当所有条件都满足时，才添加英雄
        if CheckFilter(k) then
            table.insert(_allHeroItems, k)
        end
    end
end

local function RefreshItems(refreshData)
    if refreshData then
        SetFilterContent()
        SetItems()
    end

    if _heroFilterContent and _heroFilterContent.HeroSorting then
        local heroTable = CS.App.Config.ConfigManager.Instance:GetConfig(typeof(CS.HeroConfigTable))
        table.sort(_allHeroItems, function(a, b)
            local config_a = heroTable:GetRowData(a)
            local config_b = heroTable:GetRowData(b)
            return _heroFilterContent.HeroSorting(config_a, config_b) < 0
        end)
    else
        table.sort(_allHeroItems, SortHeroes)
    end

    _gridView:ReloadContent()
end

local function LoadList(layer, onNormalFormation)
    _layer = layer
    _config = layer.ScriptEnv.GetConfig()
    _onNormalFormation = onNormalFormation
    RefreshItems(true)
end

function Refresh(layer ,onNormalFormation)
    if not _init then
        LoadList(layer, onNormalFormation)
        _init = true
        return
    end

    for i = 0, _gridView.VisibleItems.Length - 1 do
        if _gridView.VisibleItems[i] ~= nil then
            local item = _gridView.VisibleItems[i]:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
            item.ScriptEnv.SetSelected()
        end
    end
end

function Awake()
    _gridView = self:GetComponent(typeof(CS.App.UI.Utility.UIGridView))
    _heroFilterContent = self:GetComponent(typeof(CS.App.UI.Hero.HeroFilterContentLua))
    _heroFilterContent.OnHeroSortingChanged = function()
        RefreshItems(false)
    end

    _heroFilterContent.OnHeroFilterChanged = function()
        RefreshItems(true)
    end
end