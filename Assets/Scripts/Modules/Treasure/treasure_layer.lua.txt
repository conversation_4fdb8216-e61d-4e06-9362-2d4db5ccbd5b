local TreasureEventLog = require "treasure_event_log"

function OnEnable()
    TreasureEventLog.Treasure_Open()
end

function OpenHelperTip()
    local lay = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
            CS.App.UI.LayerTag.Popup,
            "ui/message/CommonWindowTipsLayer")

    local luaScript = lay:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    luaScript:CallFunctionTable("ShowTips",
            {
                content = CS.LanguageManager.Instance:GetLanguage("Treasure_Help_Des", "Treasure_Help_Des"),
                title = CS.LanguageManager.Instance:GetLanguage("Treasure_Help_Title", "Treasure_Help_Title")
            })
end

function Show()
    Layer.ScriptEnv.SetListContent(nil)
end