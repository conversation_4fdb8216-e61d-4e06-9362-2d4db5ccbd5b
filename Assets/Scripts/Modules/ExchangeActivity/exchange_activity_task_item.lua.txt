local ConfigManager = require "config_manager"
local ExchangeActivityManager = require "exchange_activity_manager"
local TableUtil = require "table_util"
local TaskManager = require "task_manager"

local _itemData = nil
local _config = nil
local _itemList = {}

local function LoadTaskInfo()
    local taskData = ExchangeActivityManager.GetTaskDataByTypeID(_config.type)
    local targetValue = taskData or 0
    local finish = false
    if _config.taget_param then
        finish = targetValue >= _config.target_count
        if finish then 
            TaskProgress.value = 1
            ProgressText.text = "1/1"
        else
            TaskProgress.value = 0
            ProgressText.text = "0/1"
        end
    else
        TaskProgress.value = targetValue / _config.target_count
        ProgressText.text = targetValue.."/".._config.target_count
        finish = targetValue >= _config.target_count
    end

    local activityDay = ExchangeActivityManager.GetCurrentDay()
    local dayLocked = activityDay < _config.page 
    local hasGet = ExchangeActivityManager.TaskRewardHasGet(_config.id)

    if hasGet then
        GetButton.gameObject:SetActive(false)
    else
        GetButton.gameObject:SetActive(true)
        if finish and not dayLocked then
            ButtonGrayImage.Amount = 0
            GetButton.interactable = true
        else
            ButtonGrayImage.Amount = 1
            GetButton.interactable = false
        end
    end
    Received:SetActive(hasGet)
end


function SetData(data)
    _itemData = data
    _config = data.config
    local description = CS.LanguageManager.Instance:GetLanguage(_config.desc, _config.desc)
    if _config.taget_param then
        description = string.format(description, CS.IAPManager.Instance:GetLocalizedPriceByInappId(_config.taget_param))
    else
        description = string.format(description, _config.target_count)
    end
    TaskDes.text = description

    for i = 1, #_itemList do
        if i <= #_config.rewards then
            _itemList[i].ScriptEnv.SetReward(_config.rewards[i])
        end

        _itemList[i].gameObject:SetActive(i <= #_config.rewards)
    end

    LoadTaskInfo()
end

local function SendClickGetTaskReward()

    CS.LogEvent.I:Track("Activity_Exchange_Task_Click")
            :IsNew()
            :AddMaxChapter()
            :Add("ActivityExchangeTaskID", _config.id)
            :Add("ActivityId", ExchangeActivityManager.GetExchangeActivityID())
            :Send()
end

local function OnClickGetReward()
    ExchangeActivityManager.ClaimTaskReward(_config.id)
    SendClickGetTaskReward()
end

function Awake()
    table.insert(_itemList, RewardItem)
    table.insert(_itemList, RewardItem2)
    GetButton:AddClick(OnClickGetReward)
end