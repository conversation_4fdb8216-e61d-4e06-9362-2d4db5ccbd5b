local ConfigManager = require "config_manager"
local ExchangeActivityManager = require "exchange_activity_manager"

local function Close()
    local layer = self.gameObject:GetComponent(typeof(CS.FQDev.UI.Layer.LayerContent))
    layer:Close()
end

function Awake()

end

function ClickJoin()
    local go = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
        CS.App.UI.LayerTag.Dialog,
        "ui/exchangeactivity/ExchangeActivityLayer")

    local luaScrip = go:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    if ExchangeActivityManager.OnExchangeDay() then
        luaScrip:CallFunctionInt("Initialize", 2)
    else
        luaScrip:CallFunctionInt("Initialize", 1)
    end
    Close()
end