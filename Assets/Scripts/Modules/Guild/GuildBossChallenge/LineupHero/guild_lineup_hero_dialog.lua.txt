local EventCenter = require "event_center"
local ConfigManager = require "config_manager"
local GameLineupManager = require "game_lineup_manager"
local TableUtil = require "table_util"

local _csEventSubscriber
local _isHeroInfo = true

local _lineupController
local MaxHeroCount = 4

local function CloseUI()
    self:GetComponent(typeof(CS.LuaScript.LuaLayerContent)):Close()
end

local function SetActiveEx(tr, state)
    if tr then
        if state then
            local pos = tr.transform.localPosition
            if pos.x < -100000 then
                pos.x = pos.x + 1000000;
                tr.transform.localPosition = pos;
            end
        else
            local pos = tr.transform.localPosition
            if pos.x > -100000 then
                pos.x = pos.x - 1000000;
                tr.transform.localPosition = pos;
            end
        end
    end
end

local _cd = 3
local _time = 0


local function OnClickLineup()
    if not ScrollList.ScriptEnv.IsSelectComplete() then
        CS.App.UI.MessageTip.Create("Four_Hero")
        return
    end
    ScrollList.ScriptEnv.SetFormation()
    if CS.FQDev.Time.TimeManager.ServerTimeUTC - _time < _cd then
        return
    end
    _time = CS.FQDev.Time.TimeManager.ServerTimeUTC
    local lordId = _lineupController.LineupData:GetLineupLordId()
    if lordId == 0 or lordId == nil then
        CS.App.UI.MessageTip.Create("Sever_Error_3")
        return
    end
    local heroes = _lineupController.LineupData:GetLineupHeroes() or {}
    if  #heroes ~= MaxHeroCount then
        CS.App.UI.MessageTip.Create("Four_Hero")
        return
    end
    _lineupController:EnterLineupLevel()

end

local function OnClickBack()
    _lineupController.LineupData:BackLineupHeroes()
    _lineupController.LineupData:BackGuardianHeroes()
    CloseUI()
    local path = "ui/guild/GuildLordChoseLayer"
    local go = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
            CS.App.UI.LayerTag.Dialog,
            path)

    local luaScript = go:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    --luaScript:CallFunctionTable("Initialize", {mode = _mode})
    luaScript.ScriptEnv.SetData(_lineupController)
end

local function OpenLineupDetailsDialog()
    local path = "ui/guild/GuildLineupDetailsDialog"
    local go = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
            CS.App.UI.LayerTag.Dialog,
            path)
    local luaScript = go:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    luaScript.ScriptEnv.SetData(_lineupController)

end

local function OnClickExit()
    CS.App.UI.MessageLayer.ShowMessage(
            "PVPArena_Arrange_Process_9",
            "Tip_Title",
            true,
            function(sure)
                if sure then
                    _lineupController.LineupData:BackLineupHeroes()
                    _lineupController.LineupData:BackGuardianHeroes()
                    CS.FQDev.UI.Layer.LayerManager.Instance:GetLayerController(CS.App.UI.LayerTag.Dialog):CloseAll()
                    OpenLineupDetailsDialog()
                end
            end)
end

local function OnClickSwitch()
    _isHeroInfo = not _isHeroInfo
    if _isHeroInfo then
        TitleText.text = "HeroChose"
        HeroInfoBg.sprite = CS.LuaScript.LuaAssetOperator.LoadAsset("ui/hero", "HomeMainLineupBox", typeof(CS.UnityEngine.Sprite))
    else
        TitleText.text = "HeroGuardianChose"
        HeroInfoBg.sprite = CS.LuaScript.LuaAssetOperator.LoadAsset("ui/hero", "HeroListBg", typeof(CS.UnityEngine.Sprite))
    end
    SetActiveEx(BattleHeroNode, _isHeroInfo)
    SetActiveEx(GuardianHeroNode, not _isHeroInfo)
    --BattleHeroesRoot.gameObject:SetActive(_isHeroInfo)
    --GuardianHeroNodeList.gameObject:SetActive(not _isHeroInfo)
    if _isHeroInfo then
        ScrollList.ScriptEnv.RefreshContent()
    else
        GuardianScrollList.ScriptEnv.RefreshContent()
    end
end

local function LoadHeroList()
    _lineupController.LineupData:SaveLastLineupHeroes()
    _lineupController.LineupData:SaveLastGuardianHeroes()

    ScrollList:CallFunctionTable("SetData",  {
        getFormation = function()
            return _lineupController.LineupData:GetLineupHeroes()
        end,
        setFormation = function(formation)

        end,
        eventID = EventCenter.ID.Guild_Lineup_SelectHero,
    })
    local showGuardian = CS.DB.User.SafeMaxChapter > ConfigManager.Tables.ConstConfig.SupportSkillsUnlockChapter
    GuardianScrollList:CallFunctionTable("SetData",  {
        showGuardian = showGuardian,
        getFormation = function()
            return _lineupController.LineupData:GetLineupGuardianHeroes()
        end,
        setFormation = function(formation)

        end,
        eventID = EventCenter.ID.Guild_Lineup_SelectGuardian,
    })

    SwitchBtn.gameObject:SetActive(showGuardian)
    SetActiveEx(GuardianHeroNode, not _isHeroInfo)
end

function SetData(lineupController)
    _lineupController = lineupController
    LoadHeroList()
end

function Awake()
    NextBtn:AddClick(OnClickLineup)
    PreviousBtn:AddClick(OnClickBack)
    ExitBtn:AddClick(OnClickExit)
    SwitchBtn:AddClick(OnClickSwitch)

    _csEventSubscriber = CS.FQDev.EventSystem.EventSubscriber()
    _csEventSubscriber:Subscribe(typeof(CS.App.UI.Home.HomeNavigatorTurnEvent), CloseUI)
end


function OnDestroy()
    _csEventSubscriber:Dispose()
end