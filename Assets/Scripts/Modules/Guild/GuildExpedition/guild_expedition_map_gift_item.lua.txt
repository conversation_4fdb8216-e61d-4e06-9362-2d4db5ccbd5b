local ConfigManager = require "config_manager"
local GuildExpeditionManager = require "guild_expedition_manager"

local _pointText
local _spine
local _data
local _isBossBox

local function UpdateContent()
    if _isBossBox then
        _pointText.text = "Boss"
       local bossNodeState = GuildExpeditionManager.GetBoosNodeState(_data.teamId)
        _spine.AnimationState:SetAnimation(0, "close", true)
        --if bossNodeState.occupied then
        --    _spine.AnimationState:SetAnimation(0, "refresh", true)
        --else
        --    _spine.AnimationState:SetAnimation(0, "close", true) 
        --end
    else
        local totalPoint = GuildExpeditionManager.GetTeamTotalPoint(_data.teamId)
        _spine.AnimationState:SetAnimation(0, "close", true)
        --if totalPoint < _data.point then
        --    _spine.AnimationState:SetAnimation(0, "close", true)
        --else
        --    _spine.AnimationState:SetAnimation(0, "refresh", true)
        --end
        _pointText.text = CS.App.UI.StringUtils.FormatCurrency(_data.point) 
    end
end

function Awake()
    _pointText = PointText
    _spine = Spine
end



function SetData(data, isBossBox)
    _data = data
    _isBossBox = isBossBox
    UpdateContent()
end

function OnSelected()
    local boxConfig = ConfigManager.Tables.GuildAwardBoxConfig[_data.boxId]

    local lay = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
            CS.App.UI.LayerTag.Popup,
            "ui/guild/GuildCustomTipsPopup")
    local luaScript = lay:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    
    local boxName = CS.LanguageManager.Instance:GetLanguage(boxConfig.name, boxConfig.name) 
    local boxDesc = string.format(CS.LanguageManager.Instance:GetLanguage(boxConfig.desc, boxConfig.desc), boxConfig.param)
    luaScript.ScriptEnv.SetData(boxName, boxDesc, boxConfig.icon, boxConfig.show_award)
end