local ConfigManager = require "config_manager"
local _eventSubscriber = (require "event_subscriber").New()
local EventCenter = require "event_center"
local PVPBattleManager = require "pvp_battle_manager"
local TableUtil = require "table_util"
local Enumerable = CS.System.Linq.Enumerable
local StringUtils = CS.App.UI.StringUtils

local _csEventSubscriber
local _settleData = nil

local function CloseUI()
    self:GetComponent(typeof(CS.LuaScript.LuaLayerContent)):Close()
end

local function LoadTitle()
    local victory = _settleData.victory
    if victory then
        TitleBg.sprite = CS.LuaScript.LuaAssetOperator.LoadAsset("ui/gameplay_pvp", "VictoryTitle", typeof(CS.UnityEngine.Sprite))
        Title.text = CS.LanguageManager.Instance:GetLanguage("GameVictoryLayer_title", "GameVictoryLayer_title")
    else
        TitleBg.sprite = CS.LuaScript.LuaAssetOperator.LoadAsset("ui/gameplay_pvp", "FailTitle", typeof(CS.UnityEngine.Sprite))
        Title.text = CS.LanguageManager.Instance:GetLanguage("GameFailureLayer_title", "GameFailureLayer_title")    
    end
end

local function LoadHeroDamage()
    local totalHit = _settleData.game.TotalDamage
    TotalDamage.transform:DOKill()
    local tempHit = 0;
    CS.DG.Tweening.DOTween.To(function() return tempHit end
    , function(value) tempHit = value end, totalHit, 0.5):OnUpdate(function()
        TotalDamage.text = StringUtils.FormatCurrency(tempHit, 2)
    end):SetEase(CS.DG.Tweening.Ease.Linear):SetTarget(TotalDamage.transform)

    local heroHitStatistics = {}
    for k,v in pairs(_settleData.game.HeroHitStatistics) do
        heroHitStatistics[k] = v
    end
    table.sort(heroHitStatistics, function(a, b)
        return a > b
    end)
    local index = 0
    for heroId, hit in pairs(heroHitStatistics) do
        local found, level = _settleData.game.AppearHeroes:TryGetValue(heroId)
        if found then
            local tr = HeroNode:GetChild(index)
            local item = tr.gameObject:GetComponent(typeof(CS.App.GamePlay.UI.ResultHeroItem))
            index = index + 1
            item:Init(heroId, level, totalHit, hit, _settleData.game);
        end
    end
end

local function LoadInfo()
    PlayerItem:CallFunctionTable("SetData", {avatar = _settleData.avatar, frame = _settleData.frame, name = _settleData.name})
    Day.text = _settleData.game.Day
    LoadHeroDamage()
    LoadTitle()
end

function SetData(data)
    _settleData = data
    LoadInfo()
end

function Awake()
    _csEventSubscriber = CS.FQDev.EventSystem.EventSubscriber()
    -- _eventSubscriber:Subscribe(EventCenter.ID.PVPBattle_Task_DataUpdated, OnRefresh)
    -- _csEventSubscriber:Subscribe(typeof(CS.App.Bag.ItemUpdatedEvent), OnItemUpdate)
end

function Start()

end

function OnEnable()

end

function OnDestroy()
    TotalDamage.transform:DOKill()
    _eventSubscriber:Dispose()
    _csEventSubscriber:Dispose()
end