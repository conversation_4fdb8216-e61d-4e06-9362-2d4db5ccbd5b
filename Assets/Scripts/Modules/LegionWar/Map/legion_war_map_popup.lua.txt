local MapManager = require "legion_war_map_manager"
local SystemManager = require "legion_war_system_manager"
local ConfigManager = require "config_manager"
local _eventSubscriber = (require "event_subscriber").New()
local EventCenter = require "event_center"

local _isSelf

local function CloseUI()
    self:GetComponent(typeof(CS.LuaScript.LuaLayerContent)):Close()
end

local function InitGuild()
    local selfData = SystemManager.GetGuildInfo()
    local rivalData = SystemManager.GetGuildInfo(true)
    
    SelfGuildName.text = selfData.name
    RivalGuildName.text = rivalData.name

    local selfAvatarConfig = ConfigManager.Tables.GuildAvatarConfig[selfData.avatar]
    SelfIcon.sprite = selfAvatarConfig.icon:Load(typeof(CS.UnityEngine.Sprite))
    
    local rivalAvatarConfig = ConfigManager.Tables.GuildAvatarConfig[rivalData.avatar]
    RivalIcon.sprite = rivalAvatarConfig.icon:Load(typeof(CS.UnityEngine.Sprite))
    
    SelfScore.text = selfData.score
    RivalScore.text = rivalData.score
end

local function Refresh()
    local mapContent = _isSelf and SelfMapContent or RivalMapContent
    LineupButton.gameObject:SetActive(not _isSelf)
    MemberButton.gameObject:SetActive(not _isSelf)
    SelfMapContent.gameObject:SetActive(_isSelf)
    RivalMapContent.gameObject:SetActive(not _isSelf)
    
    mapContent.ScriptEnv.Init(_isSelf)
    
    RivalSelect:SetActive(not _isSelf)
    SelfSelect:SetActive(_isSelf)

    local curState = SystemManager.GetCurrentState()
    JumpToRival:SetActive(_isSelf and curState >= SystemManager.LegionWarState.Match)
    JumpToSelf:SetActive(not _isSelf)
end

local function CheckState()
    local curState = SystemManager.GetCurrentState()
    if curState == SystemManager.LegionWarState.None or 
        curState == SystemManager.LegionWarState.Result then--休赛期或者结算期
        CloseUI()
    end
end

local function OnClickMemberList()
    local go = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
        CS.App.UI.LayerTag.Dialog,
        "ui/legionwar/LegionWarAttackMemberPopup")

    local luaScript = go:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    luaScript:CallFunction("Initialize")
end

function ClickSelfMap()
    if _isSelf then
        return
    end

    _isSelf = true
    Refresh()
end

function ClickRivalMap()
    if not _isSelf then
        return
    end

    _isSelf = false
    Refresh()
end

function OnClickLineupAttackFormation()
    MapManager.StartLineup(0, true)
end

function Init(isSelf)
    InitGuild()
    
    _isSelf = isSelf
    Refresh()
end

function Awake()
    MemberButton:AddClick(OnClickMemberList)
    _eventSubscriber:Subscribe(EventCenter.ID.LegionWar_SystemDataUpdated, CheckState)
end 

function OnDestroy()
    _eventSubscriber:Dispose()
end 