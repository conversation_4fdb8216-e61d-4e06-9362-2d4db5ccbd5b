local SystemManager = require "legion_war_system_manager"
local _init = false

local function OnClickShowTips()
    local lay = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
        CS.App.UI.LayerTag.Popup,
        "ui/message/CommonWindowTipsLayer")

    local luaScript = lay:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    luaScript:CallFunctionTable("ShowTips",
        {
            content = CS.LanguageManager.Instance:GetLanguage("LegionWar_Map_Help_Des", "LegionWar_Map_Help_Des"),
            title = CS.LanguageManager.Instance:GetLanguage("LegionWar_Main_Help_Des", "LegionWar_Main_Help_Des")
        })
end

function Init(isSelf)
    if _init then
        return
    end
    
    for i = 0, Root.childCount - 1 do
        local child = Root:GetChild(i):GetComponent(typeof(CS.LuaScript.LuaBehaviour))
        child.ScriptEnv.Init(isSelf)
    end

    for i = 0, LineRoot.childCount - 1 do
        local child = LineRoot:GetChild(i):GetComponent(typeof(CS.LuaScript.LuaBehaviour))
        child.ScriptEnv.Init(isSelf)
    end

    local state = SystemManager.GetCurrentState()
    if state >= SystemManager.LegionWarState.Fight and not isSelf then
        Title:SetLang("LegionWar_Map_Building_ATK", "LegionWar_Map_Building_ATK")
    else
        Title:SetLang("LegionWar_Map_Building_DEF", "LegionWar_Map_Building_DEF")
    end
    
    _init = true
end

function Awake()
    TipsButton:AddClick(OnClickShowTips)
end 