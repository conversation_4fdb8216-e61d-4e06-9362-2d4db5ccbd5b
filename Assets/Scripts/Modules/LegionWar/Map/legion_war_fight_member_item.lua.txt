local ConfigManager = require "config_manager"
local TableUtil = require "table_util"
local MapManager = require "legion_war_map_manager"
local FightManager = require "legion_war_fight_manager"
local SystemManager = require "legion_war_system_manager"
local EventCenter = require "event_center"
local _eventSubscriber = (require "event_subscriber").New()

local _pointId
local _mapId
local _isSelf
local _fightBtn

local function ClearSpineFrame()
    if SpineFrameNode.childCount > 0 then
        for i = SpineFrameNode.childCount, 1, -1 do
            local go = SpineFrameNode:GetChild(i - 1).gameObject
            go:SetActive(false)
            CS.UnityEngine.Object.Destroy(go)
        end
    end
end

local function LoadAvatar(avatarID, frameID)
    local avatar = math.max(1, avatarID)
    local avatarConfig = ConfigManager.Tables.AvatarConfig[avatar]

    local frame = math.max(1, frameID)
    local frameConfig = ConfigManager.Tables.AvatarFrameConfig[frame]

    if avatarConfig then
        local spriteAtlas = CS.LuaScript.LuaAssetOperator.LoadAsset("ui/avataricon", "avataratlas", typeof(CS.UnityEngine.U2D.SpriteAtlas))
        Head.sprite = spriteAtlas:GetSprite(avatarConfig.icon)
    end

    if frameConfig then
        if frameConfig.anim then
            ClearSpineFrame()
            local go = CS.LuaScript.LuaAssetOperator.InstantiatePrefab(
                frameConfig.anim.AssetBundlePath,
                frameConfig.anim.AssetName,
                SpineFrameNode)

            local rect = go:GetComponent(typeof(CS.UnityEngine.RectTransform))
            rect.anchoredPosition = CS.UnityEngine.Vector2(0, 0)
            Frame.gameObject:SetActive(false)
            SpineFrameNode.gameObject:SetActive(true)
        else
            Frame.sprite = frameConfig.icon:Load(typeof(CS.UnityEngine.Sprite))
            Frame.gameObject:SetActive(true)
            SpineFrameNode.gameObject:SetActive(false)
        end
    end
end

local function SetState()
    local state = MapManager.GetPointStatus(_isSelf, _pointId)
    local stage = SystemManager.GetCurrentState()
    if stage == SystemManager.LegionWarState.Fight 
        and not _isSelf 
        and state == MapManager.PointStatus.Empty then
        _fightBtn:SetActive(true)
        local mapState = MapManager.GetBuildingState(_isSelf, _mapId)
        if mapState == MapManager.BuildingState.Locked then
            _fightBtn:GetComponent(typeof(CS.App.UI.Utility.UIGrayscaleImage)).Amount = 1
        else
            _fightBtn:GetComponent(typeof(CS.App.UI.Utility.UIGrayscaleImage)).Amount = 0
        end
    else
        _fightBtn:SetActive(false)
    end
    
    if state == MapManager.PointStatus.Win then
        Mask:SetActive(true)
        OccupiedNode:SetActive(true)
        InfightingNode:SetActive(false)

        if _isSelf then
            OccupiedScore.gameObject:SetActive(false)
        else
            OccupiedScore.gameObject:SetActive(true)
            OccupiedScore.text = MapManager.GetPointMapInfo(_isSelf, _pointId).score
        end
    elseif state == MapManager.PointStatus.Empty then
        Mask:SetActive(false)
        OccupiedNode:SetActive(false)
        InfightingNode:SetActive(false)
    else
        Mask:SetActive(true)
        OccupiedNode:SetActive(false)
        InfightingNode:SetActive(true)
    end
end

local function SetPlayer(mapInfo)
    _fightBtn = FightBtnPlayer
    
    local isRobot = MapManager.IsRobot(mapInfo.teamIndex)
    local constConfig = ConfigManager.Tables.LegionWarConst
    local playerInfo = MapManager.GetPointPlayerInfo(_isSelf, _pointId)

    local avatarId = isRobot and constConfig.RobotDefaultAvatar or playerInfo.avatar
    local frameId = isRobot and constConfig.RobotDefaultFrame or playerInfo.frame

    if isRobot then
        local key = constConfig.RobotDefaultName
        Name.text = CS.LanguageManager.Instance:GetLanguage(key, key) .. playerInfo.name
    else
        Name.text = playerInfo.name
    end

    LoadAvatar(avatarId, frameId)

    if MapManager.IsOurSide(mapInfo.uid) then
        Power.text = MapManager.GetPlayerFormationInfo(mapInfo.uid, mapInfo.teamIndex).power
    else
        Power.text = mapInfo.power
    end

    if MapManager.IsCoreBuilding(_mapId) then
        CoreNode:SetActive(true)
        DefenseCount.text = MapManager.GetPointDefensiveCount(_isSelf, _mapId, _pointId)
    else
        CoreNode:SetActive(false)
    end
end

local function SetEmpty(mapInfo)
    _fightBtn = FightBtnEmpty
end

local function Refresh()
    local mapInfo = MapManager.GetPointMapInfo(_isSelf, _pointId)
    if mapInfo == nil or mapInfo.uid == nil or mapInfo.uid == "" then
        InfoNode:SetActive(false)
        EmptyNode:SetActive(true)
        SetEmpty(mapInfo)
    else
        InfoNode:SetActive(true)
        EmptyNode:SetActive(false)
        SetPlayer(mapInfo)
    end
    
    SetState()
end

local function TurnToFormation()
    MapManager.StartLineup(0, true)
end

function Init(isSelf, mapId, pointId)
    _isSelf = isSelf
    _mapId = mapId
    _pointId = pointId
    
    Refresh()
    
    if isSelf then
        _eventSubscriber:Subscribe(EventCenter.ID.LegionWar_SelfDataUpdated, Refresh)
    else
        _eventSubscriber:Subscribe(EventCenter.ID.LegionWar_RivalDataUpdated, Refresh)
    end
end

function OnClickDetails()
    local info = MapManager.GetPointMapInfo(_isSelf, _pointId)

    if info == nil then
        return
    end
    
    local go = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
        CS.App.UI.LayerTag.Popup,
        "ui/legionwar/LegionWarPlayerLineupDetailsDialog")

    local luaScript = go:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    luaScript:CallFunctionTable("SetData",
        {
            uid = info.uid, teamIndex = info.teamIndex
        })
end

function OnClickFight()
    local mapState = MapManager.GetBuildingState(_isSelf, _mapId)
    if mapState == MapManager.BuildingState.Locked then
        CS.App.UI.MessageTip.Create("LegionWar_Building_Locked");
        return
    end

    local selfUid = CS.DB.User.Id
    if SystemManager.GetMemberAttackedCount(CS.DB.User.Id) >= 
        ConfigManager.Tables.LegionWarConst.AttackTimesPerPlayer then

        CS.App.UI.MessageTip.Create("Tower_tips2");
        return
    end

    if MapManager.GetPlayerFormationInfo(selfUid) == nil then
        --CS.App.UI.MessageTip.Create("LegionWar_Not_Join");
        TurnToFormation()
        return
    end
    
    FightManager.Match(_mapId, _pointId)
end

function OnDestroy()
    _eventSubscriber:Dispose()
end

