local TableUtil = require "table_util"
local EventCenter = require "event_center"
local ConfigManager = require "config_manager"

local _gridView
local _heroDemonKingRef = CS.App.Config.ConfigManager.Instance.HeroDemonKingRef

local _appearList = {} --上阵数据
local _unlockHeroList = {} --已解锁列表
local _restHeroList = {} --空闲列表
local _heroConfigList = {} --英雄配置
local _restCount = 0
local _formationData = nil
local _onFormationHeroList = {}
local _heroFilterContent = nil
local _heroSelectItemList = {}
local _barracksList = {}

local _currentSelectHeroID = 0

local function SetActiveEx(tr, state)
    if tr then
        if state then
            local pos = tr.transform.localPosition
            if pos.x < -100000 then
                pos.x = pos.x + 1000000;
                tr.transform.localPosition = pos;
            end
        else
            local pos = tr.transform.localPosition
            if pos.x > -100000 then
                pos.x = pos.x - 1000000;
                tr.transform.localPosition = pos;
            end
        end
    end
end

function IsSelectComplete()
    local complete = #_appearList == 4
    for k, v in pairs(_appearList) do
        if v == 0 then
            complete = false
            break
        end
    end
    return complete
end

function RemoveAppearListItemById(id)
    for i = #_appearList,  1, -1 do
        if _appearList[i] == id then
            _appearList[i] = 0
            break
        end
    end
end

local function GetFormationHeroIndex(id)
    for i = 1, #_appearList do
        if _appearList[i] == id then
            return i
        end
    end

    return 0
end

local function GetBarracksHeroIndex(id)
    for k,v in pairs(_barracksList) do
        if v and v.id == id then
            return k
        end
    end

    return 0
end

local function GetBarracksHeroCount()
    local count = 0
    for i = 1, 6 do
        local data = _barracksList[i]
        if data and data.id > 0 then
            count = count + 1
        end
    end
    
    return count
end

local function GetRestFormationIndex()
    for i = 1, #_appearList do
        if _appearList[i] <= 0 then
            return i
        end
    end

    return 0
end

local function SetFormationHeroIndex(index, id)
    _appearList[index] = id
end


local function LoadAppearHero()
    local result = {}
    for i = 1, 6 do
        local data = _barracksList[i]
        if data and data.id > 0 then
            local found, level = CS.DB.Hero.Contents:TryGetValue(data.id)
            result[i] = {id = data.id, level = level}
        else
            result[i] = nil
        end
    end
    BarracksNode:CallFunctionTable("LoadBarracks",
        {
            barracks = result,
            selectCallBack = function(item, type)
                SelectHeroItem(item, type)
            end,
        })
end


local function SortRestHeroList()
    if _heroFilterContent and _heroFilterContent.HeroSorting then
        local heroTable = CS.App.Config.ConfigManager.Instance:GetConfig(typeof(CS.HeroConfigTable))
        if #_restHeroList > 0 then
            table.sort(_restHeroList, function(a, b)
                local config_a = heroTable:GetRowData(a)
                local config_b = heroTable:GetRowData(b)
                return _heroFilterContent.HeroSorting(config_a, config_b) < 0
            end)
        end
        return
    end
    
    if #_restHeroList > 0 then
        table.sort(_restHeroList, function(a, b)
            local config_a = ConfigManager.Tables.HeroConfig[a]
            local config_b = ConfigManager.Tables.HeroConfig[b]

            if config_a.quality ~= config_b.quality then
                return config_a.quality > config_b.quality
            else
                local found, level_a = CS.DB.Hero.Contents:TryGetValue(a)
                level_a = level_a or 0

                local found, level_b = CS.DB.Hero.Contents:TryGetValue(b)
                level_b = level_b or 0

                return level_a > level_b
            end
        end)
    end
end

-- 函数：计算交集
local function HasIntersection(t1, t2)
    if not t1 or not t2 then
        return false
    end

    local set = {}
    for _, v in ipairs(t1) do
        set[v] = true
    end

    for _, v in ipairs(t2) do
        if set[v] then
            return true
        end
    end

    return false
end

local function CheckFilter(id)
    if _heroFilterContent and _heroFilterContent.HeroFilter then

        local _, qualities = _heroFilterContent.HeroFilter:TryGetValue(CS.App.UI.Hero.HeroFilterType.Quality)
        local _, attributes = _heroFilterContent.HeroFilter:TryGetValue(CS.App.UI.Hero.HeroFilterType.Attribute)
        local _, camps = _heroFilterContent.HeroFilter:TryGetValue(CS.App.UI.Hero.HeroFilterType.Camp)

        local config = ConfigManager.Tables.HeroConfig[id]

        local qualityMatch = qualities == nil or qualities.Count == 0 or TableUtil.Contains(qualities, config.quality)
        local attributeMatch = attributes == nil or attributes.Count == 0 or TableUtil.Contains(attributes, config.attack_attribute)
        local campMatch = camps == nil or camps.Count == 0 or HasIntersection(camps, config.hero_camp)

        return qualityMatch and attributeMatch and campMatch
    else
        return true
    end
end

local function LoadRestHeroList()
    --加载未上阵英雄
    _restHeroList = {}
    _heroFilterContent:ClearResourceHeroes()
    _heroConfigList = CS.App.Config.ConfigManager.Instance:GetConfig(typeof(CS.HeroConfigTable))
    _unlockHeroList = CS.DB.Hero.Contents:Get()
    for k,v in pairs(_unlockHeroList) do
        if GetFormationHeroIndex(k) <= 0 and
            not table.contains(_onFormationHeroList, k) and
            GetBarracksHeroIndex(k) <= 0 and
            not _heroDemonKingRef:HeroIsTransfered(k) then
            if CheckFilter(k) then
                table.insert(_restHeroList, k)
            end
            _heroFilterContent:AddResourceHero(k, v)
        end
    end
    
    SortRestHeroList()
    _restCount = #_restHeroList
end

local function RefreshAppearList()
    LoadAppearHero()
end

local function RefreshRestList()
    LoadRestHeroList()
    _gridView:ReloadContent()

end

local function SetBarracksHero(id, index)
    _barracksList[index] = {id = id}
    SetFormation()
end

local function LoadInfo()
    local count = GetBarracksHeroCount()
    if count > 0 then
        NextButton.interactable = true
        NextButtonGray.Amount = 0
    else
        NextButton.interactable = false
        NextButtonGray.Amount = 1
    end
end

local function OnClickSelectHero(index)
    SelectNode:SetActive(false)
    SetBarracksHero(_currentSelectHeroID, index)
    CS.AudioManager.Instance:PlaySound("Hero_Go_Battle")
    OnClickBlank()
    RefreshAppearList()
    RefreshRestList()
    LoadInfo()
end

local function InitBarracksItemList()
    if #_heroSelectItemList <= 0 then
        local count = ClickNode.childCount
        for i = 1, count do
            local go = ClickNode:GetChild(i - 1).gameObject
            local button = go:GetComponent(typeof(CS.FQDev.UI.Button))
            button:AddClick(function()
                OnClickSelectHero(i)
            end)
            table.insert(_heroSelectItemList, go)
        end
    end
end

local function OnSelectCallBack(id)
    _currentSelectHeroID = id
    SelectNode:SetActive(true)
    RefreshRestList()
    BarracksNode:CallFunction("ShowSlot")
    ClickNode:SetActive(true)
end

function GetItemPrefab()
    return ItemPrefab
end

function GetItemCount()
    return _restCount
end

function OnItemShown(item, index)
    local listItem = item:GetComponent(typeof(CS.LuaScript.LuaListItem))
    local id = _restHeroList[index]
    listItem:CallFunctionTable("SetData",
        {
            id = id,
            config = _heroConfigList:GetRowData(id),
            selectCallBack = function(selectHeroID)
                OnSelectCallBack(selectHeroID)
            end
        })
    
    if _currentSelectHeroID > 0 then--正在选择
        if id ~= _currentSelectHeroID then
            listItem:CallFunction("Hide")
        else
            listItem:CallFunction("OnSelectState")
        end
    end
end

function OnClickBlank()
    _currentSelectHeroID = 0
    RefreshRestList()
    SelectNode:SetActive(false)
    ClickNode:SetActive(false)
    BarracksNode:CallFunction("RefreshUI")
end

function SelectHeroItem(item, type)
    local index = 0
    if type == CS.App.UI.Hero.HeroItemChooseType.Remove then
        index = GetBarracksHeroIndex(item.HeroId)
        table.insert(_restHeroList, item.HeroId)
        SetBarracksHero(0, index)
        RefreshAppearList()
        RefreshRestList()
        LoadInfo()
    elseif type == CS.App.UI.Hero.HeroItemChooseType.ShowDetail then
        local layer = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
            CS.App.UI.LayerTag.Popup,
            "ui/hero/HeroDetailLayer")

        local lay = layer.gameObject:GetComponent(typeof(CS.App.UI.Hero.HeroDetailLayer))
        local luaScript = layer.gameObject:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
        lay:SetData(item)
        luaScript:CallFunctionTable("SetData", {hideUpgrade = true})
        OnClickBlank()
    end
end

function RefreshContent()
    LoadAppearHero()
    RefreshRestList()
end

function SetData(data)
    InitBarracksItemList()
    _formationData = data
    _appearList = data.getFormation()
    _onFormationHeroList = data.onFormationHeroList
    LoadAppearHero()
    RefreshRestList()
    LoadInfo()
end

function Awake()
    _gridView = self:GetComponent(typeof(CS.App.UI.Utility.UIGridView))
    _heroFilterContent = self:GetComponent(typeof(CS.App.UI.Hero.HeroFilterContentLua))
    _heroFilterContent.OnHeroSortingChanged = function()
        SortRestHeroList()
        RefreshRestList()
    end

    _heroFilterContent.OnHeroFilterChanged = function()
        RefreshRestList()
    end
end

function OnEnable()

end

function SetFormation()
    _formationData.setFormation(_barracksList)
    EventCenter.DispatchEvent(_formationData.eventID)
end

function OnDestroy()

end