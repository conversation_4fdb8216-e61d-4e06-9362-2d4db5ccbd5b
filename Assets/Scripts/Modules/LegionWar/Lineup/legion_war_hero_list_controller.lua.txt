local TableUtil = require "table_util"
local EventCenter = require "event_center"
local ConfigManager = require "config_manager"

local _gridView
local _heroDemonKingRef = CS.App.Config.ConfigManager.Instance.HeroDemonKingRef

local _appearList = {} --上阵数据
local _currentAppearCount = 0 --当前上阵英雄数量
local _appearHeroItemList = {} --上阵Item列表
local _unlockHeroList = {} --已解锁列表
local _restHeroList = {} --空闲列表
local _heroConfigList = {} --英雄配置
local _currentSelectItem = nil --当前选中的Item
local _swapUsedItem = nil --当前进行交换的Item
local _onSwapState = false --是否在交换状态
local _swapHeroID = 0
local _restCount = 0
local _formationData = nil
local _onFormationHeroList = {}

local _heroFilterContent = nil

function IsSelectComplete()
    local complete = #_appearList == 4
    for k, v in pairs(_appearList) do
        if v == 0 then
            complete = false
            break
        end
    end
    return complete
end

function RemoveAppearListItemById(id)
    for i = #_appearList,  1, -1 do
        if _appearList[i] == id then
            _appearList[i] = 0
            break
        end
    end
end

local function GetFormationHeroIndex(id)
    for i = 1, #_appearList do
        if _appearList[i] == id then
            return i
        end
    end

    return 0
end

local function GetRestFormationIndex()
    for i = 1, #_appearList do
        if _appearList[i] <= 0 then
            return i
        end
    end

    return 0
end

local function SetFormationHeroIndex(index, id)
    _appearList[index] = id
end

local function LoadAppearHero()
    _heroConfigList = CS.App.Config.ConfigManager.Instance:GetConfig(typeof(CS.HeroConfigTable))
    _currentAppearCount = #_appearList
    for i = 1, #_appearList do
        local id = _appearList[i]
        local item = nil
        if i > #_appearHeroItemList then
            local go = CS.LuaScript.LuaAssetOperator.InstantiatePrefab(
                    "ui/hero",
                    "HeroItem2",
                    AppearHeroNode.transform)
            item = go:GetComponent(typeof(CS.App.UI.Hero.HeroItem))
            table.insert(_appearHeroItemList, item)
        else
            item = _appearHeroItemList[i]
        end
        if id > 0 then
            local found, level = CS.DB.Hero.Contents:TryGetValue(id)
            level = level or 0
            local config = _heroConfigList:GetRowData(id)

            item:FreshUI(config.Id, level, config, function()
                return GetFormationHeroIndex(id) > 0
            end, function()
                return GetRestFormationIndex() == 0
            end)
            --item.DisableButton = true
            item:SetButtonAction(1)
            item.ActionChoose = SelectHeroItem
        end
    end

    for i = 1, #_appearHeroItemList do
        local id = _appearList[i]
        _appearHeroItemList[i].gameObject:SetActive(i <= #_appearList and id > 0)
    end
end


local function SortRestHeroList()
    if _heroFilterContent and _heroFilterContent.HeroSorting then
        local heroTable = CS.App.Config.ConfigManager.Instance:GetConfig(typeof(CS.HeroConfigTable))
        if #_restHeroList > 0 then
            table.sort(_restHeroList, function(a, b)
                local config_a = heroTable:GetRowData(a)
                local config_b = heroTable:GetRowData(b)
                return _heroFilterContent.HeroSorting(config_a, config_b) < 0
            end)
        end
        return
    end
    
    if #_restHeroList > 0 then
        table.sort(_restHeroList, function(a, b)
            local config_a = ConfigManager.Tables.HeroConfig[a]
            local config_b = ConfigManager.Tables.HeroConfig[b]

            if config_a.quality ~= config_b.quality then
                return config_a.quality > config_b.quality
            else
                local found, level_a = CS.DB.Hero.Contents:TryGetValue(a)
                level_a = level_a or 0

                local found, level_b = CS.DB.Hero.Contents:TryGetValue(b)
                level_b = level_b or 0

                return level_a > level_b
            end
        end)
    end
end

-- 函数：计算交集
local function HasIntersection(t1, t2)
    if not t1 or not t2 then
        return false
    end

    local set = {}
    for _, v in ipairs(t1) do
        set[v] = true
    end

    for _, v in ipairs(t2) do
        if set[v] then
            return true
        end
    end

    return false
end

local function CheckFilter(id)
    if _heroFilterContent and _heroFilterContent.HeroFilter then

        local _, qualities = _heroFilterContent.HeroFilter:TryGetValue(CS.App.UI.Hero.HeroFilterType.Quality)
        local _, attributes = _heroFilterContent.HeroFilter:TryGetValue(CS.App.UI.Hero.HeroFilterType.Attribute)
        local _, camps = _heroFilterContent.HeroFilter:TryGetValue(CS.App.UI.Hero.HeroFilterType.Camp)

        local config = ConfigManager.Tables.HeroConfig[id]

        local qualityMatch = qualities == nil or qualities.Count == 0 or TableUtil.Contains(qualities, config.quality)
        local attributeMatch = attributes == nil or attributes.Count == 0 or TableUtil.Contains(attributes, config.attack_attribute)
        local campMatch = camps == nil or camps.Count == 0 or HasIntersection(camps, config.hero_camp)

        return qualityMatch and attributeMatch and campMatch
    else
        return true
    end
end

local function LoadRestHeroList()
    --加载未上阵英雄
    _restHeroList = {}
    _heroFilterContent:ClearResourceHeroes()
    _heroConfigList = CS.App.Config.ConfigManager.Instance:GetConfig(typeof(CS.HeroConfigTable))
    _unlockHeroList = CS.DB.Hero.Contents:Get()
    for k,v in pairs(_unlockHeroList) do
        if GetFormationHeroIndex(k) <= 0 and
            not table.contains(_onFormationHeroList, k) and
            not _heroDemonKingRef:HeroIsTransfered(k) then
            if CheckFilter(k) then
                table.insert(_restHeroList, k)
            end
            _heroFilterContent:AddResourceHero(k, v)
        end
    end
    
    SortRestHeroList()
    _restCount = #_restHeroList
end

local function RefreshAppearList()
    LoadAppearHero()
end

local function RefreshRestList()
    LoadRestHeroList()
    _gridView:ReloadContent()

end

function GetItemPrefab()
    return HeroItemPrefab
end

function GetItemCount()
    return _restCount
end

function OnItemShown(item, index)
    local heroItem = item:GetComponent(typeof(CS.App.UI.Hero.HeroItem))
    
    local id = _restHeroList[index]
    local config = _heroConfigList:GetRowData(id)
    local found, level = CS.DB.Hero.Contents:TryGetValue(id)
    level = level or 0

    if _onSwapState then
        item.gameObject:SetActive(_swapUsedItem.HeroId == id)
    else
        item.gameObject:SetActive(true)
    end
    heroItem:FreshUI(config.Id, level, config, function()
        return GetFormationHeroIndex(id) > 0
    end, function() 
        return GetRestFormationIndex() == 0
    end)
--[[    _item.DisableButton = true]]

    heroItem:SetButtonAction(1)
    heroItem.ActionChoose = SelectHeroItem
end

function OnClickBlank()
    if _currentSelectItem then
        _currentSelectItem:HideButtons()
    end
    _currentSelectItem = nil

    if _swapUsedItem then
        _swapUsedItem.IsWaitingUse = false
        _swapUsedItem = nil;
    end

    if _onSwapState then
        for i = 1, #_appearHeroItemList do
            if i <= _currentAppearCount then
                local item = _appearHeroItemList[i]
                item:StopWaitingForRemove()
            end
        end
        _onSwapState = false
        RefreshRestList()
    end
end

function SelectHeroItem(item, type)
    local index = 0
    if type == CS.App.UI.Hero.HeroItemChooseType.NormalChoose then
        if item ~= _currentSelectItem then
            if _currentSelectItem then
                _currentSelectItem:HideButtons()
            end

            _currentSelectItem = item
            if GetFormationHeroIndex(item.HeroId) <= 0 then
                _currentSelectItem.transform:SetAsLastSibling()
            end
        end
    elseif type == CS.App.UI.Hero.HeroItemChooseType.Remove then
        index = GetFormationHeroIndex(item.HeroId)
        table.insert(_restHeroList, item.HeroId)
        SetFormationHeroIndex(index, 0);

        RefreshAppearList()
        RefreshRestList()
    elseif type == CS.App.UI.Hero.HeroItemChooseType.Use then
        index = GetRestFormationIndex()

        if index == 0 then
            return
        end

        SetFormationHeroIndex(index, item.HeroId)
        CS.AudioManager.Instance:PlaySound("Hero_Go_Battle")

        RefreshAppearList()
        RefreshRestList()

    elseif type == CS.App.UI.Hero.HeroItemChooseType.SwapUse then
        _swapUsedItem = item
        _swapHeroID = item.HeroId
        local heroItem = nil
        for i = 1, #_appearHeroItemList do
            if i <= _currentAppearCount then
                heroItem = _appearHeroItemList[i]
                heroItem:WaitingForRemove()
            end
        end

        _onSwapState = true
        RefreshAppearList()
        RefreshRestList()
    elseif type == CS.App.UI.Hero.HeroItemChooseType.SwapRemove then
        index = GetFormationHeroIndex(item.HeroId);
        table.insert(_restHeroList, item.HeroId)
        
        SetFormationHeroIndex(index, _swapHeroID);
        CS.AudioManager.Instance:PlaySound("Hero_Go_Battle");
        
        _swapUsedItem.IsWaitingUse = false
        _swapUsedItem = nil
        local heroItem = nil
        for i = 1, #_appearHeroItemList do
            if i <= _currentAppearCount then
                heroItem = _appearHeroItemList[i]
                heroItem:StopWaitingForRemove()
            end
        end
        _onSwapState = false
        RefreshAppearList()
        RefreshRestList()
    elseif type == CS.App.UI.Hero.HeroItemChooseType.ShowDetail then
        local layer = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
                CS.App.UI.LayerTag.Popup,
                "ui/hero/HeroDetailLayer")

        local lay = layer.gameObject:GetComponent(typeof(CS.App.UI.Hero.HeroDetailLayer))
        local luaScript = layer.gameObject:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
        lay:SetData(item)
        luaScript:CallFunctionTable("SetData", {hideUpgrade = true})
        OnClickBlank()
    end
end

local function FixDemonKingHero(formation)
    for i = 1, 4 do
        if formation[i] > 0 and 
        _heroDemonKingRef:HeroIsTransfered(formation[i]) then
            formation[i] = 0
        end
    end
end

function RefreshContent()
    LoadAppearHero()
    RefreshRestList()
end

function SetData(data)
    _formationData = data
    _appearList = data.getFormation()
    _onFormationHeroList = data.onFormationHeroList
    FixDemonKingHero(_appearList)
    LoadAppearHero()
    RefreshRestList()
end

function Awake()
    _gridView = self:GetComponent(typeof(CS.App.UI.Utility.UIGridView))
    _heroFilterContent = self:GetComponent(typeof(CS.App.UI.Hero.HeroFilterContentLua))
    _heroFilterContent.OnHeroSortingChanged = function()
        SortRestHeroList()
        RefreshRestList()
    end

    _heroFilterContent.OnHeroFilterChanged = function()
        RefreshRestList()
    end
end

function OnEnable()
    LoadAppearHero()
    RefreshRestList()
end

function SetFormation()
    for i = 1, 4 do
        if _appearList[i] == nil or _appearList[i] <= 0 then
            CS.App.UI.MessageTip.Create("Four_Hero")
            return
        end
    end

    _formationData.setFormation(_appearList)
    EventCenter.DispatchEvent(_formationData.eventID)
end

function OnDestroy()
    if _onSwapState then
        local heroItem = nil
        for i = 1, #_appearHeroItemList do
            if i <= _currentAppearCount then
                heroItem = _appearHeroItemList[i]
                heroItem:StopWaitingForRemove()
            end
        end
        _onSwapState = false
    end
    -- SetFormation()
end