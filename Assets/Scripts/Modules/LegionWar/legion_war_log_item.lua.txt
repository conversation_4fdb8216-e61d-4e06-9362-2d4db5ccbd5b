local ConfigManager = require "config_manager"
local TableUtil = require "table_util"
local EventCenter = require "event_center"
local _eventSubscriber = (require "event_subscriber").New()
local GuildManager = require "guild_manager"
local LegionWarSystemManager = require "legion_war_system_manager"

local _seasonSeriesID = 0
local _logData = nil

local function SetResultInfo(victory)
    if victory then
        DateBg.sprite = CS.LuaScript.LuaAssetOperator.LoadAsset("ui/legionwar", "BattleLogDateBox", typeof(CS.UnityEngine.Sprite))
        Bg.sprite = CS.LuaScript.LuaAssetOperator.LoadAsset("ui/legionwar", "BattleLogVictoryListBox", typeof(CS.UnityEngine.Sprite))
    else
        DateBg.sprite = CS.LuaScript.LuaAssetOperator.LoadAsset("ui/legionwar", "BattleLogDateRedBox", typeof(CS.UnityEngine.Sprite))
        Bg.sprite = CS.LuaScript.LuaAssetOperator.LoadAsset("ui/legionwar", "BattleLogFailListBox", typeof(CS.UnityEngine.Sprite))
    end
    VictoryNode:SetActive(victory)
    FailedNode:SetActive(not victory)
end

local function LoadGuildInfo(node, avatar, guildName, score)
    local iconImage = node:Find("GuildIcon").gameObject:GetComponent(typeof(CS.UnityEngine.UI.Image))
    local guildNameText = node:Find("GuildName").gameObject:GetComponent(typeof(CS.TMPro.TextMeshProUGUI))
    local scoreText = node:Find("ScoreIcon/Score").gameObject:GetComponent(typeof(CS.TMPro.TextMeshProUGUI))
    local guildAvatarConfig = ConfigManager.Tables.GuildAvatarConfig[avatar]
    iconImage.sprite = guildAvatarConfig.icon:Load(typeof(CS.UnityEngine.Sprite))
    guildNameText.text = guildName
    scoreText.text = score
end

local function LoadBaseInfo(seasonSeriesID)
    local seasonConfig = ConfigManager.Tables.LegionWarSeasonConfig[seasonSeriesID]
    local time = LegionWarSystemManager.GetTimeByDate(seasonConfig.season_start_time)
    local date = os.date("!*t", time)
    Date.text = string.format("%02d/%02d/%02d", date.year, date.month, date.day)
end

function SetData(data)
    _logData = data
    _seasonSeriesID = data.seasonId
    SetResultInfo(data.isWin)
    local guildData = GuildManager.GetCurrentGuildData()
    LoadGuildInfo(SelfInfoNode, guildData.avatar, guildData.name, data.myScore)
    LoadGuildInfo(RivalInfoNode, data.enemyAvatar, data.enemyName, data.enemyScore)
    LoadBaseInfo(data.seasonId)
    --Date.text = data.date
end

local function OnClickDetails()
    local go = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
        CS.App.UI.LayerTag.Dialog,
        "ui/legionwar/LegionWarLogDetailDialog")

    local luaScript = go:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    luaScript:CallFunctionTable("Initialize", {
        seasonSeriesID = _seasonSeriesID,
        logData = _logData,
    })
end


function Awake()
    DetailsButton:AddClick(OnClickDetails)
end

