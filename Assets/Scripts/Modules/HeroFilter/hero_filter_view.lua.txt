local ConfigManager = require "config_manager"

-------------Sort----------------
local _cellHeight
local _showNum = 2
local _heroFilterLuaSetter

local sortingItems = {}

local function ClickExpand()
    local iconTran = ExpandBtn.transform:GetChild(0)
    iconTran:DOKill();
    if ExpandBtn.interactable then
        ExpandBtn.interactable = false
        Scroll.transform:SetHeight(_cellHeight * _showNum)
        Scroll.enabled = true
        iconTran:DOLocalRotate(CS.UnityEngine.Vector3.forward * 180, 0.2)
    else
        ExpandBtn.interactable = true
        Scroll.transform:SetHeight(_cellHeight)
        Scroll.enabled = false
        iconTran:DOLocalRotate(CS.UnityEngine.Vector3.zero, 0.2)
    end
    
end

local function ClickLanguageItem(item)
    if item == sortingItems.QualityPriority then
        _heroFilterLuaSetter:SetSort(1)
        Scroll.content:SetLocPosY(1 * _cellHeight);
    else
        _heroFilterLuaSetter:SetSort(0)
        Scroll.content:SetLocPosY(0 * _cellHeight);
    end

    ClickExpand()
end

local function InitializeSort()
    ExpandBtn:AddClick(ClickExpand)

    local qualityBtn = sortingItems.QualityPriority:GetComponent(typeof(CS.FQDev.UI.Button))
    qualityBtn:AddClick(function()
        ClickLanguageItem(sortingItems.QualityPriority)
    end)

    local levelBtn = sortingItems.LevelPriority:GetComponent(typeof(CS.FQDev.UI.Button))
    levelBtn:AddClick(function()
        ClickLanguageItem(sortingItems.LevelPriority)
    end)
    
    Scroll.content:SetLocPosY(_heroFilterLuaSetter.SortTag * _cellHeight);
end

-------------Filter----------------
local _qualityCells = {}
local _attributeCells = {}
local _campCells = {}
local _enableCallback = false

local SelectedColor = CS.UnityEngine.Color(0.9411765, 0.6588235, 0.03529412, 1)
local DefaultColor = CS.UnityEngine.Color(0.3647059, 0.3372549, 0.4313726, 1)

local function RefreshAttributeToggle()
    local heroTables = ConfigManager.Tables.HeroConfig
    local allAttribute = {}
    for id, _ in pairs(_heroFilterLuaSetter:GetResourceHeroes()) do
        local config = heroTables[id]
        local found = _heroFilterLuaSetter:ContainsFilter(CS.App.UI.Hero.HeroFilterType.Quality, config.quality)
        local attributeIndex = config.attack_attribute + 1
        if found then
            allAttribute[attributeIndex] = true -- 属性有0值
        else
            if allAttribute[attributeIndex] ~= true then
                allAttribute[attributeIndex] = false
            end
        end
    end

    for attribute, go in pairs(_attributeCells) do
        local have = allAttribute[attribute]
        local cell = go:GetComponent(typeof(CS.UnityEngine.UI.Image))
        cell.color = DefaultColor
        cell.raycastTarget = have

        go.transform:GetChild(0):GetComponent(typeof(CS.App.UI.Utility.UIGrayscaleImage)).Grayscale = not have
    end
end

local function RefreshQualityToggle()
    local heroTables = ConfigManager.Tables.HeroConfig
    local allAttribute = {}
    for id, _ in pairs(_heroFilterLuaSetter:GetResourceHeroes()) do
        local config = heroTables[id]
        local found = _heroFilterLuaSetter:ContainsFilter(CS.App.UI.Hero.HeroFilterType.Attribute, config.attack_attribute)
        local qualityIndex = heroTables[id].quality
        if found then
            allAttribute[qualityIndex] = true -- 属性有0值
        else
            if allAttribute[qualityIndex] ~= true then
                allAttribute[qualityIndex] = false
            end
        end
    end

    for qualityIndex, go in pairs(_qualityCells) do
        local have = allAttribute[qualityIndex]
        local cell = go:GetComponent(typeof(CS.UnityEngine.UI.Image))
        cell.color = DefaultColor
        cell.raycastTarget = have

        go.transform:GetChild(0):GetComponent(typeof(CS.App.UI.Utility.UIGrayscaleImage)).Grayscale = not have
    end
end

local function OnAttributeToggleChanged(go ,isOn)
    if not _enableCallback then
        return
    end
    
    local key = -1
    for k, g in pairs(_attributeCells) do
        if g == go then
            key = k - 1
            break
        end
    end

    local cell = go:GetComponent(typeof(CS.UnityEngine.UI.Image))
    local type = CS.App.UI.Hero.HeroFilterType.Attribute
    if isOn then
        cell.color = SelectedColor
        _heroFilterLuaSetter:AddFilter(type, key)
    else
        cell.color = DefaultColor
        _heroFilterLuaSetter:RemoveFilter(type, key)
    end

    RefreshQualityToggle()
end

local function OnQualityToggleChanged(go ,isOn)
    if not _enableCallback then
        return
    end
    
    local key = -1
    for k, g in pairs(_qualityCells) do
        if g == go then
            key = k
            break
        end
    end

    local cell = go:GetComponent(typeof(CS.UnityEngine.UI.Image))
    local type = CS.App.UI.Hero.HeroFilterType.Quality
    if isOn then
        cell.color = SelectedColor
        _heroFilterLuaSetter:AddFilter(type, key)
    else
        cell.color = DefaultColor
        _heroFilterLuaSetter:RemoveFilter(type, key)
    end

    RefreshAttributeToggle()
end

local function OnCampToggleChanged(go ,isOn)
    if not _enableCallback then
        return
    end
    
    local key = -1
    for k, g in pairs(_campCells) do
        if g == go then
            key = k
            break
        end
    end

    local cell = go:GetComponent(typeof(CS.UnityEngine.UI.Image))
    local type = CS.App.UI.Hero.HeroFilterType.Camp
    if isOn then
        cell.color = SelectedColor
        _heroFilterLuaSetter:AddFilter(type, key)
    else
        cell.color = DefaultColor
        _heroFilterLuaSetter:RemoveFilter(type, key)
    end
end

local function InitFilter()
    local heroTables = ConfigManager.Tables.HeroConfig
    local attributeType = CS.App.UI.Hero.HeroFilterType.Attribute
    local qualityType = CS.App.UI.Hero.HeroFilterType.Quality
    local allQuality = {}
    local allAttribute = {}
    local allCamp = {}
    for id, config in pairs(heroTables) do
        if config.lock_invisible == 0 then
            local found = _heroFilterLuaSetter:GetResourceHeroes():ContainsKey(id)
            if found then
                allQuality[config.quality] = true
                allAttribute[config.attack_attribute + 1] = true -- 属性有0值
                for _, camp in pairs(config.hero_camp) do
                    allCamp[camp] = true
                end
            else
                if allQuality[config.quality] ~= true then
                    allQuality[config.quality] = false
                end

                if allAttribute[config.attack_attribute + 1] ~= true then
                    allAttribute[config.attack_attribute + 1] = false
                end
            end
        end
    end

    --品质
    local qualityTable = ConfigManager.Tables.HeroQualityConfig
    for quality, have in pairs(allQuality) do
        if _qualityCells[quality] == nil then
            local go = CS.UnityEngine.GameObject.Instantiate(QualityCell, QualityContent, false)
            _qualityCells[quality] = go

            local toggle = go:GetComponent(typeof(CS.UnityEngine.UI.Toggle))
            
            local containsFilter = _heroFilterLuaSetter:ContainsFilter(qualityType, quality)
            
            toggle.isOn = not _heroFilterLuaSetter:IsEmptyFilter(qualityType) and have and containsFilter
            toggle.onValueChanged:AddListener(function(isOn)
                OnQualityToggleChanged(go, isOn)
            end)
            
            local icon = go.transform:GetChild(0):GetComponent(typeof(CS.App.UI.Utility.UIGrayscaleImage))
            icon.sprite = qualityTable[quality].icon_bg:Load(typeof(CS.UnityEngine.Sprite))
            
            if (not have) and containsFilter then
                _heroFilterLuaSetter:RemoveFilter(qualityType, quality)
            end
        end

        local cell = _qualityCells[quality]:GetComponent(typeof(CS.UnityEngine.UI.Image))
        cell.color = DefaultColor
        cell.raycastTarget = have
        
        _qualityCells[quality].transform:GetChild(0)
                :GetComponent(typeof(CS.App.UI.Utility.UIGrayscaleImage)).Grayscale = not have
    end
    QualityCell:SetActive(false)

    --属性
    local attributeTable = ConfigManager.Tables.AttackAttributeConfig
    for attribute, have in pairs(allAttribute) do
        if _attributeCells[attribute] == nil then
            local go = CS.UnityEngine.GameObject.Instantiate(AttributeCell, AttributeContent, false)
            _attributeCells[attribute] = go
            
            local toggle = go:GetComponent(typeof(CS.UnityEngine.UI.Toggle))
            local containsFilter = _heroFilterLuaSetter:ContainsFilter(attributeType, attribute - 1)
            toggle.isOn =  not _heroFilterLuaSetter:IsEmptyFilter(attributeType) and have and containsFilter
            toggle.onValueChanged:AddListener(function(isOn)
                OnAttributeToggleChanged(go, isOn)
            end)
            
            local icon = go.transform:GetChild(0):GetComponent(typeof(CS.App.UI.Utility.UIGrayscaleImage))
            icon.sprite = attributeTable[attribute - 1].res:Load(typeof(CS.UnityEngine.Sprite))

            if (not have) and containsFilter then
                _heroFilterLuaSetter:RemoveFilter(attributeType, attribute - 1)
            end
        end

        local cell = _attributeCells[attribute]:GetComponent(typeof(CS.UnityEngine.UI.Image))
        cell.color = DefaultColor
        cell.raycastTarget = have

        _attributeCells[attribute].transform:GetChild(0)
                :GetComponent(typeof(CS.App.UI.Utility.UIGrayscaleImage)).Grayscale = not have
    end
    AttributeCell:SetActive(false)

    --阵营
    local campTable = ConfigManager.Tables.CampConfig
    for camp, have in pairs(allCamp) do
        if _campCells[camp] == nil then
            local go = CS.UnityEngine.GameObject.Instantiate(CampCell, CampContent, false)
            _campCells[camp] = go
            local toggle = go:GetComponent(typeof(CS.UnityEngine.UI.Toggle))
            local containsFilter = _heroFilterLuaSetter:ContainsFilter(CS.App.UI.Hero.HeroFilterType.Camp, camp)
            toggle.isOn =  not _heroFilterLuaSetter:IsEmptyFilter(CS.App.UI.Hero.HeroFilterType.Camp) and have and containsFilter
            toggle.onValueChanged:AddListener(function(isOn)
                OnCampToggleChanged(go, isOn)
            end)
            
            local icon = go.transform:GetChild(0):GetComponent(typeof(CS.App.UI.Utility.UIGrayscaleImage))
            icon.sprite = campTable[camp].icon:Load(typeof(CS.UnityEngine.Sprite))
            if (not have) and containsFilter then
                _heroFilterLuaSetter:RemoveFilter(CS.App.UI.Hero.HeroFilterType.Camp, camp)
            end
        end
        local cell = _campCells[camp]:GetComponent(typeof(CS.UnityEngine.UI.Image))
        cell.color = DefaultColor
        cell.raycastTarget = have

        _campCells[camp].transform:GetChild(0)
                :GetComponent(typeof(CS.App.UI.Utility.UIGrayscaleImage)).Grayscale = not have
    end
    CampCell:SetActive(false)
    
    
    _enableCallback = true
end

-----------------------------------
---@param heroItemList CS.App.UI.Hero.IHeroItemList
function InitData(heroFilterLuaSetter)
    _heroFilterLuaSetter = heroFilterLuaSetter
    InitializeSort()
    InitFilter()
end

local function OnClickConfirm()
    _heroFilterLuaSetter:ConfirmSet()
    self:GetComponent(typeof(CS.LuaScript.LuaLayerContent)):Close()
end

local function OnClickClose()
    _heroFilterLuaSetter:CancelSet()
    self:GetComponent(typeof(CS.LuaScript.LuaLayerContent)):Close()
end

local function OnClickReset()
    _enableCallback = false
    for _, go in pairs(_qualityCells) do
        local toggle = go:GetComponent(typeof(CS.UnityEngine.UI.Toggle))
        toggle.isOn = false
    end

    for _, go in pairs(_attributeCells) do
        local toggle = go:GetComponent(typeof(CS.UnityEngine.UI.Toggle))
        toggle.isOn = false
    end
    _heroFilterLuaSetter:ClearFilter()
    InitFilter()
end

function Awake()
    _cellHeight = Cell:GetComponent(typeof(CS.UnityEngine.RectTransform)).rect.height
    sortingItems.LevelPriority = Cell
    sortingItems.QualityPriority = Cell1

    BtnConfirm:AddClick(OnClickConfirm)
    BtnReset:AddClick(OnClickReset)
    BtnClose:AddClick(OnClickClose)
end