local TableUtil = require "table_util"
local EventCenter = require "event_center"
local RankListManager = require "rank_list_manager"
local ConfigManager = require "config_manager"

local _eventSubscriber = (require "event_subscriber").New()

local _gridView
local _itemCount = 0
local _scrollHeight = 0
local _rankData = nil
local _startIndex = 1
local _needReqMore = false
local _activityIcon = nil


local function GetContentHeight()
    return Content.rect.height
end

local function ReqGetMoreRankList()
    if not _rankData then
        return
    end

    RankListManager.ReqRankList(20, true, _rankData.rankType, #_rankData.list + 1)
end

local function OnDragging()
    if GetContentHeight() <= 0 or _needReqMore then
        return
    end

    if GetContentHeight() > _scrollHeight then
        if Content.anchoredPosition.y + _scrollHeight - GetContentHeight() >= 150 then
            _needReqMore = true
            if not Loading.activeSelf then
                Loading:SetActive(true)
            end
        end
    else
        if Content.anchoredPosition.y >= 150 then
            _needReqMore = true
            if not Loading.activeSelf then
                Loading:SetActive(true)
            end
        end
    end
end



function GetItemPrefab()
    return RankItemPrefab
end

function GetItemCount()

    return _itemCount
end

function OnItemShown(item, index)
    local listItem = item:GetComponent(typeof(CS.LuaScript.LuaListItem))
    listItem:CallFunctionTable("SetData", 
    {
        rank = _startIndex + index - 1, 
        playerData = _rankData.list[_startIndex + index - 1], 
        rankType = _rankData.rankType,
        icon = _activityIcon
    })
end

function OnDrag(offset)
    OnDragging()
end

function OnEndDrag()
    if _needReqMore then
        ReqGetMoreRankList()
    end
end

local function InitData()
    if _rankData then
        if _startIndex <= #_rankData.list then
            _itemCount = #_rankData.list - _startIndex + 1
        else
            _itemCount = 0
        end
    else
        _itemCount = 0
    end
    _scrollHeight = Root.rect.height
end

local function OnRefresh()

    _gridView:ReloadContent()
    _gridView:StopMovement()
    _needReqMore = false
    Loading:SetActive(false)
end

function LoadRankItemList(data)
    _rankData = data.rankData
    _startIndex = data.startIndex or 1
    _activityIcon = data.icon
    InitData()
    OnRefresh()
end


function Awake()

    _gridView = self:GetComponent(typeof(CS.App.UI.Utility.UIGridView))
    --_eventSubscriber:Subscribe(EventCenter.ID.PeriodShop_ShopItemUpdated, OnRefresh)

end

function Start()
    --_gridView:MoveToItem(#_towerConfigList)
end

function OnDestroy()
    _eventSubscriber:Dispose()
end