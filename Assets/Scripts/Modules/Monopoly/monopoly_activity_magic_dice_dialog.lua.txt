local ConfigManager = require "config_manager"
local _eventSubscriber = (require "event_subscriber").New()
local EventCenter = require "event_center"
local MonopolyManager = require "monopoly_manager"
local TableUtil = require "table_util"


local _callBack = nil
local _currentDice = 0

local function Close()
    local layer = self.gameObject:GetComponent(typeof(CS.FQDev.UI.Layer.LayerContent))
    layer:Close()
end

local function LoadInfo()
    local activityConfig = MonopolyManager.GetMonopolyActivityConfig()
    local itemConfig = ConfigManager.Tables.ItemConfig[activityConfig.prop_id[2]]
    CoinIcon.sprite = itemConfig.icon:Load(typeof(CS.UnityEngine.Sprite))
    CoinCount.text = "x"..CS.App.Bag.BagManager.Instance:GetItemCount(activityConfig.prop_id[2])
end

local function OnClickSelectDice()
    if _callBack then
        _callBack(_currentDice)
    end

    Close()
end

local function OnSelectDice(index)
    _currentDice = index
end

local function LoadToggleGroup()
    local count = GroupNode.childCount
    for i = 1, count do
        local go = GroupNode:GetChild(i - 1)
        local toggle = go.gameObject:GetComponent(typeof(CS.LuaScript.LuaToggle))
        toggle.ScriptEnv.SetToggleGroup(DiceGroup.ScriptEnv)
    end

    DiceGroup.ScriptEnv.SetValueHandler(OnSelectDice)
end

function SetData(data)
    LoadToggleGroup()
    LoadInfo()
    _callBack = data.callBack
    DiceGroup.ScriptEnv.SelectIndex(1)
end

function Awake()
    SureButton:AddClick(OnClickSelectDice)
end

