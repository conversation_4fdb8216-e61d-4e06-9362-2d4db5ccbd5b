local ConfigManager = require "config_manager"
local StringModifier = require "string_modifier"

local _skillItemList = {}
local _skillCoreItem = nil

local function ShowSkillDetails()
    local layer = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
        CS.App.UI.LayerTag.Popup,
        "ui/entrancemerge/SkillDetailsPop")

    local luaScript = layer:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    luaScript:CallFunctionTable("SetData", 
    {
        skillListStr = SkillListStr,
        skillCoreID = SkillCoreID,
    })
end

local function LoadBaseInfo()
    local config = ConfigManager.Tables.HeroConfig[HeroID]
    HeroName.text = CS.LanguageManager.Instance:GetLanguage(config.name, config.name)
end

local function LoadSkillCore()
    if not SkillCoreID or SkillCoreID <= 0 then
        return
    end

    if not _skillCoreItem then
        local go = CS.LuaScript.LuaAssetOperator.InstantiatePrefab(
            "ui/common",
            "SkillCoreItem2",
            SkillNode)

        _skillCoreItem = go:GetComponent(typeof(CS.App.UI.Common.SkillCoreItem))
    end
    local button = _skillCoreItem.gameObject:GetComponent(typeof(CS.FQDev.UI.Button))
    button:RemoveAllClick()
    button:AddClick(ShowSkillDetails)
    _skillCoreItem:SetData(SkillCoreID)
end

local function LoadSkillList()
    local skillStr = StringModifier.Split(SkillListStr, "|")
    for i = 1, #skillStr do
        local item = nil
        if i > #_skillItemList then
            local go = CS.UnityEngine.GameObject.Instantiate(SkillItemPrefab, SkillNode)
            item = go:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
            table.insert(_skillItemList, item)
        else
            item = _skillItemList[i]
        end

        item:CallFunctionTable("SetData", {id = tonumber(skillStr[i]), callBack = function()
            ShowSkillDetails()
        end})
    end

    for i = 1, #_skillItemList do
        _skillItemList[i].gameObject:SetActive(i <= #skillStr)
    end
end

local function OnRefresh()
    LoadSkillCore()
    LoadSkillList()
    LoadBaseInfo()
end

function Awake()
    TipsButton:AddClick(ShowSkillDetails)
end

function OnEnable()
    OnRefresh()
end

function OnDestroy()

end