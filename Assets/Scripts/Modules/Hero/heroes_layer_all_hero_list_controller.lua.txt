local ConfigManager = require "config_manager"
local HeroManager = CS.App.Hero.HeroManager
local CSConfigManager = CS.App.Config.ConfigManager
local TableUtil = require "table_util"
local _csEventSubscriber

local HeroUnlockCondition = {
    PassChapter = 1,
    Event = 2,
    InComing = 3,
}

local _allHeroItems = {} -- heroId
local _gridView
local _heroFilterContent

function GetItemPrefab()
    return HeroItemPrefab
end

function GetItemCount()
    return #_allHeroItems
end

function OnItemShown(item, index)
    item:GetComponent(typeof(CS.App.UI.Hero.HeroItemBase)):RefreshUI(_allHeroItems[index], true)
end

local function SortHeroes(idA, idB)
    local configA = ConfigManager.Tables.HeroConfig[idA]
    local configB = ConfigManager.Tables.HeroConfig[idB]

    local isLockedA = HeroManager.Instance:IsHeroLocked(idA)
    local isLockedB = HeroManager.Instance:IsHeroLocked(idB)

    -- 1. 是否解锁：已解锁优先
    if isLockedA ~= isLockedB then
        return isLockedB -- 如果B锁定而A未锁定，A排前面
    end

    -- 2. 有章节解锁时，较小章节优先
    if isLockedA and isLockedB then
        local aUnlockLevel = configA.unlock_conditions[HeroUnlockCondition.PassChapter] or 0
        local bUnlockLevel = configB.unlock_conditions[HeroUnlockCondition.PassChapter] or 0
        if aUnlockLevel ~= bUnlockLevel then
            -- 都有章节解锁条件时，章节小的优先
            if aUnlockLevel > 0 and bUnlockLevel > 0 then
                return aUnlockLevel < bUnlockLevel
            end
            -- 有章节解锁条件的优先于无章节解锁条件的
            return aUnlockLevel > bUnlockLevel
        end
    end

    -- 3. 等级高的优先
    local foundA, levelA = CS.DB.Hero.Contents:TryGetValue(idA)
    levelA = foundA and levelA or 0

    local foundB, levelB = CS.DB.Hero.Contents:TryGetValue(idB)
    levelB = foundB and levelB or 0

    if levelA ~= levelB then
        return levelA > levelB
    end

    -- 4. Quality高的优先
    if configA.quality ~= configB.quality then
        return configA.quality > configB.quality
    end

    -- 5. id小的优先
    return idA < idB
end

local function CheckFilter(id)
    if _heroFilterContent and _heroFilterContent.HeroFilter then

        local _, qualities = _heroFilterContent.HeroFilter:TryGetValue(CS.App.UI.Hero.HeroFilterType.Quality)
        local _, attributes = _heroFilterContent.HeroFilter:TryGetValue(CS.App.UI.Hero.HeroFilterType.Attribute)

        if (qualities == nil or qualities.Count == 0) and (attributes == nil or attributes.Count == 0) then
            return true
        end

        local config = ConfigManager.Tables.HeroConfig[id]

        if qualities ~= nil and attributes ~= nil and qualities.Count > 0 and attributes.Count > 0 then
            return TableUtil.Contains(qualities, config.quality)
                and TableUtil.Contains(attributes, config.attack_attribute)
        end

        if qualities ~= nil and TableUtil.Contains(qualities, config.quality) then
            return true
        end

        if attributes ~= nil and TableUtil.Contains(attributes, config.attack_attribute) then
            return true
        end

        return false
    else
        return true
    end
end

local function SetItems()
    local heroConfig = ConfigManager.Tables.HeroConfig
    local heroDemonKingRef = CSConfigManager.Instance.HeroDemonKingRef

    _allHeroItems = {}
    
    _heroFilterContent:ClearResourceHeroes()
    local dbHero = CS.DB.Hero.Contents:Get()

    for k, v in pairs(heroConfig) do
        if v.lock_invisible ~= 1
            and v.unlock_conditions[HeroUnlockCondition.InComing] == nil
            and CheckFilter(k)
            and not heroDemonKingRef:HeroIsTransfered(k)
            and (not heroDemonKingRef:IsDemonKingHero(k)
            or heroDemonKingRef:HeroIsTransfered(heroDemonKingRef:GetOriginHeroIDByDemonKingHeroID(k))) then

            table.insert(_allHeroItems, k)
            local found, level = dbHero:TryGetValue(k)
            _heroFilterContent:AddResourceHero(k, found and level or 0)
        end
    end
end

local function RefreshItems(refreshData)
    if refreshData then
        SetItems()
    end

    if _heroFilterContent and _heroFilterContent.HeroSorting then
        local heroTable = CS.App.Config.ConfigManager.Instance:GetConfig(typeof(CS.HeroConfigTable))
        table.sort(_allHeroItems, function(a, b)
            local config_a = heroTable:GetRowData(a)
            local config_b = heroTable:GetRowData(b)
            return _heroFilterContent.HeroSorting(config_a, config_b) < 0
        end)
    else
        table.sort(_allHeroItems, SortHeroes)
    end

    _gridView:ReloadContent()
end

local function OnHeroUpdate(evt)
    if TableUtil.Contains(_allHeroItems, evt.HeroId) then
        RefreshItems(false)
    end
end

function Refresh()
    RefreshItems(true)
end 

function Awake()
    _gridView = self:GetComponent(typeof(CS.App.UI.Utility.UIGridView))
    _heroFilterContent = self:GetComponent(typeof(CS.App.UI.Hero.HeroFilterContentLua))
    _heroFilterContent.OnHeroSortingChanged = function()
        RefreshItems(false)
    end

    _heroFilterContent.OnHeroFilterChanged = function()
        RefreshItems(true)
    end

    _csEventSubscriber = CS.FQDev.EventSystem.EventSubscriber()
    _csEventSubscriber:Subscribe(typeof(CS.UserData.HeroUpdateEvent), OnHeroUpdate)
end 

function OnDestroy()
    if _csEventSubscriber then
        _csEventSubscriber:Dispose()
    end
end 