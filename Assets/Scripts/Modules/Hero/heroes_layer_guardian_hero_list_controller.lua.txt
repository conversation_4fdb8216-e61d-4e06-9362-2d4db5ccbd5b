local ConfigManager = require "config_manager"
local HeroManager = CS.App.Hero.HeroManager
local CSConfigManager = CS.App.Config.ConfigManager
local TableUtil = require "table_util"
local EventCenter = require "event_center"
local _csEventSubscriber
local _eventSubscriber = (require "event_subscriber").New()

local HeroUnlockCondition = {
    PassChapter = 1,
    Event = 2,
    InComing = 3,
}

local _allHeroItems = {} -- heroId
local _gridView
local _heroFilterContent

function GetItemPrefab()
    return HeroItemPrefab
end

function GetItemCount()
    return #_allHeroItems
end

function OnItemShown(item, index)
    item:GetComponent(typeof(CS.App.UI.Hero.HeroItemBase)):RefreshUI(_allHeroItems[index], true)
end

local HeroState = {
    Locked = 0,
    Unlocked = 1,
    Summoned = 2,
    CanGuardian = 3,
}

local function GetCurrentHeroState(heroId)
    local heroConfig = CSConfigManager.Instance:GetConfig(typeof(CS.HeroConfigTable)):GetRowData(heroId)
    local found, level = CS.DB.Hero.Contents:TryGetValue(heroId)
    level = found and level or 0

    if level >= 1 then
        if CS.App.Config.ConfigManager.Instance.GuardianRef:IsUnlockGuardian(heroId) then
            return HeroState.CanGuardian
        else
            return HeroState.Summoned
        end
    end

    if HeroManager.Instance:IsHeroLocked(heroConfig) and
        CS.App.Bag.BagManager.Instance:GetItemCount(heroConfig.LevelUpItemId) == 0 then
        return HeroState.Locked
    end

    return HeroState.Unlocked
end

-- 函数：计算交集
local function HasIntersection(t1, t2)
    if not t1 or not t2 then
        return false
    end

    local set = {}
    for _, v in ipairs(t1) do
        set[v] = true
    end

    for _, v in ipairs(t2) do
        if set[v] then
            return true
        end
    end

    return false
end

local function CheckFilter(id)
    if _heroFilterContent and _heroFilterContent.HeroFilter then

        local _, qualities = _heroFilterContent.HeroFilter:TryGetValue(CS.App.UI.Hero.HeroFilterType.Quality)
        local _, attributes = _heroFilterContent.HeroFilter:TryGetValue(CS.App.UI.Hero.HeroFilterType.Attribute)
        local _, camps = _heroFilterContent.HeroFilter:TryGetValue(CS.App.UI.Hero.HeroFilterType.Camp)

        local config = ConfigManager.Tables.HeroConfig[id]

        local qualityMatch = qualities == nil or qualities.Count == 0 or TableUtil.Contains(qualities, config.quality)
        local attributeMatch = attributes == nil or attributes.Count == 0 or TableUtil.Contains(attributes, config.attack_attribute)
        local campMatch = camps == nil or camps.Count == 0 or HasIntersection(camps, config.hero_camp)

        return qualityMatch and attributeMatch and campMatch
    else
        return true
    end
end

local function SortHeroes(idA, idB)
    local configA = ConfigManager.Tables.HeroConfig[idA]
    local configB = ConfigManager.Tables.HeroConfig[idB]

    local stateA = GetCurrentHeroState(idA)
    local stateB = GetCurrentHeroState(idB)

    -- 1. 按状态排序：Summoned > Unlocked > Locked
    if stateA ~= stateB then
        return stateA > stateB
    end

    -- 2. 有章节解锁时，较小章节优先
    if stateA == HeroState.Locked and stateB == HeroState.Locked then
        local aUnlockLevel = configA.unlock_conditions[HeroUnlockCondition.PassChapter] or 0
        local bUnlockLevel = configB.unlock_conditions[HeroUnlockCondition.PassChapter] or 0
        if aUnlockLevel ~= bUnlockLevel then
            -- 都有章节解锁条件时，章节小的优先
            if aUnlockLevel > 0 and bUnlockLevel > 0 then
                return aUnlockLevel < bUnlockLevel
            end
            -- 有章节解锁条件的优先于无章节解锁条件的
            return aUnlockLevel > bUnlockLevel
        end
    end

    -- 3. 等级高的优先
    local foundA, levelA = CS.DB.Hero.Contents:TryGetValue(idA)
    levelA = foundA and levelA or 0

    local foundB, levelB = CS.DB.Hero.Contents:TryGetValue(idB)
    levelB = foundB and levelB or 0

    if levelA ~= levelB then
        return levelA > levelB
    end

    -- 4. Quality高的优先
    if configA.quality ~= configB.quality then
        return configA.quality > configB.quality
    end

    -- 5. id小的优先
    return idA < idB
end

local function SetFilterContent()
    local heroConfig = ConfigManager.Tables.HeroConfig
    local heroDemonKingRef = CSConfigManager.Instance.HeroDemonKingRef
    local guardianRef = CSConfigManager.Instance.GuardianRef
    
    _heroFilterContent:ClearResourceHeroes()
    local dbHero = CS.DB.Hero.Contents:Get()

    for k, v in pairs(heroConfig) do
        if v.lock_invisible ~= 1
            and v.unlock_conditions[HeroUnlockCondition.InComing] == nil
            and guardianRef:IsGuardianHero(k)
            and not heroDemonKingRef:HeroIsTransfered(k)
            and (not heroDemonKingRef:IsDemonKingHero(k)
            or heroDemonKingRef:HeroIsTransfered(heroDemonKingRef:GetOriginHeroIDByDemonKingHeroID(k))) then
            
            local found, level = dbHero:TryGetValue(k)
            _heroFilterContent:AddResourceHero(k, found and level or 0)
        end
    end
end

local function SetItems()
    _allHeroItems = {}

    for k, v in pairs(_heroFilterContent.ResourceHeroes) do
        if CheckFilter(k) then
            table.insert(_allHeroItems, k)
        end
    end
end

local function RefreshItems(refreshData)
    if refreshData then
        SetFilterContent()
        SetItems()
    end

    if #_allHeroItems < 2 then
        return
    end

    if _heroFilterContent and _heroFilterContent.HeroSorting then
        local heroTable = CS.App.Config.ConfigManager.Instance:GetConfig(typeof(CS.HeroConfigTable))
        table.sort(_allHeroItems, function(a, b)
            local stateA = GetCurrentHeroState(a)
            local stateB = GetCurrentHeroState(b)
            -- 按状态排序：Summoned > Unlocked > Locked
            if stateA ~= stateB then
                return stateA > stateB
            end
            
            local config_a = heroTable:GetRowData(a)
            local config_b = heroTable:GetRowData(b)
            return _heroFilterContent.HeroSorting(config_a, config_b) < 0
        end)
    else
        table.sort(_allHeroItems, SortHeroes)
    end

    _gridView:ReloadContent()
end

local function OnHeroUpdate(evt)
    RefreshItems(true)
end

function Refresh()
    RefreshItems(true)
end

function Awake()
    _gridView = self:GetComponent(typeof(CS.App.UI.Utility.UIGridView))
    _heroFilterContent = self:GetComponent(typeof(CS.App.UI.Hero.HeroFilterContentLua))
    _heroFilterContent.OnHeroSortingChanged = function()
        RefreshItems(false)
    end

    _heroFilterContent.OnHeroFilterChanged = function()
        RefreshItems(true)
    end

    _csEventSubscriber = CS.FQDev.EventSystem.EventSubscriber()
    --_csEventSubscriber:Subscribe(typeof(CS.UserData.HeroUpdateEvent), OnHeroUpdate)
    _csEventSubscriber:Subscribe(typeof(CS.App.UI.Hero.HeroUpgradeEvent), OnHeroUpdate)
    _eventSubscriber:Subscribe(EventCenter.ID.Common_HeroTransform_Finished, OnHeroUpdate)
end

function OnDestroy()
    if _csEventSubscriber then
        _csEventSubscriber:Dispose()
    end
    _eventSubscriber:Dispose()
end 