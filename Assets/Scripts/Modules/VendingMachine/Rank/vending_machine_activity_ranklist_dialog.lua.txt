local _eventSubscriber = (require "event_subscriber").New()
local EventCenter = require "event_center"
local ActivityManager = require "vending_machine_manager"
local AvatarFrameManager = require "avatar_frame_manager"
local RankListManager = require "rank_list_manager"
local ConfigManager = require "config_manager"

local _rankListData = nil
local _rankType = ""
local _rankData = nil
local _rewardHasLoad = false
local _tabMenuItemList = {}
local _activityID = 0
local _activityRankID = 0

local function CloseUI()
    self:GetComponent(typeof(CS.LuaScript.LuaLayerContent)):Close()
end

local function SetActivityTime()
    --local activityData = ActivityManager.GetActivityData(_activityID)
    --local expireTime = activityData.expiredTime
    --if expireTime > 0 then
    --    local diff = os.difftime(expireTime, CS.FQDev.Time.TimeManager.ServerTimeUTC)
    --    if diff > 0 then
    --        RefreshTimer.transform:DOKill()
    --        RefreshTimer.transform:DOCounter(diff, 0, diff, function(v)
    --            RefreshTimer.text = CS.App.Extends.TimeFormat.FormatSecToDay(v)
    --        end):OnComplete(function()
    --            CloseUI()
    --        end)
    --    else
    --        CloseUI()
    --    end
    --else
    --    RefreshTimer.text = ""
    --end
end

local function LoadSelfRankItem()
    if _rankData then
        SelfRankItem:CallFunctionTable("SetData", 
        {
            rank = _rankData.selfRank or 0,
            extra = _rankData.extra or nil,
            playerData = 
            {
                name = CS.DB.UserInfo.Name, 
                avatar = AvatarFrameManager.GetCurrAvatarId(), 
                frame = AvatarFrameManager.GetCurrAvatarFrameId(), 
                weigh = _rankData.selfWeight or 0,
                maxRank = _rankListData.maxRank or 0,
            }, 
            rankType = _rankData.rankType,
            icon = _rankListData.icon,
            isSelf = true,
            activityID = _activityID,
            activityRankID = _activityRankID
        })
    else
        SelfRankItem:CallFunctionTable("SetData", 
        {
            rank = 0,
            extra = nil,
            playerData = 
            {
                name = CS.DB.UserInfo.Name, 
                avatar = AvatarFrameManager.GetCurrAvatarId(), 
                frame = AvatarFrameManager.GetCurrAvatarFrameId(), 
                weigh = _rankListData.localWeight,
                maxRank = _rankListData.maxRank or 0,
            }, 
            rankType = _rankListData.rankType,
            icon = _rankListData.icon,
            isSelf = true,
            activityID = _activityID,
            activityRankID = _activityRankID
        })
    end
end

local function LoadRankList()
    _rankData = RankListManager.GetRankDataByRankType(_rankType)
    LoadSelfRankItem()
    EmptyNode:SetActive(not _rankData or #_rankData.list <= 0)
    Scroll:CallFunctionTable("LoadRankItemList", {
        rankData = _rankData,
        startIndex = 1,
        icon = _rankListData.icon,
        activityID = _activityID,
        activityRankID = _activityRankID
    })
end

local function LoadRankReward()
    if not _rewardHasLoad then
        _rewardHasLoad = true
        local list = ActivityManager.GetRankRewardList(_activityRankID)
        for i = 1, #list do
            local go = CS.LuaScript.LuaAssetOperator.InstantiatePrefab(
                    "ui/vendingmachine",
                    "VendingMachineActivityRankRewardItem",
                    RewardNode)
            local item = go:GetComponent(typeof(CS.LuaScript.LuaListItem))
            item:CallFunctionTable("SetData", { data = list[i],
                                                activityID = _activityID,
                                                selfRank = _rankData.selfRank or 0})
        end
    end
end

local function OnClickRankList()
    Scroll.gameObject:SetActive(true)
    RewardScroll:SetActive(false)
    SelfRankItem.gameObject:SetActive(true)
    _tabMenuItemList[1].select:SetActive(true)
    _tabMenuItemList[2].select:SetActive(false)
    LoadRankList()
end

local function OnClickRewardList()
    Scroll.gameObject:SetActive(false)
    RewardScroll:SetActive(true)
    EmptyNode:SetActive(false)
    --SelfRankItem.gameObject:SetActive(false)
    _tabMenuItemList[1].select:SetActive(false)
    _tabMenuItemList[2].select:SetActive(true)
    LoadRankReward()
end

local function OnRefresh(rankType)
    if rankType ~= _rankType then
        return
    end
    LoadRankList()
    -- SetActivityTime()
end

local function OnClickShowTips()
    local lay = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
            CS.App.UI.LayerTag.Popup,
            "ui/vendingmachine/CommonWindowTipsLayer")

    local luaScript = lay:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    luaScript:CallFunctionTable("ShowTips",
            {
                content = CS.LanguageManager.Instance:GetLanguage("Vending_Ranking_Tips", "Vending_Ranking_Tips"),
                title = CS.LanguageManager.Instance:GetLanguage("Entrance_Rank", "Entrance_Rank"),
            })
end

local function InitTabMenuList()
    table.insert(_tabMenuItemList, {select = RankButton.transform:GetChild(0).gameObject})
    table.insert(_tabMenuItemList, {select = RewardButton.transform:GetChild(0).gameObject})
end

local function ReqRankList()
    RankListManager.ReqRankList(20, true, _rankType, 1)
end

function Initialize(data)
    _rankListData = data
    _rankType = data.rankType
    _activityID = data.activityID
    local activityCoinConfig = ConfigManager.Tables.ActivityCoinConfig[data.activityID]
    _activityRankID = activityCoinConfig.rank_id
    ReqRankList()
    OnRefresh(_rankType)
end

function Awake()
    InitTabMenuList()
    RankButton:AddClick(OnClickRankList)
    RewardButton:AddClick(OnClickRewardList)
    TipsButton:AddClick(OnClickShowTips)

    _eventSubscriber:Subscribe(EventCenter.ID.RankList_DateUpdate, OnRefresh)
end

function Start()

end

function OnEnable()

end

function OnDestroy()
    RefreshTimer.transform:DOKill()
    _eventSubscriber:Dispose()
end