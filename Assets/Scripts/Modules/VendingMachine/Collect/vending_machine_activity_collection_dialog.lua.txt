local _eventSubscriber = (require "event_subscriber").New()
local EventCenter = require "event_center"
local ConfigManager = require "config_manager"

local _activityID = 0
local _activityConfig

local function OnRefresh()
    TaskScroll.ScriptEnv.LoadTaskList(_activityID)
end

local function OpenCoinShowBox()
    local go = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
            CS.App.UI.LayerTag.Popup,
            "ui/vendingmachine/VendingMachineCoinShowBox")
    local luaScript = go:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    luaScript.ScriptEnv.SetUp(_activityID)
end

function OnClickShowTips()
    local lay = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
            CS.App.UI.LayerTag.Popup,
            "ui/vendingmachine/CommonWindowTipsLayer")

    local luaScript = lay:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    luaScript:CallFunctionTable("ShowTips",
            {
                content = CS.LanguageManager.Instance:GetLanguage("Vending_Ranking_Tips", "Vending_Ranking_Tips"),
                title = CS.LanguageManager.Instance:GetLanguage("Activity_Celebration_Tips3", "Activity_Celebration_Tips3"),
            })
end

function Initialize(activityID)
    _activityID = activityID
    _activityConfig = ConfigManager.Tables.ActivityCoinConfig[_activityID]
    coinItem.ScriptEnv.SetUp(_activityConfig.exchange_coin_id, _activityConfig.id)
    OnRefresh()
end

function Awake()
    coinShowButton:AddClick(OpenCoinShowBox)
    _eventSubscriber:Subscribe(EventCenter.ID.VendMachine_DataUpdated, OnRefresh)
end

function OnDestroy()
    _eventSubscriber:Dispose()
end