local ConfigManager = require "config_manager"
local RankListManager = require "rank_list_manager"
local CollectorCardActivityManager = require "collector_card_activity_manager"

local _rankData = nil
local _rankListData = nil

local function LoadRewardList(isSelf)
    if not RewardNode then
        return
    end
    local rewardList = CollectorCardActivityManager.GetRewardByRank(_rankData.rank) or {}
    local count = RewardNode.childCount
    for i = 1, count do
        local item = RewardNode:GetChild(i - 1).gameObject:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
        if i <= #rewardList then       
            item:CallFunctionTable("SetData", {reward = rewardList[i], isSelf = isSelf})
        end
        item.gameObject:SetActive(i <= #rewardList)
    end
end

local function ClearSpineFrame()
    if SpineFrameNode.childCount > 0 then
        for i = SpineFrameNode.childCount, 1, -1 do
            local go = SpineFrameNode:GetChild(i - 1).gameObject
            go:SetActive(false)
            CS.UnityEngine.Object.Destroy(go)
        end
    end
end

local function LoadItemBg(isSelf)
    if not ItemBg then
        return
    end
    
    if isSelf then
        ItemBg.sprite = CS.LuaScript.LuaAssetOperator.LoadAsset("ui/common", "goddess-day-pass-buy-2-box", typeof(CS.UnityEngine.Sprite))
        if ScoreBg then
            ScoreBg.sprite = CS.LuaScript.LuaAssetOperator.LoadAsset("ui/ranklist", "RankingTargetBox2", typeof(CS.UnityEngine.Sprite))
        end
    else
        ItemBg.sprite = CS.LuaScript.LuaAssetOperator.LoadAsset("ui/ranklist", "RankListBox", typeof(CS.UnityEngine.Sprite))
        if ScoreBg then
            ScoreBg.sprite = CS.LuaScript.LuaAssetOperator.LoadAsset("ui/ranklist", "RankingTargetBox1", typeof(CS.UnityEngine.Sprite))
        end
    end
end

local function LoadInfo()
    if not _rankListData then
        return
    end

    UINode:SetActive(true)
    local avatar = math.max(1, _rankData.playerData.avatar)
    local avatarConfig = ConfigManager.Tables.AvatarConfig[avatar]

    local frame = math.max(1, _rankData.playerData.frame)
    local frameConfig = ConfigManager.Tables.AvatarFrameConfig[frame]

    if avatarConfig then
        local spriteAtlas = CS.LuaScript.LuaAssetOperator.LoadAsset("ui/avataricon", "avataratlas", typeof(CS.UnityEngine.U2D.SpriteAtlas))
        Head.sprite = spriteAtlas:GetSprite(avatarConfig.icon)
    end

    if frameConfig then
        if frameConfig.anim then
            ClearSpineFrame()
            local go = CS.LuaScript.LuaAssetOperator.InstantiatePrefab(
                frameConfig.anim.AssetBundlePath,
                frameConfig.anim.AssetName,
                SpineFrameNode)

            local rect = go:GetComponent(typeof(CS.UnityEngine.RectTransform))
            rect.anchoredPosition = CS.UnityEngine.Vector2(0, 0)
            Frame.gameObject:SetActive(false)
            SpineFrameNode.gameObject:SetActive(true)
        else
            Frame.sprite = frameConfig.icon:Load(typeof(CS.UnityEngine.Sprite))
            Frame.gameObject:SetActive(true)
            SpineFrameNode.gameObject:SetActive(false)
        end    
    end

    if _rankData.rank <= 0 then
        Rank.text = CS.LanguageManager.Instance:GetLanguage("Rank_tips2", "Rank_tips2")
    else
        Rank.text = _rankData.rank
    end

    local isSelf = false
    if _rankData.isSelf then
        isSelf = _rankData.isSelf
    else
        isSelf = CS.DB.UserInfo.Name == _rankData.playerData.name
    end

    local weight = _rankData.playerData.weigh
    if isSelf then   
        if not _rankData.playerData.weigh or _rankData.playerData.weigh <= 0 then
            weight = MonopolyManager.GetMonopolyScore()
        end
    end
    Score.text = weight
    
    Name.text = _rankData.playerData.name
    LoadItemBg(isSelf)
    LoadRewardList()
    if EmptyNode then
        EmptyNode:SetActive(false)
    end
end

function SetEmpty()
    UINode:SetActive(false)
    EmptyNode:SetActive(true)
end


function SetData(data)
    _rankData = data
    _rankListData = RankListManager.GetRankDataByRankType(_rankData.rankType)
    LoadInfo()
end


function Awake()

end

