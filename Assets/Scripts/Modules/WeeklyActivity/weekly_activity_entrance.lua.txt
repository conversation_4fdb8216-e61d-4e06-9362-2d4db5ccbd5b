local WeeklyActivityManager = require "weekly_activity_manager"
local _eventSubscriber = (require "event_subscriber").New()
local EventCenter = require "event_center"
local ConfigManager = require "config_manager"
local TableUtil = require "table_util"

local _csEventSubscriber
local _entranceData = nil

local function SetRedDot()
    RedDot:SetActive(WeeklyActivityManager.CheckRedDot())
end

local function UpdateCoin(data)

end

local function SetActivityTime()
    local expireTime = WeeklyActivityManager.GetExpiredTime()
    if expireTime > 0 then
        local diff = os.difftime(expireTime, CS.FQDev.Time.TimeManager.ServerTimeUTC)
        TimerText.transform:DOKill()
        TimerText.transform:DOCounter(diff, 0, diff, function(v)
            TimerText.text = CS.App.Extends.TimeFormat.FormatSecToDay(v)
        end):OnComplete(function()
            Root:SetActive(false)
            WeeklyActivityManager.WeeklyGetData()
        end)
    else
        TimerText.text = ""
    end
end



local function Open()
    if not _entranceData.canCreate() then
        return
    end
    local go = CS.LuaScript.LuaAssetOperator.InstantiatePrefab(
                    "ui/weeklyactivity",
                    "WeeklyActivityDialog",
                    _entranceData.parent)

    local luaScript = go:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    luaScript:CallFunctionInt("Initialize", 1)

    if _entranceData.callBack then
        _entranceData.callBack(go)
    end
end

local function UpdateState()
    if WeeklyActivityManager.IsUnlock() then
        Root:SetActive(true)
        SetRedDot()
        SetActivityTime()
        --SetState()
    else
        Root:SetActive(false)
    end
end

function SetState(state)
    Select:SetActive(state)
end

function SetEntranceData(data)
    _entranceData = data
end

function Awake()
    Btn:AddClick(Open)
    _eventSubscriber:Subscribe(EventCenter.ID.BattingActivity_DataUpdated, UpdateState)
    _eventSubscriber:Subscribe(EventCenter.ID.BattingActivity_TaskDataUpdated, UpdateState)

    _csEventSubscriber = CS.FQDev.EventSystem.EventSubscriber()
    _csEventSubscriber:Subscribe(typeof(CS.App.Bag.ItemUpdatedEvent), UpdateCoin)
end

function OnEnable()
    UpdateState()
end

function OnDestroy()
    TimerText.transform:DOKill()
    _eventSubscriber:Dispose()
    _csEventSubscriber:Dispose()
end