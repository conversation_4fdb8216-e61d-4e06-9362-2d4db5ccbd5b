local ConfigManager = require "config_manager"
local _eventSubscriber = (require "event_subscriber").New()
local EventCenter = require "event_center"
local HeroManager = require "hero_manager"


local _csEventSubscriber
local _conditionItemList = {}
local _originHeroID = 0
local _heroDemonKingRef = CS.App.Config.ConfigManager.Instance.HeroDemonKingRef
local _heroGradeRef = CS.App.Config.ConfigManager.Instance.HeroGradeRef
local _enoughTransfer = false

local function CloseUI()
    self:GetComponent(typeof(CS.LuaScript.LuaLayerContent)):Close()
end

local function InitConditionItemList()
    if #_conditionItemList <= 0 then
        local count = ConditionNode.childCount
        for i = 1, count do
            local go = ConditionNode:GetChild(i - 1).gameObject
            local item = go:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
            table.insert(_conditionItemList, item)
        end
    end
end

local function LoadOriginHeroItem(originHeroID)
    local config = CS.App.Config.ConfigManager.Instance:GetConfig(typeof(CS.HeroConfigTable)):GetRowData(originHeroID)
    local found, level = CS.DB.Hero.Contents:TryGetValue(originHeroID)
    OriginHeroItem:FreshUI(config.Id, level, config, function()
        return false
    end, function()
        return false
    end)
    OriginHeroItem.DisableButton = true
    OriginHeroItem:SetButtonAction(1)
    OriginHeroName.text = CS.LanguageManager.Instance:GetLanguage(config.Name, config.Name)
end

local function LoadNewHeroItem(newHeroID)
    local config = CS.App.Config.ConfigManager.Instance:GetConfig(typeof(CS.HeroConfigTable)):GetRowData(newHeroID)
    local level = config.StartLevel
    NewHeroItem:FreshUI(config.Id, level, config, function()
        return false
    end, function()
        return false
    end)
    NewHeroItem.DisableButton = true
    NewHeroItem:SetButtonAction(1)
    NewHeroName.text = CS.LanguageManager.Instance:GetLanguage(config.Name, config.Name)
    --local des = config.Name.."Description"
    --NewHeroDes.text = CS.LanguageManager.Instance:GetLanguage(des, des)
end

local function LoadCondition()
    local found, level = CS.DB.Hero.Contents:TryGetValue(_originHeroID)
    local demonKingConfig = _heroDemonKingRef:GetDemonKingConfigByOriginHeroID(_originHeroID)
    local needLevel = _heroDemonKingRef:GetDemonKingHeroNeedLevelByOriginHeroID(_originHeroID)
    _enoughTransfer = true
    local levelConditionItem = _conditionItemList[1]
    local levelLang = CS.LanguageManager.Instance:GetLanguage("hero_level_text", "hero_level_text")
    local progress = ""
    local currentLevelString = CS.System.String.Format(levelLang, level)
    local needLevelString = CS.System.String.Format(levelLang, needLevel)
    if level < needLevel then
        progress = "<color=#fa2a2a>%s</color>/%s"
    else
        progress = "<color=#26d518>%s</color>/%s"
    end

    local progressText = string.format(progress, currentLevelString, needLevelString)
    levelConditionItem:CallFunctionTable("SetData",
        {
            icon = _heroGradeRef:GetHeroSpriteByLevel(_originHeroID, level),
            progress = progressText,
            finish = level >= needLevel,
        })
    if level < needLevel then
        _enoughTransfer = false
    end
    local index = 2
    for k,v in pairs(demonKingConfig.Cost) do
        local itemConfig = ConfigManager.Tables.ItemConfig[k]
        local itemCount = CS.App.Bag.BagManager.Instance:GetItemCount(k)
        local condition = ""
        if itemCount < v then
            condition = "<color=#fa2a2a>%d</color>/%d"
        else
            condition = "<color=#26d518>%d</color>/%d"
        end
        local conditionText = string.format(condition, itemCount, v)
        _conditionItemList[index]:CallFunctionTable("SetData",
            {
                icon = itemConfig.icon:Load(typeof(CS.UnityEngine.Sprite)),
                progress = conditionText,
                finish = itemCount >= v,
            })
        if itemCount < v then
            _enoughTransfer = false
        end
        index = index + 1
    end

    if _enoughTransfer then
        ConfirmButtonGray.Amount = 0
        ConfirmButton.interactable = true
    else
        ConfirmButtonGray.Amount = 1
        ConfirmButton.interactable = false
    end

    LoadNewHeroItem(demonKingConfig.DemonHeroId)
end

local function SendHeroTransferLogEvent()
    local constConfig = ConfigManager.Tables.ConstConfig
    CS.LogEvent.I:Track("Demon_King_Click")
      :IsNew()
      :AddMaxChapter()
      :Add("Role_Id", _originHeroID)
      :Add("Demon_Coin_Pre", CS.App.Bag.BagManager.Instance:GetItemCount(constConfig.DemonKingTransferItemID))
      :Send()
end

local function OnClickConfirm()
    local demonKingConfig = _heroDemonKingRef:GetDemonKingConfigByOriginHeroID(_originHeroID)
    SendHeroTransferLogEvent()
    HeroManager.HeroTransferDemonKing(demonKingConfig.Id)
    CloseUI()
end

local function OnClickDetails()
    local demonKingConfig = _heroDemonKingRef:GetDemonKingConfigByOriginHeroID(_originHeroID)
    local config = CS.App.Config.ConfigManager.Instance:GetConfig(typeof(CS.HeroConfigTable)):GetRowData(demonKingConfig.DemonHeroId)
    local lay = CS.App.UI.Hero.HeroDetailLayer.Create()
    lay:SetData(demonKingConfig.DemonHeroId, config.StartLevel)
end

function Initialize(id)
    InitConditionItemList()
    _originHeroID = id
    LoadCondition()
    LoadOriginHeroItem(_originHeroID)
end

function Awake()
    ConfirmButton:AddClick(OnClickConfirm)
    DetailsButton:AddClick(OnClickDetails)
    _csEventSubscriber = CS.FQDev.EventSystem.EventSubscriber()
    --_csEventSubscriber:Subscribe(typeof(CS.App.Activity.ActivityChapter.ActivityChapterDataRefreshedEvent), RefreshChapterEntrance)
end


function OnDestroy()
    _eventSubscriber:Dispose()
    _csEventSubscriber:Dispose()
end