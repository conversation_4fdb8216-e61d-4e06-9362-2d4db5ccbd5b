local ConfigManager = require "config_manager"
local _eventSubscriber = (require "event_subscriber").New()
local EventCenter = require "event_center"
local LightingActivityManager = require "lighting_activity_manager"
local TableUtil = require "table_util"
local DOTween = CS.DG.Tweening.DOTween
local StringModifier = require "string_modifier"

local _activityID = 0
local _csEventSubscriber
local _onSkip = false
local _lastCurrentBallCount = 0
local _lastRestBallCount = 0
local _sequence = nil
local _lightingMax = false

local _ballPosList = 
{
    [1] = CS.UnityEngine.Vector2(-271, 159),
    [2] = CS.UnityEngine.Vector2(-171, 115),
    [3] = CS.UnityEngine.Vector2(-18, 82),
    [4] = CS.UnityEngine.Vector2(146, 105),
    [5] = CS.UnityEngine.Vector2(274, 151),
    [6] = CS.UnityEngine.Vector2(-146, 419),
    [7] = CS.UnityEngine.Vector2(-50, 391),
    [8] = CS.UnityEngine.Vector2(69, 389),
    [9] = CS.UnityEngine.Vector2(159, 418),
    [10] = CS.UnityEngine.Vector2(12, 629),
}
local _currentBallPosIndexList = {}
local _ballItemList = {}

local function CloseUI()
    self:GetComponent(typeof(CS.LuaScript.LuaLayerContent)):Close()
end

local function UpdateBuyRedDot()
    BuyRedDot:SetActive(LightingActivityManager.CheckBuyRedDot(_activityID))
end

local function UpdateTaskRedDot()
    TaskRedDot:SetActive(LightingActivityManager.CheckTaskRedDot(_activityID))
end
local function UpdateExchangeRedDot()
    ExchangeRedDot:SetActive(LightingActivityManager.CheckExchangeRedDot(_activityID))
end
local function UpdateChapterRedDot()
    ChapterRedDot:SetActive(LightingActivityManager.CheckTabMenuRedDot(_activityID, 2))
end
local function UpdateScoreRedDot()
    ScoreRedDot:SetActive(LightingActivityManager.CheckLightingScoreTaskRedDot(_activityID))
end

local function UpdateRedDot()
    UpdateBuyRedDot()
    UpdateTaskRedDot()
    UpdateExchangeRedDot()
    UpdateChapterRedDot()
    UpdateScoreRedDot()
end

local function UpdateBuyButton()
    BuyButton.gameObject:SetActive(LightingActivityManager.CheckBuyButtonActive(_activityID))
end


local function SetActivityTime()
    local lightingData = LightingActivityManager.GetLightingActivityData(_activityID)
    local expireTime = lightingData.expiredTime
    if expireTime > 0 then
        local diff = os.difftime(expireTime, CS.FQDev.Time.TimeManager.ServerTimeUTC)
        if diff > 0 then
            ExpiredTime.transform:DOKill()
            ExpiredTime.transform:DOCounter(diff, 0, diff, function(v)
                ExpiredTime.text = CS.App.Extends.TimeFormat.FormatSecToDay(v)
            end):OnComplete(function()
                ExpiredTime.text = ""
            end)
        else
            ExpiredTime.text = ""
        end
    else
        ExpiredTime.text = ""
    end
end

local function LoadBaseInfo()
    local cost,_ = LightingActivityManager.GetCurrentLightingCost(_activityID)
    if cost then
        local awardConfig = TableUtil.Find(ConfigManager.Tables.ActivityLightingAwardConfig, function(config) 
            return config.activity_id ==  _activityID end)
        local maxCostCount = awardConfig.section_count 
        local itemConfig = ConfigManager.Tables.ItemConfig[cost.Id]
        CostIcon.sprite = itemConfig.icon:Load(typeof(CS.UnityEngine.Sprite))
        CostCountIcon.sprite = CostIcon.sprite
        local count = CS.App.Bag.BagManager.Instance:GetItemCount(cost.Id)
        Cost.text = CS.App.UI.StringUtils.FormatCurrency(count)
        CostCount.text = string.format("x%d", count < maxCostCount and count or maxCostCount)
        if math.floor(count / cost.Count) <= 0 then
            LightingButtonGray.Amount = 1
            LightingButton.interactable = false
        else
            LightingButtonGray.Amount = 0
            LightingButton.interactable = true
        end
    end
end

local function LoadScoreTask()
    if not self.gameObject.activeInHierarchy then
        return
    end
    Scroll:CallFunctionInt("LoadTaskList", _activityID)
end

local function UpdateSkipToggle()
    SkipCheck:SetActive(_onSkip)
end

local function OnClickLighting()
    local cost,maxCount = LightingActivityManager.GetCurrentLightingCost(_activityID)
    if not cost then
        return
    end

    local count = CS.App.Bag.BagManager.Instance:GetItemCount(cost.Id)
    if count < cost.Count then
        CS.App.UI.MessageTip.Create("CM_LightBar_Lack")
        return
    end

    local lightingCount = math.clamp(math.floor(count / cost.Count), 1, maxCount)
    LightingActivityManager.ActivityLightingClick(_activityID, lightingCount, nil, nil, _onSkip)
    CS.AudioManager.Instance:PlaySound("anniversary_2_lighting")
end

local function GetSkinByNum(num)
    return CS.LuaScript.LuaAssetOperator.LoadAsset("ui/lightingactivity", "Pattern"..num, typeof(CS.UnityEngine.Sprite))
end

local function OnClickOpenRankListDialog()
    local activityConfig = LightingActivityManager.GetLightingActivityConfig(_activityID)
    local go = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
        CS.App.UI.LayerTag.Dialog,
        "ui/ranklist/RankListMainRankLayer")

    local luaScript = go:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    luaScript:CallFunctionTable("Initialize", {key = activityConfig.ranking})
end

local function OnClickSkip()
    _onSkip = not _onSkip
    local v = 0
    if _onSkip then
        v = 1
    end
    CS.UnityEngine.PlayerPrefs.SetInt("LightingActivity_".._activityID, v)
    UpdateSkipToggle()
end

local function LoadFinalLightingTree(force)
    LoadScoreTask()
    if not _onSkip and not force then
        local settleData = LightingActivityManager.GetLightingSettleData(_activityID)
        if settleData then
            local lay = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
                    CS.App.UI.LayerTag.Popup,
                    "ui/lightingactivity/LightingActivitySettleDialog")

                    local luaScript = lay:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
                    luaScript:CallFunctionTable("SetData", LightingActivityManager.GetLightingSettleData(_activityID))
        end
    end
    if _lightingMax then
        return
    end

    _lightingMax = true
    for i = 1, #_ballItemList do
        local item = _ballItemList[i]
        item.active:SetActive(true)
    end
    StarSpine.AnimationState:SetAnimation(0, "stop", true)
end

local function LoadTreeBall(force, skip)
    local lightingCount = LightingActivityManager.GetLightingCountScore(_activityID)
    local taskList = LightingActivityManager.GetScoreTaskList(_activityID)
    if lightingCount >= taskList[#taskList].box_level_id then--所有进度奖励已点亮完毕
        LoadFinalLightingTree(force)
        return
    end

    if not force then
        return
    end

    _lastCurrentBallCount = lightingCount % 10
    _lastRestBallCount = 10 - _lastCurrentBallCount
    _currentBallPosIndexList = {}

    if not skip then
        if _sequence then
            _sequence:Kill()
        end
        _sequence = DOTween.Sequence()
        Mask:SetActive(true)
        MeteorVFX.gameObject:SetActive(true)
        VFXEndPos.position = ScoreStarRect.position
        MeteorVFX.anchoredPosition = CS.UnityEngine.Vector2(0, 107)
        _sequence:AppendInterval(0.5)
        _sequence:AppendCallback(function()
            StarSpine.AnimationState:SetAnimation(0, "stop", true)
        end)
        _sequence:Append(MeteorVFX:DOLocalMove(VFXEndPos.localPosition, 1):SetEase(CS.DG.Tweening.Ease.OutCirc))
        _sequence:AppendCallback(function()
            CS.AudioManager.Instance:PlaySound("anniversary_2_reward")
            BoomVFX.gameObject:SetActive(true)
            StarSpine.AnimationState:SetAnimation(0, "loop", true)
            for i = 1, #_ballItemList do
                _ballItemList[i].active:SetActive(false)
            end
        end)
        _sequence:AppendInterval(0.5)
    else
        for i = 1, #_ballItemList do
            _ballItemList[i].active:SetActive(false)
        end
        if _lastCurrentBallCount >= _lastRestBallCount then
            StarSpine.AnimationState:SetAnimation(0, "loop", true)
        else
            StarSpine.AnimationState:SetAnimation(0, "stop", true)
        end
    end

    local ballList = {}
    if not skip then
        _sequence:AppendCallback(function()
            for i = _lastCurrentBallCount + 1, #_ballItemList do
                _ballItemList[i].active:SetActive(true)
            end
        end)
    else
        for i = 1, #_ballItemList do
            _ballItemList[i].active:SetActive(i <= _lastCurrentBallCount)
        end
    end

    if not skip then
        _sequence:AppendCallback(function()
            LoadScoreTask()
            BoomVFX.gameObject:SetActive(false)
            MeteorVFX.gameObject:SetActive(false)
            for i = 1, #_ballItemList do
                _ballItemList[i].active:SetActive(false)
            end
        end)
        _sequence:AppendInterval(0.5)
        for i = 1, #_ballItemList do
            if i <= _lastCurrentBallCount then
                _sequence:AppendCallback(function()
                    _ballItemList[i].active:SetActive(true)
                end)
                _sequence:AppendInterval(0.2)
            end
        end
        _sequence:AppendInterval(0.2)
        _sequence:AppendCallback(function()
            for i = 1, #_ballItemList do
                _ballItemList[i].active:SetActive(i <= _lastCurrentBallCount)
            end
            Mask:SetActive(false)

            local lay = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
                CS.App.UI.LayerTag.Popup,
                "ui/lightingactivity/LightingActivitySettleDialog")

            local luaScript = lay:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
            luaScript:CallFunctionTable("SetData", LightingActivityManager.GetLightingSettleData(_activityID))
        end)
    end
end

local function AddTreeBall()
    local lightingCount = LightingActivityManager.GetLightingCountScore(_activityID)
    local taskList = LightingActivityManager.GetScoreTaskList(_activityID)
    if lightingCount >= taskList[#taskList].box_level_id then--所有进度奖励已点亮完毕
        LoadFinalLightingTree()
        return
    end

    local currentBallCount = lightingCount % 10
    local restCount = 10 - currentBallCount
    if not _onSkip then
        if _sequence then
            _sequence:Kill()
        end
        _sequence = DOTween.Sequence()
        Mask:SetActive(true)
    end

    local count = currentBallCount - _lastCurrentBallCount
    if _lastCurrentBallCount >= currentBallCount then
        count = _lastRestBallCount
    end

    if not _onSkip then
        _sequence:AppendCallback(function()

        end)
        _sequence:AppendInterval(1)
        for i = _lastCurrentBallCount + 1, _lastCurrentBallCount + count do
            _sequence:AppendCallback(function()
                _ballItemList[i].active:SetActive(true)
            end)
            _sequence:AppendInterval(0.2)
        end
        _sequence:AppendCallback(function()
            Mask:SetActive(false)
            if _lastCurrentBallCount >= currentBallCount then
                LoadTreeBall(true, _onSkip)
                return
            end

            local lay = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
            CS.App.UI.LayerTag.Popup,
            "ui/lightingactivity/LightingActivitySettleDialog")

            local luaScript = lay:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
            luaScript:CallFunctionTable("SetData", LightingActivityManager.GetLightingSettleData(_activityID))

            if currentBallCount >= restCount then
                StarSpine.AnimationState:SetAnimation(0, "loop", true)
            else
                StarSpine.AnimationState:SetAnimation(0, "stop", true)
            end
            _lastCurrentBallCount = currentBallCount
            _lastRestBallCount = restCount
            LoadScoreTask()
        end)
    else
        if _lastCurrentBallCount >= currentBallCount then
            LoadTreeBall(true, _onSkip)
            return
        end

        if currentBallCount >= restCount then
            StarSpine.AnimationState:SetAnimation(0, "loop", true)
        else
            StarSpine.AnimationState:SetAnimation(0, "stop", true)
        end
        _lastCurrentBallCount = currentBallCount
        _lastRestBallCount = restCount
        LoadScoreTask()
    end
end

local function OnLightingEnd(activityID)
    if _activityID ~= activityID then
        return
    end

    AddTreeBall()
end

local function OnClickShowTips()
    local lay = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
            CS.App.UI.LayerTag.Popup,
            "ui/lightingactivity/CommonWindowTipsLayer")

    local luaScript = lay:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    luaScript:CallFunctionTable("ShowTips",
            {
                content = CS.LanguageManager.Instance:GetLanguage("Activity_Lighting_Info", "Activity_Lighting_Info"),
                title = CS.LanguageManager.Instance:GetLanguage("Lighting_ActivityName60008", "Lighting_ActivityName60008"),
                tips = ""
            })
end

local function OnClickBuyCount()
    local go = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
            CS.App.UI.LayerTag.Popup,
            "ui/lightingactivity/LightingSetCountDialog")
    
    local luaScript = go:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    
    local exchangeConfig = LightingActivityManager.GetLightingExchangeConfig(_activityID)
    local cost = StringModifier.Split(exchangeConfig.cost, ":")
    local lastCount = math.max(0, math.min(exchangeConfig.buy_num, exchangeConfig.limit - LightingActivityManager.GetBuyCollectorCount(_activityID)))
    luaScript:CallFunctionTable("SetData", 
    {
        title = CS.LanguageManager.Instance:GetLanguage("ConfirmLayer_title", "ConfirmLayer_title"),
        coinID = tonumber(cost[1]),
        coinCount = tonumber(cost[2]),
        lastCount = lastCount,
        reward = CS.RewardItem.Parse("item:"..exchangeConfig.exchange),
        tips = "",
        callBack = function(count)
            LightingActivityManager.ActivityItemExchange(exchangeConfig.id, count)
        end
    })
end

local function OnClickShowScoreTask()
    local lay = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
            CS.App.UI.LayerTag.Dialog,
            "ui/lightingactivity/LightingActivityLightingScoreTaskDialog")

    local luaScript = lay:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    luaScript:CallFunctionInt("Initialize", _activityID)
end

local function OnClickShowRechargeTask()
    local lay = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
        CS.App.UI.LayerTag.Dialog,
        "ui/lightingactivity/LightingActivitySpecialTaskDialog")

    local luaScript = lay:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    luaScript:CallFunctionInt("Initialize", _activityID)
end

local function OnClickShowGiftDialog()
    local lay = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
            CS.App.UI.LayerTag.Dialog,
            "ui/lightingactivity/LightingActivityGiftDialog")

    local luaScript = lay:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    luaScript:CallFunctionInt("Initialize", _activityID)
end

local function OnClickShowTaskDialog()
    local lay = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
            CS.App.UI.LayerTag.Dialog,
            "ui/lightingactivity/LightingActivityTaskDialog4")

    local luaScript = lay:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    luaScript:CallFunctionInt("Initialize", _activityID)
end

local function OnClickShowChapterDialog()
    local lay = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
            CS.App.UI.LayerTag.Dialog,
            "ui/lightingactivity/LightingActivityChapterDialog2")

    local luaScript = lay:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    luaScript:CallFunctionInt("Initialize", _activityID)
end

local function OnClickShowExchangeDialog()
    local lay = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
            CS.App.UI.LayerTag.Dialog,
            "ui/lightingactivity/LightingActivityExchangeDialog4")

    local luaScript = lay:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    luaScript:CallFunctionInt("Initialize", _activityID)
end

local function UpdateCoin(data)
    local cost,_ = LightingActivityManager.GetCurrentLightingCost(_activityID)
    if data and data.Id == cost.Id then
        LoadBaseInfo()
    end
end

local function OnRefresh(activityID)
    if _activityID ~= activityID then
        return
    end
    SetActivityTime()
    UpdateSkipToggle()
    if _onSkip then
        LoadScoreTask()
    end
    UpdateRedDot()
    UpdateBuyButton()
end

local function InitBallList()
    local count = BallNode.childCount
    for i = 1, count do
        local tr = BallNode:GetChild(i - 1)
        local active = tr:Find("Active").gameObject
        table.insert(_ballItemList, {go = tr.gameObject, active = active})
    end

    table.insert(_ballItemList, {go = FinalBall, active = FinalBall.transform:Find("Active").gameObject})
end

local function OnClickShowGiftDialog()
    local lay = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
        CS.App.UI.LayerTag.Dialog,
        "ui/lightingactivity/LightingActivityGiftDialog")

    local luaScript = lay:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    luaScript:CallFunctionInt("Initialize", _activityID)
end

local function InitGiftEntrance()
    local activityConfig = LightingActivityManager.GetLightingActivityConfig(_activityID)
    GiftScript:CallFunctionInt("SetActivityId", activityConfig.gift_activity_id)
end

function Initialize(activityID)
    _activityID = activityID
    _onSkip = CS.UnityEngine.PlayerPrefs.GetInt("LightingActivity_"..activityID, 0) > 0
    InitGiftEntrance()
    OnRefresh(activityID)
    LoadTreeBall(true, true)
    LoadBaseInfo()
    LoadScoreTask()
end

function UnInitialize()
    ExpiredTime.transform:DOKill()
    _eventSubscriber:Dispose()
    _csEventSubscriber:Dispose()
end

function Awake()
    InitBallList()
    SkipButton:AddClick(OnClickSkip)
    LightingButton:AddClick(OnClickLighting)
    RankButton:AddClick(OnClickOpenRankListDialog)
    TipsButton:AddClick(OnClickShowTips)
    BuyButton:AddClick(OnClickBuyCount)
    ScoreButton:AddClick(OnClickShowScoreTask)
    RechargeButton:AddClick(OnClickShowRechargeTask)
    TaskButton:AddClick(OnClickShowTaskDialog)
    ChapterButton:AddClick(OnClickShowChapterDialog)
    ExchangeButton:AddClick(OnClickShowExchangeDialog)
    --GiftButton:AddClick(OnClickShowGiftDialog)
    _csEventSubscriber = CS.FQDev.EventSystem.EventSubscriber()
    _eventSubscriber:Subscribe(EventCenter.ID.LightingActivity_DataUpdated, OnRefresh)
    _eventSubscriber:Subscribe(EventCenter.ID.LightingActivity_LightingEnd, OnLightingEnd)
    _eventSubscriber:Subscribe(EventCenter.ID.LightingActivity_ScoreTaskUpdated, LoadScoreTask)
    _csEventSubscriber = CS.FQDev.EventSystem.EventSubscriber()
    _csEventSubscriber:Subscribe(typeof(CS.App.Bag.ItemUpdatedEvent), UpdateCoin)
end

function OnEnable()
    if _onSkip then
        LoadScoreTask()
    end
end

function OnDestroy()
    if _sequence then
        _sequence:Kill()
    end
    ExpiredTime.transform:DOKill()
    _eventSubscriber:Dispose()
    _csEventSubscriber:Dispose()
end