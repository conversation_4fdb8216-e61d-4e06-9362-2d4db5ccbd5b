local ConfigManager = require "config_manager"
local _eventSubscriber = (require "event_subscriber").New()
local EventCenter = require "event_center"
local LightingActivityManager = require "lighting_activity_manager"
local StringModifier = require "string_modifier"

local _activityID = 0
local _csEventSubscriber
local _houseItemList = {}
local _hasInit = false
local _currentChapter = 0
local _currentChapterIndex = 0

local function CloseUI()
    self:GetComponent(typeof(CS.LuaScript.LuaLayerContent)):Close()
end


local function SetActivityTime()
    local lightingData = LightingActivityManager.GetLightingActivityData(_activityID)
    local expireTime = lightingData.expiredTime
    if expireTime > 0 then
        local diff = os.difftime(expireTime, CS.FQDev.Time.TimeManager.ServerTimeUTC)
        if diff > 0 then
            ExpiredTime.transform:DOKill()
            ExpiredTime.transform:DOCounter(diff, 0, diff, function(v)
                ExpiredTime.text = CS.App.Extends.TimeFormat.FormatSecToDay(v)
            end):OnComplete(function()
                ExpiredTime.text = ""
            end)
        else
            ExpiredTime.text = ""
        end
    else
        ExpiredTime.text = ""
    end
end

local function LoadCoinList()
    local cost,maxCount = LightingActivityManager.GetCurrentLightingCost(_activityID)

    local exchangeList = LightingActivityManager.GetExchangeList(_activityID)
    local id = 0
    for k,v in pairs(exchangeList[1].cost) do
        id = k
        break
    end

    local coinList = {cost.Id, id}
    local count = CoinNode.childCount
    for i = 1, count do
        local go = CoinNode:GetChild(i - 1).gameObject
        if i <= #coinList then
            local item = go:GetComponent(typeof(CS.LuaScript.LuaBehaviour))  
            item:CallFunctionInt("Initialize", coinList[i])
        end
        go:SetActive(i <= #coinList)
    end
end

local function UpdateExchangeCoin(data)
    if data then
        local id = 0
        local cost,maxCount = LightingActivityManager.GetCurrentLightingCost(_activityID)
        local exchangeList = LightingActivityManager.GetExchangeList(_activityID)
        for k,v in pairs(exchangeList[1].cost) do
            id = k
            break
        end

        if id == data.Id or cost.Id == data.Id then
            LoadCoinList()
            return
        end
    end
end


local function LoadFinalReward()
    local state = LightingActivityManager.ChapterFinalRewardState(_activityID)
    FinalRewardVFX:SetActive(state == LightingActivityManager.TaskState.Complete)
    if state == LightingActivityManager.TaskState.InComplete or state == LightingActivityManager.TaskState.Complete then
        FinalRewardImage.sprite = CS.LuaScript.LuaAssetOperator.LoadAsset("ui/lightingactivity", "ChristmasGiftBoxClose", typeof(CS.UnityEngine.Sprite))
    else
        FinalRewardImage.sprite = CS.LuaScript.LuaAssetOperator.LoadAsset("ui/lightingactivity", "ChristmasGiftBoxopen", typeof(CS.UnityEngine.Sprite))
    end
end

local function OnSelectChapter(index)
    if _currentChapterIndex == index then
        return
    end

    if _currentChapterIndex > 0 then
        _houseItemList[_currentChapterIndex].ScriptEnv.SetState(false)
    end

    _currentChapterIndex = index
    local activityConfig = LightingActivityManager.GetLightingActivityConfig(_activityID)
    _currentChapter = activityConfig.chapter_id[_currentChapterIndex]
    _houseItemList[_currentChapterIndex].ScriptEnv.SetState(true)

    local finish = LightingActivityManager.CheckChapterFinish(_activityID, _currentChapterIndex)
    StartButton.interactable = not finish
    StartButton:GetComponent(typeof(CS.App.UI.Utility.UIGrayscaleImage)).Amount = finish and 1 or 0
end

local function LoadChapter()
    local activityConfig = LightingActivityManager.GetLightingActivityConfig(_activityID)
    for i = 1, #_houseItemList do
        local item = _houseItemList[i]
        if i <= #activityConfig.chapter_id then
            local item = _houseItemList[i]
            item:CallFunctionTable("SetData", 
            {   id = activityConfig.chapter_id[i], 
                index = i, activityID = _activityID, 
                callBack = function(index)
                    OnSelectChapter(index)
                end
            })
        end
        item.gameObject:SetActive(i <= #activityConfig.chapter_id)
    end
end

local function LoadChapterProgress()
    local activityConfig = LightingActivityManager.GetLightingActivityConfig(_activityID)
    local progress = LightingActivityManager.GetChapterProgress(_activityID)
    --FinalProgress:SetActive(progress >= #activityConfig.chapter_id)
    if progress <= #activityConfig.chapter_id then
        if progress > 0 then
            local go = LineNode:GetChild(progress - 1)
            local rect = go.gameObject:GetComponent(typeof(CS.UnityEngine.RectTransform))
            ProgressRect.sizeDelta = CS.UnityEngine.Vector2(ProgressRect.sizeDelta.x, rect.anchoredPosition.y)
        else
            ProgressRect.sizeDelta = CS.UnityEngine.Vector2(ProgressRect.sizeDelta.x, 0)
        end
    end

    LoadFinalReward()
end

local function OnRefresh(activityID)
    if _activityID ~= activityID then
        return
    end

    SetActivityTime()
    LoadChapterProgress()
    LoadChapter()
    LoadCoinList()
    if _currentChapterIndex > 0 then
        _houseItemList[_currentChapterIndex].ScriptEnv.SetState(true)
        local finish = LightingActivityManager.CheckChapterFinish(_activityID, _currentChapterIndex)
        StartButton.interactable = not finish
        StartButton:GetComponent(typeof(CS.App.UI.Utility.UIGrayscaleImage)).Amount = finish and 1 or 0
    end
end

local function InitHouseItem()
    if _hasInit then
        return
    end

    _hasInit = true
    _houseItemList = {}
    local count = HouseNode.childCount
    for i = count, 1, -1 do
        local go = HouseNode:GetChild(i - 1)
        local luaItem = go.gameObject:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
        table.insert(_houseItemList, luaItem)
    end
end

local function OnClickShowChapterDetail()
    if _currentChapterIndex <= 0 then
        return
    end
    local layer = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
            CS.App.UI.LayerTag.Dialog,
            "ui/lightingactivity/LightingActivityChapterDetailDialog2")

    local luaScript = layer:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    luaScript:CallFunctionTable("Initialize", {activityID = _activityID, chapter = _currentChapter, index = _currentChapterIndex})
end

local function OnClickShowTips()
    local lay = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
            CS.App.UI.LayerTag.Popup,
            "ui/lightingactivity/CommonWindowTipsLayer")

    local luaScript = lay:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    luaScript:CallFunctionTable("ShowTips",
            {
                content = CS.LanguageManager.Instance:GetLanguage("Activity_Lighting_Level_Info", "Activity_Lighting_Level_Info"),
                title = CS.LanguageManager.Instance:GetLanguage("Lighting_ActivityName60008", "Lighting_ActivityName60008"),
                tips = ""
            })
end

local function OnClickClaimFinalReward()
    local activityConfig = LightingActivityManager.GetLightingActivityConfig(_activityID)
    local state = LightingActivityManager.ChapterFinalRewardState(_activityID)
    if state == LightingActivityManager.TaskState.Complete then
        LightingActivityManager.ActivityLightingChapterReward(_activityID)
    elseif state == LightingActivityManager.TaskState.InComplete then
        local lay = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
            CS.App.UI.LayerTag.Popup,
            "ui/common/CommonBoxRewardDetailDialog")

        local luaScript = lay:GetComponent(typeof(CS.LuaScript.LuaBehaviour))     
        luaScript:CallFunctionTable("SetData", 
        {
            title = CS.LanguageManager.Instance:GetLanguage("Cumulative_Recharge_Final_Tips", "Cumulative_Recharge_Final_Tips"),
            tips = "",
            itemList = activityConfig.final_rewards,
        })
    end
end

function Initialize(activityID)
    _activityID = activityID
    OnRefresh(activityID)
    OnSelectChapter(LightingActivityManager.GetDefaultChapterIndex(activityID))
end

function UnInitialize()
    ExpiredTime.transform:DOKill()
    _eventSubscriber:Dispose()
    _csEventSubscriber:Dispose()
end

function Awake()
    InitHouseItem()
    StartButton:AddClick(OnClickShowChapterDetail)
    FinalRewardButton:AddClick(OnClickClaimFinalReward)
    TipsButton:AddClick(OnClickShowTips)
    _csEventSubscriber = CS.FQDev.EventSystem.EventSubscriber()
    _csEventSubscriber:Subscribe(typeof(CS.App.Bag.ItemUpdatedEvent), UpdateExchangeCoin)
    _eventSubscriber:Subscribe(EventCenter.ID.LightingActivity_DataUpdated, OnRefresh)
end


function OnDestroy()
    ExpiredTime.transform:DOKill()
    _eventSubscriber:Dispose()
    _csEventSubscriber:Dispose()
end