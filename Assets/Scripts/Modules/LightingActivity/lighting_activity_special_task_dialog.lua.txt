local ConfigManager = require "config_manager"
local _eventSubscriber = (require "event_subscriber").New()
local EventCenter = require "event_center"
local LightingActivityManager = require "lighting_activity_manager"
local TableUtil = require "table_util"

local _csEventSubscriber
local _activityID = 0

local function CloseUI()
    self:GetComponent(typeof(CS.LuaScript.LuaLayerContent)):Close()
end


local function SetActivityTime()
    local lightingData = LightingActivityManager.GetLightingActivityData(_activityID)
    local expireTime = lightingData.expiredTime
    if expireTime > 0 then
        local diff = os.difftime(expireTime, CS.FQDev.Time.TimeManager.ServerTimeUTC)
        if diff > 0 then
            ExpiredTime.transform:DOKill()
            ExpiredTime.transform:DOCounter(diff, 0, diff, function(v)
                ExpiredTime.text = CS.App.Extends.TimeFormat.FormatSecToDay(v)
            end):OnComplete(function()
                ExpiredTime.text = ""
            end)
        else
            ExpiredTime.text = ""
        end
    else
        ExpiredTime.text = ""
    end
end

local function LoadFinalReward()
    local exchangeList = LightingActivityManager.GetExchangeList(_activityID)
    local id = 0
    for k,v in pairs(exchangeList[1].cost) do
        id = k
        break
    end

    if SpecialTaskType == 1 then
        FinalReward.ScriptEnv.SetReward(CS.RewardItem.Parse(string.format("item:%d:%d", id, CS.App.Bag.BagManager.Instance:GetItemCount(id))))   
        local count = LightingActivityManager.GetRechargeScore(_activityID)
        FinalReward2.ScriptEnv.SetReward(CS.RewardItem.Parse(string.format("item:99:%d", count)))
    else
        local max = 0
        local task = nil
        local taskList = LightingActivityManager.GetLightingScoreTaskList(_activityID)
        for k,v in pairs(taskList) do
            if v.config.target_count > max then
                max = v.config.target_count
                task = v
            end
        end

        FinalReward.ScriptEnv.SetReward(task.config.rewards[2])
        Claim:SetActive(task.state == LightingActivityManager.TaskState.Claim)
    end

    -- local max = 0
    -- local task = nil
    -- for k,v in pairs(taskList) do
    --     if v.config.target_count > max then
    --         max = v.config.target_count
    --         task = v
    --     end
    -- end

    -- FinalReward.ScriptEnv.SetReward(task.config.rewards[1])
    --Claim:SetActive(task.state == LightingActivityManager.TaskState.Claim)
end

local function OnClickShowTips()
    local lay = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
            CS.App.UI.LayerTag.Popup,
            "ui/message/CommonWindowTipsLayer")

    local luaScript = lay:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    local content = CS.LanguageManager.Instance:GetLanguage("Item_99_Des", "Item_99_Des")
    local rechargeTips = CS.LanguageManager.Instance:GetLanguage("Activity_99_Info", "Activity_99_Info")
    content = content.."\n\n"..CS.LanguageManager.Instance:GetLanguage("Activity_99_Info1", "Activity_99_Info1")
    local rechargeList = ConfigManager.Tables.ActivityConst.RechargeCoinInfo
    for i = 1, #rechargeList do
        local config = ConfigManager.Tables.InappConfig[rechargeList[i]]
        local price = CS.IAPManager.Instance:GetLocalizedPriceByInappId(rechargeList[i])
        content = content.."\n"..CS.System.String.Format(rechargeTips, price, config.cumulative_num)
    end

    luaScript:CallFunctionTable("ShowTips",
            {
                content = content,
                title = CS.LanguageManager.Instance:GetLanguage("Lighting_ActivityName60008", "Lighting_ActivityName60008")
            })
end

local function OnRefresh(activityID)
    if _activityID ~= activityID then
        return
    end
    SetActivityTime()
    LoadFinalReward()
    TaskScroll.ScriptEnv.LoadTaskList(_activityID, SpecialTaskType)
end

local function SendTaskListLogEvent()
    local taskList = MonopolyManager.GetTaskList()
    local taskStr = {}
    for i = 1, #taskList do
        table.insert(taskStr, taskList[i].config.id)
        if i < #taskList then
            table.insert(taskStr, "|")
        end
    end
    CS.LogEvent.I:Track("Activity_Match_Task_Open")
    :IsNew()
    :AddMaxChapter()
    :Add("ActivityID", MonopolyManager.GetBattingActivityID())
    :Add("MatchFillTaskId", table.concat(taskStr))
    :Send()
end

function Initialize(activityID)
    _activityID = activityID
    --SendTaskListLogEvent()
    OnRefresh(activityID)
end

function UnInitialize()
    ExpiredTime.transform:DOKill()
    _eventSubscriber:Dispose()
    _csEventSubscriber:Dispose()
end

function Awake()
    TipsButton:AddClick(OnClickShowTips)
    _csEventSubscriber = CS.FQDev.EventSystem.EventSubscriber()
    _eventSubscriber:Subscribe(EventCenter.ID.LightingActivity_DataUpdated, OnRefresh)
end


function OnDestroy()
    ExpiredTime.transform:DOKill()
    _eventSubscriber:Dispose()
    _csEventSubscriber:Dispose()
end