local ConfigManager = require "config_manager"
local _eventSubscriber = (require "event_subscriber").New()
local EventCenter = require "event_center"
local LightingActivityManager = require "lighting_activity_manager"
local TableUtil = require "table_util"
local AvatarFrameManager = require "avatar_frame_manager"
local RankListManager = require "rank_list_manager"

local _csEventSubscriber
local _rankListData = nil
local _rankType = ""
local _rankData = nil
local _rewardHasLoad = false
local _tabMenuItemList = {}
local _activityID = 0

local function CloseUI()
    self:GetComponent(typeof(CS.LuaScript.LuaLayerContent)):Close()
end

local function SetActivityTime()
    local lightingData = LightingActivityManager.GetLightingActivityData(_activityID)
    local expireTime = lightingData.expiredTime
    local timerLang = CS.LanguageManager.Instance:GetLanguage("Activity_Match_Ranking_Tips1", "Activity_Match_Ranking_Tips1").."%s"
    if expireTime > 0 then
        local diff = os.difftime(expireTime, CS.FQDev.Time.TimeManager.ServerTimeUTC)
        if diff > 0 then
            RefreshTimer.transform:DOKill()
            RefreshTimer.transform:DOCounter(diff, 0, diff, function(v)
                RefreshTimer.text = CS.App.Extends.TimeFormat.FormatSecToDay(v)
            end):OnComplete(function()
                CloseUI()
            end)
        else
            CloseUI()
        end
    else
        RefreshTimer.text = ""
    end
end

local function SendRankListLogEvent(rank, score)
    local battingMatchData = BattingMatchManager.GetBattingActivityData()
    CS.LogEvent.I:Track("Activity_Ranking_Open")
    :IsNew()
    :AddMaxChapter()
    :Add("RankingType", "ActivityMatch")
    :Add("RankingGroup", battingMatchData.extra.rankType)
    :Add("Player_in_number", rank)
    :Add("Ranking_Score", score or battingMatchData.extra.score)
    :Add("Ranking_Show_Score", battingMatchData.extra.score)
    :Send()
end

local function LoadTopItemList()
    if not _rankData then
        local count = TopListNode.childCount
        for i = 1, count do
            local item = TopListNode:GetChild(i - 1):GetComponent(typeof(CS.LuaScript.LuaListItem))
            item:CallFunction("SetEmpty")
        end
        return 
    end

    local count = TopListNode.childCount
    for i = 1, count do
        local item = TopListNode:GetChild(i - 1):GetComponent(typeof(CS.LuaScript.LuaListItem))
        if _rankData.list and i <= #_rankData.list then
            item:CallFunctionTable("SetData", 
            {
                rank = i, 
                playerData = _rankData.list[i], 
                activityId = _activityID,
                rankType = _rankData.rankType,
                icon = _rankListData.icon,
            })
        else
            item:CallFunction("SetEmpty")
        end
    end
end

local function LoadSelfRankItem()
    if _rankData then
        SelfRankItem:CallFunctionTable("SetData", 
        {
            rank = _rankData.selfRank or 0,
            extra = _rankData.extra or nil,
            playerData = 
            {
                name = CS.DB.UserInfo.Name, 
                avatar = AvatarFrameManager.GetCurrAvatarId(), 
                frame = AvatarFrameManager.GetCurrAvatarFrameId(), 
                weigh = _rankData.selfWeight or 0,
                maxRank = _rankListData.maxRank or 0,
            }, 
            rankType = _rankData.rankType,
            icon = _rankListData.icon,
            isSelf = true,
            activityID = _activityID,
        })
    else
        SelfRankItem:CallFunctionTable("SetData", 
        {
            rank = 0,
            extra = nil,
            playerData = 
            {
                name = CS.DB.UserInfo.Name, 
                avatar = AvatarFrameManager.GetCurrAvatarId(), 
                frame = AvatarFrameManager.GetCurrAvatarFrameId(), 
                weigh = _rankListData.localWeight,
                maxRank = _rankListData.maxRank or 0,
            }, 
            rankType = _rankListData.rankType,
            icon = _rankListData.icon,
            isSelf = true,
            activityID = _activityID,
        })
    end
end

local function LoadRankList()
    _rankData = RankListManager.GetRankDataByRankType(_rankType)
    LoadTopItemList()
    LoadSelfRankItem()
    EmptyNode:SetActive(not _rankData or #_rankData.list <= 3)
    Scroll:CallFunctionTable("LoadRankItemList", {rankData = _rankData, startIndex = 4, icon = _rankListData.icon, activityID = _activityID,})
end

local function LoadRankReward()
    if not _rewardHasLoad then
        _rewardHasLoad = true
        local list = LightingActivityManager.GetRankRewardList(_activityID)
        for i = 1, #list do
            local go = CS.LuaScript.LuaAssetOperator.InstantiatePrefab(
                    "ui/lightingactivity",
                    "LightingActivityRankRewardItem",
                    RewardNode)
            local item = go:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
            item:CallFunctionTable("SetData", {data = list[i], selfRank = _rankData.selfRank or 0})
        end
    end
end

local function OnClickRankList()
    Scroll.gameObject:SetActive(true)
    RewardScroll:SetActive(false)
    SelfRankItem.gameObject:SetActive(true)
    _tabMenuItemList[1].select:SetActive(true)
    _tabMenuItemList[2].select:SetActive(false)
    LoadRankList()
end

local function OnClickRewardList()
    Scroll.gameObject:SetActive(false)
    RewardScroll:SetActive(true)
    EmptyNode:SetActive(false)
    --SelfRankItem.gameObject:SetActive(false)
    _tabMenuItemList[1].select:SetActive(false)
    _tabMenuItemList[2].select:SetActive(true)
    LoadRankReward()
end

local function OnRefresh(rankType)
    if rankType ~= _rankType then
        return
    end
    LoadRankList()
    SetActivityTime()
end

local function OnClickShowTips()
    local lay = CS.FQDev.UI.Layer.LayerManager.Instance:LoadContent(
            CS.App.UI.LayerTag.Popup,
            "ui/lightingactivity/CommonWindowTipsLayer")

    local luaScript = lay:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
    luaScript:CallFunctionTable("ShowTips",
            {
                content = CS.LanguageManager.Instance:GetLanguage("Activity_Lighting_Rank_Info", "Activity_Lighting_Rank_Info"),
                title = CS.LanguageManager.Instance:GetLanguage("Lighting_ActivityName60008", "Lighting_ActivityName60008"),
                tips = ""
            })
end

local function InitTabMenuList()
    table.insert(_tabMenuItemList, {select = RankButton.transform:GetChild(0).gameObject})
    table.insert(_tabMenuItemList, {select = RewardButton.transform:GetChild(0).gameObject})
end

local function ReqRankList()
    RankListManager.ReqRankList(20, true, _rankType, 1)
end

function Initialize(data)
    _rankListData = data
    _rankType = data.rankType
    _activityID = data.activityID

    ReqRankList()
    OnRefresh(_rankType)
end

function Awake()
    InitTabMenuList()
    RankButton:AddClick(OnClickRankList)
    RewardButton:AddClick(OnClickRewardList)
    TipsButton:AddClick(OnClickShowTips)

    _eventSubscriber:Subscribe(EventCenter.ID.RankList_DateUpdate, OnRefresh)
end

function Start()

end

function OnEnable()

end

function OnDestroy()
    RefreshTimer.transform:DOKill()
    _eventSubscriber:Dispose()
end