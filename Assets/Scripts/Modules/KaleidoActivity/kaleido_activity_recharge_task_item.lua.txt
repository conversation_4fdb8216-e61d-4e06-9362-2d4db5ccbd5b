local ConfigManager = require "config_manager"
local TableUtil = require "table_util"
local EventCenter = require "event_center"
local _eventSubscriber = (require "event_subscriber").New()
local KaleidoActivityManager = require "kaleido_activity_manager"

local _config = nil
local _activityID = 0

function SetData(data)
    _config = data.data.config
    _activityID = data.activityID
    local state = data.data.state
    local progress = KaleidoActivityManager.GetTaskProgressByTypeID(_activityID, _config.type)

    if _config.taget_param then
        if state == KaleidoActivityManager.TaskState.Complete or state == KaleidoActivityManager.TaskState.Claim then 
            TaskProgress.value = 1
            ProgressText.text = "1/1"
        else
            TaskProgress.value = 0
            ProgressText.text = "0/1"
        end
    else
        TaskProgress.value = progress / _config.target_count
        ProgressText.text = progress.."/".._config.target_count
    end

    AdButton.gameObject:SetActive(_config.type == 25)
    GetButton.gameObject:SetActive(_config.type ~= 25)
    TaskProgress.gameObject:SetActive(_config.type ~= 25)

    if state == KaleidoActivityManager.TaskState.InComplete then
        ItemBg.sprite = CS.LuaScript.LuaAssetOperator.LoadAsset("ui/lightingactivity", "WinterFestivalExhaustedChargeListBox2", typeof(CS.UnityEngine.Sprite))
        NameBg.sprite = CS.LuaScript.LuaAssetOperator.LoadAsset("ui/lightingactivity", "WinterFestivalExhaustedChargeListTitle2", typeof(CS.UnityEngine.Sprite))
        Line.sprite = CS.LuaScript.LuaAssetOperator.LoadAsset("ui/lightingactivity", "WinterFestivalExhaustedChargeLine2", typeof(CS.UnityEngine.Sprite))
        ButtonGrayImage.Amount = 1
        GetButton.interactable = false
    elseif state == KaleidoActivityManager.TaskState.Complete then
        ItemBg.sprite = CS.LuaScript.LuaAssetOperator.LoadAsset("ui/lightingactivity", "WinterFestivalExhaustedChargeListBox", typeof(CS.UnityEngine.Sprite))
        NameBg.sprite = CS.LuaScript.LuaAssetOperator.LoadAsset("ui/lightingactivity", "WinterFestivalExhaustedChargeListTitle", typeof(CS.UnityEngine.Sprite))
        Line.sprite = CS.LuaScript.LuaAssetOperator.LoadAsset("ui/lightingactivity", "WinterFestivalExhaustedChargeLine", typeof(CS.UnityEngine.Sprite))
        ButtonGrayImage.Amount = 0
        GetButton.interactable = true
    else
        ItemBg.sprite = CS.LuaScript.LuaAssetOperator.LoadAsset("ui/lightingactivity", "WinterFestivalExhaustedChargeListBox", typeof(CS.UnityEngine.Sprite))
        NameBg.sprite = CS.LuaScript.LuaAssetOperator.LoadAsset("ui/lightingactivity", "WinterFestivalExhaustedChargeListTitle", typeof(CS.UnityEngine.Sprite))
        Line.sprite = CS.LuaScript.LuaAssetOperator.LoadAsset("ui/lightingactivity", "WinterFestivalExhaustedChargeLine", typeof(CS.UnityEngine.Sprite))
        AdButton.gameObject:SetActive(false)
        GetButton.gameObject:SetActive(false)
        TaskProgress.gameObject:SetActive(false)
    end

    Received:SetActive(state == KaleidoActivityManager.TaskState.Claim)
    local description = CS.LanguageManager.Instance:GetLanguage(_config.desc, _config.desc)
    if _config.taget_param then
        description = string.format(description, CS.IAPManager.Instance:GetLocalizedPriceByInappId(_config.taget_param))
    else
        description = string.format(description, _config.target_count)
    end
    TaskDes.text = description

    local count = RewardNode.childCount
    for i = 1, count do
        local go = RewardNode:GetChild(i - 1).gameObject
        if i <= #_config.rewards then
            local item = go:GetComponent(typeof(CS.LuaScript.LuaBehaviour))
            item.ScriptEnv.SetReward(_config.rewards[i])
        end

        go:SetActive(i <= #_config.rewards)
    end   
end

local function OnClickClaim()
    KaleidoActivityManager.ClaimTaskReward(_activityID, _config.id)
end

local function OnClickAd()
    CS.AdManager.Instance:ShowRewardedVideo(function()
        KaleidoActivityManager.ClaimADTask(_activityID, _config.id)
    end, nil, "AdCollectionTask");
end

function Awake()
    GetButton:AddClick(OnClickClaim)
    AdButton:AddClick(OnClickAd)
end

