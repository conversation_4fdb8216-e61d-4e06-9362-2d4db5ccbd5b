local StringModifier = require "string_modifier"
local TableUtil = require "table_util"

local function int(value)
    return tonumber(value)
end

local function float(value)
    return tonumber(value)
end

--Core.IM.Number
local function number(value)
    return tonumber(value)
end

local function string(value)
    return tostring(value)
end

local function bool(value)
    if value == "true" or value == "1" then
        return true
    else
        return false
    end
end

local function color(value)
    local list = StringModifier.Split(value, '|')
    local color = CS.UnityEngine.Color()
    color.r = list[1]
    color.g = list[2]
    color.b = list[3]
    color.a = 0
    return color
end

local function list_number(value)
    local list = StringModifier.Split(value, '|')
    for i, v in ipairs(list) do
        list[i] = tonumber(v)
    end
    return list
end

local function list_string(value)
    local list = StringModifier.Split(value, '|')
    for i, v in ipairs(list) do
        list[i] = v
    end
    return list
end

local function dictionary_int_int(value)
    local list = StringModifier.Split(value, '|')
    local dic = {}
    for i, v in ipairs(list) do
        local key = v:gsub(":.+$", "")
        local number = v:gsub("^.+:", "")
        dic[tonumber(key)] = tonumber(number)
    end
    return dic
end

local function dictionary_int_float(value)
    local list = StringModifier.Split(value, '|')
    local dic = {}
    for i, v in ipairs(list) do
        local key = v:gsub(":.+$", "")
        local number = v:gsub("^.+:", "")
        dic[tonumber(key)] = tonumber(number)
    end
    return dic
end

local function dictionary_string_file(value)
    local list = StringModifier.Split(value, '|')
    local dic = {}
    for i, v in ipairs(list) do
        local key = v:gsub(":.+$", "")
        local file = v:gsub("^.+:", "")
        dic[key] = file
    end
    return dic
end

local function dictionary_string_string(value)
    local list = StringModifier.Split(value, '|')
    local dic = {}
    for i, v in ipairs(list) do
        local key = v:gsub(":.+$", "")
        local number = v:gsub("^.+:", "")
        dic[key] = number
    end
    return dic
end

local function dictionary_int_string(value)
    local list = StringModifier.Split(value, '|')
    local dic = {}
    for i, v in ipairs(list) do
        local key = v:gsub(":.+$", "")
        local value = v:gsub("^.+:", "")
        dic[tonumber(key)] = value
    end
    return dic
end

local function dictionary_string_int(value)
    local list = StringModifier.Split(value, '|')
    local dic = {}
    for i, v in ipairs(list) do
        local key = v:gsub(":.+$", "")
        local number = v:gsub("^.+:", "")
        dic[key] = tonumber(number)
    end
    return dic
end

local function dictionary_int_file(value)
    local list = StringModifier.Split(value, '|')
    local dic = {}
    for _, v in ipairs(list) do
        local key = v:gsub(":.+$", "")
        local content = v:gsub("^.+:", "")
        dic[tonumber(key)] = content
    end
    return dic
end

local function class_assetref(value)
    return CS.AssetRef.Parse(value)
end

-- local ItemType = require "item_type"
-- local function single_reward(value)
--     if value == nil or value == "" then
--         return nil
--     end
--     local detail = StringModifier.Split(value, ':')
--     local data = {}

--     data.type = ItemType.Parse(detail[1])
--     if data.type == ItemType.Item then        
--         data.id = tonumber(detail[2])
--         data.count = tonumber(detail[3])
--         data.probability = tonumber(detail[4])
--     elseif data.type == ItemType.Currency then
--         data.id = tonumber(detail[2])
--         data.count = tonumber(detail[3])
--         data.probability = tonumber(detail[4])
--     elseif data.type == ItemType.Player then        
--         data.id = tonumber(detail[2])
--         data.probability = tonumber(detail[3])
--         data.count = 1
--     elseif data.type == ItemType.Skin then        
--         data.id = tonumber(detail[2])
--         data.probability = tonumber(detail[3])
--         data.count = 1
--     elseif data.type == ItemType.Exp then        
--         data.id = 1
--         data.count = tonumber(detail[2])  
--         data.probability = tonumber(detail[3])      
--     elseif data.type == ItemType.Fashion then        
--         data.id = tonumber(detail[2])
--         data.count = tonumber(detail[3])
--         data.probability = tonumber(detail[4])
--     elseif data.type == ItemType.BattlePassExp then        
--         data.id = 1
--         data.count = tonumber(detail[2])
--         data.probability = tonumber(detail[3])
--     elseif data.type == ItemType.Medal then         
--         data.id = tonumber(detail[2])
--         data.count = tonumber(detail[3])
--         data.probability = tonumber(detail[4])
--     elseif data.type == ItemType.MedalPacket then         
--         data.id = tonumber(detail[2])
--         data.count = tonumber(detail[3])
--         data.probability = tonumber(detail[4])
--     elseif data.type == ItemType.Trophy then 
--         data.id = 0
--         data.count = tonumber(detail[2])
--         data.probability = tonumber(detail[3])
--     elseif data.type == ItemType.Voice then 
--         data.id = tonumber(detail[2])
--         data.probability = tonumber(detail[3])
--         data.count = 1
--     elseif data.type == ItemType.Badge then 
--         data.id = tonumber(detail[2])
--         data.probability = tonumber(detail[3])
--         data.count = 1
--     elseif data.type == ItemType.MatchShow then
--         data.id = tonumber(detail[2])
--         data.probability = tonumber(detail[3])
--         data.count = 1
--     elseif data.type == ItemType.HeadFrame then
--         data.id = tonumber(detail[2])
--         data.probability = tonumber(detail[3])
--         data.count = 1
--     elseif data.type == ItemType.RepresentSkill then
--         data.id = tonumber(detail[2])
--         data.probability = tonumber(detail[3])
--         data.count = 1
--     elseif data.type == ItemType.RechargeProduct then
--         data.id = tonumber(detail[2])
--         data.probability = tonumber(detail[3])
--         data.count = 1
--     elseif data.type == ItemType.PlayerSkill then 
--         data.id = tonumber(detail[2])
--         data.probability = tonumber(detail[3])
--         data.count = 1
--     elseif data.type == ItemType.PlayerStortID then 
--         data.id =  tonumber(detail[2])
--         data.probability = tonumber(detail[3])
--         data.count = 1
--     elseif data.type == ItemType.AttributePoint then 
--         data.id =  0
--         data.count = tonumber(detail[2])
--     else
--         if detail[1] == nil or detail[2] == nil or detail[3] == nil or detail[4] == nil then
--             return value
--         end
--         print("unsupported reward type:"..detail[1])
--         data.id = tonumber(detail[2])
--         data.count = tonumber(detail[3])
--         data.probability = tonumber(detail[4])
--     end
--     return data
-- end

local function class_rewarditem(value)
    return CS.RewardItem.Parse(value)
end

local function list_class_rewarditem(value)
    local list = StringModifier.Split(value, '|')
    for i, v in ipairs(list) do
        list[i] = class_rewarditem(v)
    end
    return list
end

local function random_reward(value)
    local param = StringModifier.Split(value, '^')
    local probability = tonumber(param[1])
    local reward =  class_rewarditem(param[2])
    local data = 
    {
        probability = probability,
        reward = reward
    }
    return data
end

local function list_vector3(value)
    local list = StringModifier.Split(value, '|')
    for i, v in ipairs(list) do
        local detail = StringModifier.Split(v, ':')
        local data = {
            x = tonumber(detail[1]),
            y = tonumber(detail[2]),
            z = tonumber(detail[3])
        }
        list[i] = data
    end
    return list
end

local function vector2(value)
    local x, y = StringModifier.SplitPair(value, '^')
    return {
        x = tonumber(x), 
        y = tonumber(y)
    }
end

local function vector3(value)
    local vector = StringModifier.Split(value, '^')
    return {
        x = tonumber(vector[1]),
        y = tonumber(vector[2]),
        z = tonumber(vector[3]),
    }
end

local function quaternion(value)
    local vector = StringModifier.Split(value, '^')
    return {
        x = tonumber(vector[1]),
        y = tonumber(vector[2]),
        z = tonumber(vector[3]),
        w = tonumber(vector[4]),
    }
end

local function battle_pass_price(value)
    local list = StringModifier.Split(value,'|')
    for i, v in ipairs(list) do
        local detail = StringModifier.Split(v, ':')
        local data = {
            time = tonumber(detail[1]),
            currencyType = tonumber(detail[2]),
            count = tonumber(detail[3])
        }
        list[i] = data
    end
    return list
end

local function list_tuple_int_int_(value)
    local list = StringModifier.Split(value,'|')
    for i, v in ipairs(list) do
        local detail = StringModifier.Split(v, ':')
        local data = {
            tonumber(detail[1]),
            tonumber(detail[2])
        }
        list[i] = data
    end
    return list
end

local function key_value_int(value)
    local str = StringModifier.Split(value,'_')
    local data = {
        key = tonumber(str[1]),
        value = tonumber(str[2])
    }
    return data
end

local function key_value_string(value)
    local str = StringModifier.Split(value,'_')
    local data = {
        key = str[1],
        value = str[2]
    }
    return data
end

-- local function net_message_messageid(value)
--     return ProtoManage.GetMessageID(value)
-- end

local function localized(value)
    return value
end

local function hyper_link(value)
    if value == nil or value == "" then
        return nil
    end
    local str = StringModifier.Split(value,':')
    local data = {
        destination = str[1]
    }
    if str[2] ~= nil then
        data.param = tonumber(str[2])
    end
    if str[3] ~= nil then
        data.param2 = tonumber(str[3])
    end
    if str[4] ~= nil then
        data.param3 = tonumber(str[4])
    end
    return data
end

local function refine_cost(value)
    if value == nil or value == "" then
        return nil
    end
    local list = StringModifier.Split(value, ':')
    local data = {
        type = tonumber(list[1]),
        id = tonumber(list[2]),
        count = tonumber(list[3])
    }
    return data
end

local function list_refine_cost(value)
    local list = StringModifier.Split(value, '|')
    local data = {}
    for i, v in ipairs(list) do
        table.insert(data, refine_cost(v))
    end
    return data
end

local function time(value)
    if value == nil or "" ==value then
        return nil 
    end
    local list = StringModifier.Split(value, '/')
    local data = {}
    data.year    = tonumber(list[1])
    data.month   = tonumber(list[2])
    local list_1 = StringModifier.Split(list[3], ':')
    data.day     = tonumber(list_1[1])
    data.hour    = tonumber(list_1[2])
    data.minute  = list_1[3] == nil and 0 or  tonumber(list_1[3])
    data.second  = list_1[4] == nil and 0 or  tonumber(list_1[4])
    return data
end

local module = {
    time = time,
    bool = bool,
    int = int,
    float = float,
    number = number,
    color = color,
    string = string,
    list_int = list_number,
    list_float = list_number,
    list_string = list_string,
    key_value_int = key_value_int,
    key_value_string = key_value_string,
    dictionary_int_int = dictionary_int_int,
    dictionary_string_string = dictionary_string_string,
    dictionary_string_int = dictionary_string_int,
    dictionary_int_string = dictionary_int_string,
    dictionary_int_float = dictionary_int_float,
    dictionary_string_file = dictionary_string_file,
    dictionary_int_file = dictionary_int_file,

    list_vector3 = list_vector3,
    vector2 = vector2,
    vector3 = vector3,
    quaternion = quaternion,
    hyper_link = hyper_link,

    class_assetref = class_assetref,
    class_rewarditem = class_rewarditem,
    list_class_rewarditem = list_class_rewarditem,
    list_tuple_int_int_ = list_tuple_int_int_,
    refine_cost = refine_cost,
    list_refine_cost = list_refine_cost,
    random_reward = random_reward, 
}

return TableUtil.MakeTableReadonly(module)
