local TableUtil = require "table_util"

local _tableList = {
    "HeroConfig",
    "HeroLevelConfig",
    "HeroQualityConfig",
    "BattlePassConfig",
    "TaskConfig",
    "TaskDaily",
    "TaskTypeConfig",
    "InappConfig",
    "ChapterConfig",
    "GrowthFundConfig",
    "GrowthFundStageConfig",
    "RuleConfig",
    "PiggyBankConfig",
    "ItemConfig",
    "HeroPoolConfig",
    "PassActivityConfig",
    "PeriodicPurchaseConfig",
    "LinkActivityTaskConfig",
    "GiftConfig",
    "ActivityConfig",
    "ActivityShowcaseConfig",
    "DailyChallengeGoalConfig",
    "DailyChallengeGoalTypeConfig",
    "AttackAttributeConfig",
    "GainConfig",
    "SkillGradeConfig",

    "LostTempleLayerConfig",
    "LostTempleMapConfig",
    "LostTempleTileConfig",
    "LostTempleRelicConfig",
    "LostTempleCampConfig",
    "LostTempleShopItemConfig",
    "LostTempleMapShopConfig",
    "LostTemplePassTaskConfig",
    "TowerConfig",
    "TowerStarConfig",
    "TowerPassConfig",
    "TowerPassTaskConfig",
    
    "TalentConfig",
    "TalentLevelConfig",
    
    
    "ActivityExchangeConfig",
    "ActivityExchangeTaskConfig",

    "GiftPeriodConfig",
    
    "ActivityGiftSetTaskConfig",
    "ActivityGiftSetUIConfig",
    
    "DailyBlessingConfig",
    "ItemBoxConfig",
    "GiftTriggerConfig",
    "AvatarConfig",
    "AvatarFrameConfig",

    "DailyChallengeRandomLevelConfig",
    
    "ActivityBossRushRankingConfig",
    "ActivityBossRushConfig",
    
    "TileTerrainGroupConfig",
    "TileTerrainConfig",

    "ActivityRanking",
    "ActivityFlipConfig",
    "ActivityFlipTimesConfig",
    "ActivityBattlePassConfig",
    "ActivityPVPConfig",
    
    "PVPRobotConfig",
    "PVPEnemyPowerUpConfig",
    "DrawCardsConfig",

    "ActivityCollectionConfig",
    "ActivityCollectionFurtherBoxConfig",
    "ActivityCollectionMapConfig",
    "TravelingMerchantPageConfig",
    "TravelingMerchantCommodityConfig",
    "EquipConfig",
    "EquipLevelConfig",

    "ActivityMatchAreaConfig",
    "ActivityMatchItemConfig",
    "ActivityMatchPoolConfig",
    "ActivityMatchConfig",
    "ActivityMatchRanking",
    
    "ActivityAdventureConfig",
    "ActivityAdventureItemConfig",
    "ActivityChessMapConfig",
    "ActivityChessCellConfig",
    "ActivityChessConfig",
    
    "GiftBuyAllConfig",
    "ActivityExchangeDetailConfig",
    "ActivityItemExchange",
    
    "HeroSkinConfig",
    
    "EnemyGalleryConfig",
    "EnemyConfig",
    
    "HeroBondAssembleConfig",
    "HeroBondConditionConfig",
    "HeroBondConfig",
    "HeroBondEffectConfig",

    "GatheringGiftConfig",
    
    "ActivityRouletteAwardConfig",
    "ActivityRoulettePoolConfig",
    "ActivityRouletteConfig",
    "ActivityRouletteGiftConfig",
    "BossChallengeChapterConfig",
    "BossChallengeSkillConfig",
    "BossChallengeExchangeConfig",
    "BossChallengeRankConfig",
    "BossChallengeRankRewardConfig",

    "EntryStorageConfig",
    
    "ChapterFundStageConfig",
    "ChapterFundConfig",
    "GiftResourseConfig",

    "ActivityLightingConfig",
    "ActivityLightingAwardConfig",
    "LightingActivityRankRewardConfig",
    
    "ActivityExploreItemGroupConfig",
    "ActivityExploreMapAwardConfig",
    "ActivityExploreMapConditionConfig",
    "ActivityExploreMapConfig",
    "ActivityExploreMapPoolConfig",
    "ActivityGroupRankingConfig",

    "WeeklyActivityConfig",
    
    "ElementTalentConditionConfig",
    "ElementTalentConfig",
    "ElementTalentNodeConfig",
    "ElementTalentNodeGradeConfig",
    
    "ElementTrialConfig",
    "ElementTrialChapterConfig",
    
    "ElementTrialPassConfig",
    "ElementTrialPassTaskConfig",
    
    "ElementTrialAchievementConfig",
    "ElementTrialRankConfig",

    "ActivityCollectorCardConfig",
    "ActivityCollectorCardExchangeConfig",

    "ActivityDailyLoginConfig",
    "ActivityDailyLoginRewardConfig",
    "ActivityOptionalGiftConfig",
    "ActivityOptionalGiftPoolConfig",
    
    "ActivitySkinsConfig",

    "NewbiePassRewardConfig",
    "AnniversaryReviewConfig",
    "AnniversaryReviewData",

    "GuardianLevelConfig",
    "GuardianConfig",
    "LordEquipConfig",
    "LordEquipLevelConfig",
    "LordEquipQualityConfig",
    "LordEquipPartConfig",
    "LordSkillConfig",
    "LordConfig",
    "LordLevelConfig",
    "LordQualityConfig",
    "LordSkillValueConfig",
    "LordStarLevelConfig",
    
    "LordAttributeConfig",
    "LordTalentConfig",
    "ActivityCoinConfig",
    "ActivityCoinTypeConfig",

    "EntranceConfig",
    "ActivityDungeonEntranceConfig",
    "PVPArenaSeasonConfig",
    "PVPArenaRankConfig",
    "PVPArenaPassTaskConfig",
    "PVPArenaExchangeConfig",
    "PVPArenaTaskConfig",

    "SkillConfig",
    "SkillValueConfig",
    "InternalWorldConfig",
    "InternalWorldFundConfig",
    "InternalWorldFundStageConfig",
    "SceneConfig",

    "ActivityCollectorCardWishConfig",
    "ActivityCollectorCardItemConfig",
    
    "GlobalTalentConfig",
    "GlobalTalentPageConfig",
    "GlobalTalentValueConfig",

    "TacticalOrderConfig",
    "TacticalOrderSkillConfig",
    
    "GuildLevelConfig",
    "GuildUnionConfig",
    "GuildAvatarConfig",
    "GuildPositionConfig",
    "GuildDonationConfig",
    "GuildShopConfig",
    "GuildPassConfig",
    "GuildTaskConfig",
    
    "BonusBoxConfig",
    
    "GuildBossChallengeConfig",
    "GuildBossLayersConfig",
    "GuildBossBoxConfig",
    
    "GuildExpeditionConfig",
    "GuildAwardBoxConfig",
    "GuildAwardBoxRuleConfig",
    "GuildExpeditionEventConfig",
    "GuildExpeditionEventValueConfig",
    "GuildExpeditionNodeConfig",
    "GuildExpeditionDailyBuffConfig",
    "GuildExpeditionGiftConfig",
    
    "ActivityPrayerPoolConfig",
    "ActivityPrayerPoolRewardConfig",

    "GuildWarStarConfig",
    "GuildWarGradeConfig",
    "GuildWarRankConfig",
    "GuildWarExchangeConfig",
    "FightPowerFundConfig",
    "FightPowerFundStageConfig",
    
    "ActivityCombineConfig",
    
    "GuildReportGroupConfig",
    "GuildWarLevelConfig",
    "GuildWarChapterRandomWaveConfig",
    "GuildWarChapterWaveConfig",
    "GuildWarRobotConfig",
    "TreasureExchangeCycleConfig",
    "TreasureExchangeConfig",
    "TreasureCardProbabilityConfig",
    "TreasureCardPoolConfig",
    "TreasureQualityConfig",
    "TreasureUpStarConfig",
    "TreasureConfig",
    "TreasureGiftConfig",
    "ExtraAttributeConfig",
    "ActivityCultivationConfig",
    "ActivityGuildTaskConfig",
    "EnemyBuffResistanceConfig",
    
    "ActivityPuzzleConfig",
    
    "ActivityKaleidoConfig",
    "ActivityKaleidoRewardConfig",
    "ActivityKaleidoUIConfig",
    
    "HeroDemonKingConfig",
    
    "LegionWarShopConfig",
    "LegionWarSeasonConfig",
    "LegionWarMapConfig",
    "LegionWarBuildingConfig",
    "BarrackHeroConfig",
    "LegionWarRankConfig",
    "LegionWarRankRewardConfig",
    
    "SkillCoreConfig",
    
    "PowerRankingConfig",
    
    "CampConfig",
    "SkillCampConfig",
}

local _constList = {
    "ConstConfig",
    "TaskConstConfig",
    "BattlePassConst",
    "GamePlayConst",
    "AdsConst",
    "LostTempleConst",
    "TowerConst",
    "AvatarConst",
    "ActivityBossRushConst",
    "BossChallengeConst",
    "WeeklyActivityConst",
    "FragmentExchangeConst",
    "ActivityCollectorCardConst",
    "ActivityConst",
    "NewbiePassConst",
    "LordConst",
    "PVPArenaConst",
    "ActivityChapterConstConfig",
    "InternalWorldConst",
    "GuildConst",
    "BonusBoxConst",
    "GuildActivityConst",
    "AutoMergeConst",
    "GuildExpeditionConst",
    "BattlePowerConst",
    "GuildWarConst",
    "TreasureConst",
    "LegionWarConst",
    "LegionWarGamePlayConst",
}

local module = {
    TableList = _tableList,
    ConstList = _constList
}

return TableUtil.MakeTableReadonly(module)