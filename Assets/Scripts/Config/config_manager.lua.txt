local CSVParser = require "csv_parser"
local TableUtil = require "table_util"
local ConfigFieldParser = require "config_field_parser"
-- local WordsUtil = require "words_util"
local ConfigList = require "config_list"
local UserLabelManager = CS.FQDev.UserLabel.UserLabelManager

local TABLE_CONFIG_ASSETBUNDLE = "config/table"
local CONST_CONFIG_ASSETBUNDLE = "config/const"

local _configTables = {}

local function CheckUserLabel(sourceBundle, tableName)
    local replaced = UserLabelManager.Instance:GetReplacement(sourceBundle .. "/" .. tableName)
    local _, newSourceBundle, newTableName = CS.FQDev.AssetBundles.AssetBundleUtils.ResolveAssetPath(replaced)
    return newSourceBundle, newTableName
end

local function LoadTable(tableName, sourceBundle)
    if sourceBundle == nil then
        sourceBundle = TABLE_CONFIG_ASSETBUNDLE
    end
    
    local newSourceBundle, newTableName = CheckUserLabel(sourceBundle, tableName)
    
    local raw = CS.LuaScript.LuaAssetOperator.ReadTextAsset(newSourceBundle, newTableName)
    if raw == nil then
        error("Fail to load table config: ".. newTableName)
    end
    local configTable = CSVParser.ParseTable(
        raw, 
        ConfigFieldParser)

    local result = TableUtil.MakeTableReadonly(configTable)
    _configTables[tableName] = result
    return result
end

local function LoadConst(tableName, sourceBundle)
    if sourceBundle == nil then
        sourceBundle = CONST_CONFIG_ASSETBUNDLE
    end
    
    local newSourceBundle, newTableName = CheckUserLabel(sourceBundle, tableName)
    local raw = CS.LuaScript.LuaAssetOperator.ReadTextAsset(newSourceBundle, newTableName)
    
    if raw == nil then
        error("Fail to load const config: "..newTableName)
    end
    local configTable = CSVParser.ParseConst(
        raw,
        ConfigFieldParser)
    
    local result = TableUtil.MakeTableReadonly(configTable)
    _configTables[tableName] = result
    return result
end

local function LoadAllConfig()
    for _, v in ipairs(ConfigList.TableList) do
        LoadTable(v)
    end

    for _, v in ipairs(ConfigList.ConstList) do
        LoadConst(v)
    end
    --parse CommonData values
    -- local commonDataTable = _configTables["CommonData"]
    -- local newCommonDataTable = {}
    -- for k, v in pairs(commonDataTable) do
    --     local fieldType = v.field_type:gsub("%W", "_"):gsub("%W$", ""):lower()
    --     if ConfigFieldParser[fieldType] ~= nil then
    --         newCommonDataTable[k] = ConfigFieldParser[fieldType](v.value)
    --     else
    --         newCommonDataTable[k] = v.value
    --     end
    -- end
    -- _configTables["CommonData"] = TableUtil.MakeTableReadonly(newCommonDataTable)

    -- word filter init
    --WordsUtil.LoadKeyWords("config_table", "WordsBlock")

    --load xml
    -- for _, v in ipairs(_xmlList) do
    --     LoadXml(v)
    -- end
end

local module = {
    LoadTable = LoadTable,
    LoadConst = LoadConst,
    LoadAllConfig = LoadAllConfig,
    Tables = _configTables,
}

return TableUtil.MakeTableReadonly(module)
