Shader "UI/SpriteOverlayFlow"
{
    Properties
    {
        [PerRendererData] _MainTex("Texture", 2D) = "white" {}

        [Toggle(_HIDE_MAIN)]_HIDE_MAIN("隐藏主贴图",int) = 0
        [Toggle(_MAIN_UV_FLOW)]_MAIN_UV_FLOW("开启贴图UV移动",int) = 0
        _MainFlowSpeed("贴图UV移动速度", Vector) = (0, 0, 0, 0)

        [Toggle(_USE_OVERLAY)]_USE_OVERLAY("开启覆盖",int) = 0
        _OverlayTex("覆盖贴图", 2D) = "white" {}
        [HDR]_OverlayColor("覆盖颜色", Color) = (1, 1, 1, 1)
        _OverlayFlowSpeed("覆盖移动速度", Vector) = (0, 0, 0, 0)
        _OverlayRotation("覆盖旋转角度", Range(0, 360)) = 0

        // [Toggle(_USE_UV_NOSIZE)]_USE_UV_NOSIZE("开启UV扰动", int) = 0
        // _UVNoiseTex("UV扰动贴图", 2D) = "white" {}
        // _UVNoiseStrength("UV扰动强度", Range(0,1)) = 0.01
        // _UVNoiseSpeed("UV扰动贴图速度向量", Vector) = (0,0,0,0)

        [HideInInspector]_StencilComp ("Stencil Comparison", Float) = 8
        [HideInInspector]_Stencil ("Stencil ID", Float) = 0
        [HideInInspector]_StencilOp ("Stencil Operation", Float) = 0
        [HideInInspector]_StencilWriteMask ("Stencil Write Mask", Float) = 255
        [HideInInspector]_StencilReadMask ("Stencil Read Mask", Float) = 255

        [HideInInspector]_ColorMask ("Color Mask", Float) = 15

        [Toggle(UNITY_UI_ALPHACLIP)] _UseUIAlphaClip ("Use Alpha Clip", Float) = 0
    }
    SubShader
    {
        Tags
        {
            "Queue" = "Transparent"
            "IgnoreProjector" = "True"
            "RenderType" = "Transparent"
            "PreviewType" = "Plane"
            "CanUseSpriteAtlas" = "True"
        }

        Stencil
        {
            Ref [_Stencil]
            Comp [_StencilComp]
            Pass [_StencilOp] 
            ReadMask [_StencilReadMask]
            WriteMask [_StencilWriteMask]
        }

        Cull Off
        Lighting Off
        ZWrite Off
        ZTest [unity_GUIZTestMode]
        Blend SrcAlpha OneMinusSrcAlpha
        ColorMask [_ColorMask]

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            
            #include "UnityCG.cginc"
            #include "UnityUI.cginc"

            #pragma multi_compile __ UNITY_UI_CLIP_RECT
            #pragma multi_compile __ UNITY_UI_ALPHACLIP

            #pragma multi_compile_local __ _MAIN_UV_FLOW
            #pragma multi_compile_local __ _HIDE_MAIN
            #pragma multi_compile_local __ _USE_OVERLAY
            #pragma multi_compile_local __ _USE_UV_NOSIZE

            struct appdata
            {
                float4 vertex : POSITION;
                float4 uv : TEXCOORD0;
                fixed4 color : COLOR0;
            };

            struct v2f
            {
                float4 vertex   : SV_POSITION;
                fixed4 color : COLOR0;
                float4 worldPosition : TEXCOORD0;
                float2 uv  : TEXCOORD1;
                float2 uvMask : TEXCOORD2;
                // float2 uvNoise : TEXCOORD3;
            };

            float2 TimeShiftUV(float2 originUV, float2 speed)
            {
                return originUV + frac(speed * _Time.y);
            }

            sampler2D _MainTex;
            float4 _MainFlowSpeed;

            sampler2D _OverlayTex;
            float4 _OverlayColor;
            float4 _OverlayTex_ST;
            float4 _OverlayFlowSpeed;
            float _OverlayRotation;

            // sampler2D _UVNoiseTex;
            // float4 _UVNoiseTex_ST;
            // float _UVNoiseStrength;
            // float4 _UVNoiseSpeed;

            v2f vert (appdata v)
            {
                v2f o;
                o.worldPosition = v.vertex;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = v.uv;

                #ifdef _USE_OVERLAY
                o.uvMask.xy = TRANSFORM_TEX(v.uv, _OverlayTex);
                #endif

                // #if _USE_UV_NOSIZE
                // o.uvNoise.xy = TRANSFORM_TEX(v.uv, _UVNoiseTex);
                // #endif

                o.color = v.color;
                return o;
            }
            
            float4 _ClipRect;

            fixed4 frag (v2f i) : SV_Target
            {
                float2 uvMain = i.uv;
                #ifdef _MAIN_UV_FLOW
                uvMain = TimeShiftUV(uvMain, _MainFlowSpeed.xy);
                #endif
                fixed4 color = tex2D(_MainTex, uvMain) * i.color;

                #ifdef _USE_OVERLAY
                // Rotate Mask
                float maskAngle = 3.1415926 / 180 * _OverlayRotation;
                float2 uvMaskTemp = (i.uvMask - _OverlayTex_ST.zw) / _OverlayTex_ST.xy - float2(0.5,0.5);
                float2 uvMask;
                uvMask.x = uvMaskTemp.x * cos(maskAngle) + uvMaskTemp.y * sin(maskAngle);
                uvMask.y = -uvMaskTemp.x * sin(maskAngle) + uvMaskTemp.y  * cos(maskAngle);
                uvMask = (uvMask + float2(0.5,0.5)) * _OverlayTex_ST.xy + _OverlayTex_ST.zw;

                float4 mask = tex2D(_OverlayTex, TimeShiftUV(uvMask, _OverlayFlowSpeed.xy)) * _OverlayColor;
                #ifdef _HIDE_MAIN
                color.rgb = mask.rgb;
                color.a = mask.a * color.a;
                #else
                color.rgb = lerp(color.rgb, mask.rgb, mask.a);
                #endif
                #endif

                #ifdef UNITY_UI_CLIP_RECT
                    color.a *= UnityGet2DClipping(i.worldPosition.xy, _ClipRect);
                #endif

                #ifdef UNITY_UI_ALPHACLIP
                    clip (color.a - 0.001);
                #endif

                // color.rgb = lerp(color.rgb, Luminance(color.rgb), _EffectAmount);
                return color;
            }
            ENDCG
        }
    }
}
