Shader "UI/SpriteSpecialHeroBg"
{
    Properties
    {
        [PerRendererData] _MainTex("Texture", 2D) = "white" {}
        
        _OuterFlowTex("外圈贴图", 2D) = "white" {}
        _OuterFlowSpeed("外圈颜色变化速度", Float) = 1
        
        _OverlayTex("覆盖贴图", 2D) = "white" {}
        [HDR]_OverlayColor("覆盖颜色", Color) = (1, 1, 1, 1)
        _OverlayFlowSpeed("覆盖移动速度", Vector) = (0, 0, 0, 0)
        _OverlayRotation("覆盖旋转角度", Range(0, 360)) = 0

        [HideInInspector]_StencilComp ("Stencil Comparison", Float) = 8
        [HideInInspector]_Stencil ("Stencil ID", Float) = 0
        [HideInInspector]_StencilOp ("Stencil Operation", Float) = 0
        [HideInInspector]_StencilWriteMask ("Stencil Write Mask", Float) = 255
        [HideInInspector]_StencilReadMask ("Stencil Read Mask", Float) = 255

        [HideInInspector]_ColorMask ("Color Mask", Float) = 15

        [Toggle(UNITY_UI_ALPHACLIP)] _UseUIAlphaClip ("Use Alpha Clip", Float) = 0
    }
    SubShader
    {
        Tags
        {
            "Queue" = "Transparent"
            "IgnoreProjector" = "True"
            "RenderType" = "Transparent"
            "PreviewType" = "Plane"
            "CanUseSpriteAtlas" = "True"
        }

        Stencil
        {
            Ref [_Stencil]
            Comp [_StencilComp]
            Pass [_StencilOp] 
            ReadMask [_StencilReadMask]
            WriteMask [_StencilWriteMask]
        }

        Cull Off
        Lighting Off
        ZWrite Off
        ZTest [unity_GUIZTestMode]
        Blend SrcAlpha OneMinusSrcAlpha
        ColorMask [_ColorMask]

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            
            #include "UnityCG.cginc"
            #include "UnityUI.cginc"

            #pragma multi_compile __ UNITY_UI_CLIP_RECT
            #pragma multi_compile __ UNITY_UI_ALPHACLIP

            struct appdata
            {
                float4 vertex : POSITION;
                float4 uv : TEXCOORD0;
                fixed4 color : COLOR0;
            };

            struct v2f
            {
                float4 vertex   : SV_POSITION;
                fixed4 color : COLOR0;
                float4 worldPosition : TEXCOORD0;
                float2 uv  : TEXCOORD1;
                float2 uvOuter : TEXCOORD2;
                float2 uvMask : TEXCOORD3;
                // float2 uvNoise : TEXCOORD3;
            };

            float2 TimeShiftUV(float2 originUV, float2 speed)
            {
                return originUV + frac(speed * _Time.y);
            }

            sampler2D _MainTex;
            float4 _MainFlowSpeed;

            sampler2D _OuterFlowTex;
            float4 _OuterFlowTex_ST;
            float _OuterFlowSpeed;
            
            sampler2D _OverlayTex;
            float4 _OverlayColor;
            float4 _OverlayTex_ST;
            float4 _OverlayFlowSpeed;
            float _OverlayRotation;

            // sampler2D _UVNoiseTex;
            // float4 _UVNoiseTex_ST;
            // float _UVNoiseStrength;
            // float4 _UVNoiseSpeed;

            v2f vert (appdata v)
            {
                v2f o;
                o.worldPosition = v.vertex;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = v.uv;
                
                o.uvOuter.xy = TRANSFORM_TEX(v.uv, _OuterFlowTex);
                o.uvMask.xy = TRANSFORM_TEX(v.uv, _OverlayTex);

                o.color = v.color;
                return o;
            }
            
            float4 _ClipRect;

            fixed4 frag (v2f i) : SV_Target
{
    float2 uvMain = i.uv;
    fixed4 color = tex2D(_MainTex, uvMain) * i.color;

    // 外圈贴图色相变化
    fixed4 outerColor = tex2D(_OuterFlowTex, i.uvOuter);
    float hue = frac(_Time.y * _OuterFlowSpeed);
    float3 hsv = RGBtoHSV(outerColor.rgb);
    hsv.x = frac(hsv.x + hue);
    outerColor.rgb = HSVtoRGB(hsv);
    color.rgb = lerp(color.rgb, outerColor.rgb, outerColor.a);

    // 覆盖贴图位置移动
    float2 uvOverlay = TimeShiftUV(i.uvMask, _OverlayFlowSpeed.xy);
    float4 overlayColor = tex2D(_OverlayTex, uvOverlay) * _OverlayColor;
    color.rgb = lerp(color.rgb, overlayColor.rgb, overlayColor.a);

    #ifdef UNITY_UI_CLIP_RECT
        color.a *= UnityGet2DClipping(i.worldPosition.xy, _ClipRect);
    #endif

    #ifdef UNITY_UI_ALPHACLIP
        clip (color.a - 0.001);
    #endif

    return color;
}
            ENDCG
        }
    }
}
