{"skeleton": {"hash": "q45EmWU1Fo8", "spine": "4.1.11", "x": -95, "y": -66.3, "width": 190, "height": 153}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "y": -65.38}, {"name": "2", "parent": "bone", "x": 1, "y": 36.42, "scaleX": 0.8653, "scaleY": 0.8653}, {"name": "1", "parent": "bone", "x": 0.5, "y": 134.08}, {"name": "4", "parent": "bone", "x": -30.68, "y": 46.96}, {"name": "5", "parent": "4", "length": 17.84, "rotation": 120.58, "x": -6.63, "y": 9.78}, {"name": "6", "parent": "5", "length": 16.92, "rotation": -8.78, "x": 17.84}, {"name": "7", "parent": "6", "length": 15.81, "rotation": 61.86, "x": 16.92}, {"name": "8", "parent": "7", "length": 20.74, "rotation": 13.11, "x": 15.81}, {"name": "9", "parent": "4", "length": 93.02, "rotation": 50.63, "x": 5.24, "y": 5.94}, {"name": "10", "parent": "bone", "x": 30.06, "y": 48.36}, {"name": "11", "parent": "10", "length": 16.54, "rotation": 45.86, "x": 7.68, "y": 8.73}, {"name": "12", "parent": "11", "length": 16.06, "rotation": 42.9, "x": 16.54}, {"name": "13", "parent": "12", "length": 16.35, "rotation": -68.77, "x": 16.06}, {"name": "14", "parent": "13", "length": 20.05, "rotation": -26.98, "x": 16.35}, {"name": "15", "parent": "10", "length": 91.77, "rotation": 129.44, "x": -5.24, "y": 6.63}, {"name": "16", "parent": "bone", "x": 7.7, "y": 107.5}, {"name": "17", "parent": "16", "x": -1.05}, {"name": "bone2", "parent": "root", "x": 132.03, "y": 149.91}, {"name": "bone3", "parent": "root", "x": 132.03, "y": 149.91}, {"name": "bone4", "parent": "root", "x": 132.03, "y": 149.91}, {"name": "bone5", "parent": "root", "x": 132.03, "y": 149.91}, {"name": "bone6", "parent": "root", "x": 132.03, "y": 149.91}], "slots": [{"name": "8", "bone": "bone", "attachment": "8"}, {"name": "7", "bone": "bone", "attachment": "7"}, {"name": "6", "bone": "10", "attachment": "6"}, {"name": "5", "bone": "15", "attachment": "5"}, {"name": "4", "bone": "4", "attachment": "4"}, {"name": "3", "bone": "9", "attachment": "3"}, {"name": "2", "bone": "2", "attachment": "2"}, {"name": "1", "bone": "1", "attachment": "1-2"}, {"name": "9", "bone": "16"}, {"name": "10", "bone": "17"}, {"name": "4-add-black-line", "bone": "4"}, {"name": "12", "bone": "bone2", "color": "ffbf00ff"}, {"name": "13", "bone": "bone3", "color": "ffbf00ff"}, {"name": "15", "bone": "bone4", "color": "ffbf00ff"}, {"name": "17", "bone": "bone5", "color": "ffbf00ff"}, {"name": "19", "bone": "bone6", "color": "ffbf00ff"}, {"name": "11", "bone": "bone2"}, {"name": "14", "bone": "bone3"}, {"name": "16", "bone": "bone4"}, {"name": "18", "bone": "bone5"}, {"name": "20", "bone": "bone6"}], "transform": [{"name": "10", "bones": ["4"], "target": "10", "x": -60.75, "y": -1.4, "mixRotate": -1, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}], "skins": [{"name": "default", "attachments": {"1": {"1-2": {"width": 52, "height": 36}}, "2": {"2": {"width": 90, "height": 43}}, "3": {"3": {"x": 49.03, "y": -0.45, "rotation": -50.63, "width": 84, "height": 101}}, "4": {"4": {"type": "mesh", "uvs": [0.93479, 0.6623, 0.99952, 0.76296, 0.94067, 0.82606, 0.57437, 0.99583, 0.39049, 0.95676, 0.34047, 0.69986, 0.41256, 0.62624, 0.36254, 0.59169, 0.26398, 0.55112, 0.23455, 0.48802, 0.22279, 0.4129, 0.16414, 0.34183, 0.0847, 0.29526, 0, 0.23517, 0, 0.14653, 0.04204, 0.07141, 0.16561, 0.02333, 0.27004, 0.01701, 0.36421, 0.01131, 0.47895, 0.01298, 0.57016, 0.01432, 0.65401, 0.06389, 0.69321, 0.1058, 0.74816, 0.16455, 0.71043, 0.19006, 0.64813, 0.23216, 0.60547, 0.2547, 0.61655, 0.28715, 0.62753, 0.3193, 0.65621, 0.3565, 0.68196, 0.38991], "triangles": [11, 17, 18, 18, 10, 11, 17, 11, 16, 11, 12, 16, 12, 14, 15, 12, 15, 16, 12, 13, 14, 19, 26, 18, 18, 9, 10, 28, 9, 27, 27, 9, 26, 26, 9, 18, 26, 19, 20, 25, 26, 21, 25, 22, 24, 25, 21, 22, 21, 26, 20, 24, 22, 23, 28, 7, 9, 8, 9, 7, 6, 7, 28, 6, 29, 30, 4, 6, 3, 3, 0, 2, 3, 6, 0, 6, 30, 0, 4, 5, 6, 2, 0, 1, 29, 6, 28], "vertices": [1, 4, 25.42, -5.2, 1, 1, 4, 31.64, -14.66, 1, 1, 4, 25.99, -20.6, 1, 1, 4, -9.18, -36.55, 1, 1, 4, -26.83, -32.88, 1, 1, 4, -31.63, -8.73, 1, 1, 4, -24.71, -1.81, 1, 4, 4, -29.51, 1.44, 0.69104, 6, -16.88, 21.62, 0.2601, 7, 3.12, 40, 0.00422, 8, -3.29, 41.84, 0.04464, 4, 4, -38.98, 5.25, 0.15087, 6, -9.83, 28.99, 0.62693, 7, 12.95, 37.26, 0.02838, 8, 5.66, 36.93, 0.19381, 4, 4, -41.8, 11.18, 0.09516, 6, -3.27, 29.41, 0.5987, 7, 16.41, 31.67, 0.04583, 8, 7.77, 30.71, 0.26031, 4, 4, -42.93, 18.24, 0.03584, 6, 3.71, 27.83, 0.46514, 7, 18.31, 24.78, 0.07698, 8, 8.06, 23.57, 0.42204, 4, 4, -48.56, 24.92, 0.00292, 6, 12, 30.58, 0.18195, 7, 24.64, 18.76, 0.04736, 8, 12.86, 16.27, 0.76777, 3, 6, 18.9, 36.03, 0.03281, 7, 32.71, 15.25, 0.0017, 8, 19.92, 11.02, 0.96549, 2, 6, 27.16, 41.49, 0.00048, 8, 27.33, 4.46, 0.99952, 1, 8, 26.35, -3.82, 1, 1, 8, 21.51, -10.35, 1, 2, 7, 27.81, -11.01, 0.03084, 8, 9.19, -13.44, 0.96916, 2, 7, 17.91, -12.71, 0.45123, 8, -0.83, -12.85, 0.54877, 3, 6, 33.71, 1.21, 0.00184, 7, 8.99, -14.24, 0.90236, 8, -9.87, -12.32, 0.09581, 3, 6, 29.48, -8.96, 0.22813, 7, -1.98, -15.3, 0.77187, 8, -20.79, -10.87, 0, 2, 6, 26.11, -17.04, 0.54989, 7, -10.7, -16.14, 0.45011, 2, 6, 18.79, -22.79, 0.79969, 7, -19.21, -12.4, 0.20031, 2, 6, 13.74, -24.82, 0.89534, 7, -23.39, -8.9, 0.10466, 2, 6, 6.65, -27.67, 0.94753, 7, -29.24, -3.99, 0.05247, 2, 6, 5.77, -23.41, 0.94901, 7, -25.9, -1.21, 0.05099, 3, 4, -2.1, 35.23, 0.0006, 6, 4.32, -16.39, 0.96634, 7, -20.4, 3.38, 0.03306, 3, 4, -6.19, 33.11, 0.01217, 6, 3.87, -11.8, 0.97816, 7, -16.56, 5.94, 0.00967, 3, 4, -5.13, 30.06, 0.06081, 6, 0.64, -11.65, 0.93843, 7, -17.95, 8.86, 0.00076, 2, 4, -4.07, 27.04, 0.3073, 6, -2.55, -11.51, 0.6927, 2, 4, -1.32, 23.54, 0.66621, 6, -6.82, -12.77, 0.33379, 1, 4, 1.15, 20.4, 1], "hull": 31}, "sword": {"x": 6.56, "y": 30.02, "width": 130, "height": 114}}, "4-add-black-line": {"sword2": {"type": "mesh", "uvs": [0.04998, 0.4016, 0.33099, 0.14099, 0.53074, 0.5155, 0.34622, 0.75487], "triangles": [0, 1, 2, 3, 0, 2], "vertices": [64.23, 40.41, 27.95, 69.86, 1.99, 27.17, 25.97, -0.12], "hull": 4}}, "5": {"5": {"x": 47.34, "y": 1.62, "rotation": -129.44, "width": 87, "height": 101}}, "6": {"6": {"type": "mesh", "uvs": [0.29734, 0.83464, 0.4173, 0.91127, 0.48627, 0.90475, 0.50577, 0.87703, 0.51326, 0.9341, 0.57474, 0.9667, 0.7082, 0.95203, 0.75018, 0.88029, 0.73069, 0.75312, 0.7052, 0.71725, 0.74268, 0.67976, 0.80416, 0.67323, 0.83265, 0.62269, 0.83265, 0.56074, 0.87108, 0.49745, 0.92806, 0.46158, 0.99104, 0.42898, 1, 0.34746, 0.94306, 0.29365, 0.84259, 0.25616, 0.72863, 0.24963, 0.65059, 0.26199, 0.57419, 0.27409, 0.48422, 0.32789, 0.47072, 0.37517, 0.49714, 0.39165, 0.52602, 0.40965, 0.57269, 0.43876, 0.55469, 0.48278, 0.52827, 0.51642, 0.50441, 0.5468, 0.31778, 0.7844], "triangles": [10, 29, 28, 7, 6, 5, 3, 7, 5, 7, 3, 8, 8, 3, 9, 5, 4, 3, 3, 2, 1, 31, 3, 1, 31, 1, 0, 31, 30, 3, 3, 30, 9, 30, 29, 9, 9, 29, 10, 14, 20, 19, 14, 13, 20, 14, 19, 15, 16, 15, 17, 15, 19, 18, 17, 15, 18, 13, 22, 21, 20, 13, 21, 10, 27, 22, 27, 26, 22, 22, 26, 23, 24, 23, 25, 26, 25, 23, 10, 28, 27, 13, 10, 22, 12, 11, 10, 13, 12, 10], "vertices": [1, 10, -31.33, -18.51, 1, 1, 10, -14.89, -28.16, 1, 1, 10, -5.44, -27.34, 1, 1, 10, -2.77, -23.85, 1, 1, 10, -1.75, -31.04, 1, 1, 10, 6.68, -35.15, 1, 1, 10, 24.96, -33.3, 1, 1, 10, 30.71, -24.26, 1, 1, 10, 28.04, -8.24, 1, 1, 10, 24.55, -3.72, 1, 5, 11, 9.79, -21.17, 0.36119, 12, -19.36, -10.91, 0.01454, 13, -2.66, -36.97, 0.00096, 14, -0.16, -41.57, 0.01596, 10, 29.68, 1.01, 0.60736, 5, 11, 16.24, -26.64, 0.63819, 12, -18.35, -19.31, 0.0729, 13, 5.54, -39.07, 0.00809, 14, 8.1, -39.72, 0.06431, 10, 38.11, 1.83, 0.21651, 5, 11, 23.53, -25, 0.70887, 12, -11.9, -23.07, 0.1415, 13, 11.38, -34.42, 0.02435, 14, 11.19, -32.93, 0.12367, 10, 42.01, 8.2, 0.00161, 4, 11, 29.13, -19.57, 0.46196, 12, -4.1, -22.9, 0.21887, 13, 14.05, -27.08, 0.06499, 14, 10.24, -25.18, 0.25418, 4, 11, 38.52, -17.79, 0.13346, 12, 3.99, -27.99, 0.14923, 13, 21.72, -21.39, 0.07195, 14, 14.5, -16.62, 0.64535, 4, 11, 47.2, -20.24, 0.0212, 12, 8.68, -35.7, 0.03204, 13, 30.6, -19.81, 0.0096, 14, 21.69, -11.18, 0.93715, 3, 11, 56.16, -23.57, 0.0006, 12, 12.97, -44.24, 0.00142, 14, 29.76, -6.06, 0.99799, 1, 14, 29.72, 4.29, 1, 1, 14, 21.15, 10.07, 1, 2, 13, 28.44, 8.52, 0.05905, 14, 6.92, 13.08, 0.94095, 2, 13, 14.05, 14.62, 0.85787, 14, -8.68, 11.99, 0.14213, 3, 12, 32.99, 2.85, 0.14491, 13, 3.47, 16.81, 0.85434, 14, -19.1, 9.14, 0.00076, 2, 12, 31.24, 13.28, 0.53718, 13, -6.89, 18.96, 0.46282, 2, 12, 24.2, 25.46, 0.84448, 13, -20.79, 16.8, 0.15552, 3, 11, 11.37, 32.3, 0.00125, 12, 18.2, 27.18, 0.86985, 13, -24.56, 11.83, 0.1289, 3, 11, 12.41, 28.26, 0.00661, 12, 16.2, 23.51, 0.87055, 13, -21.87, 8.65, 0.12284, 3, 11, 13.53, 23.84, 0.02774, 12, 14.02, 19.51, 0.87006, 13, -18.93, 5.16, 0.1022, 3, 11, 15.35, 16.69, 0.18421, 12, 10.49, 13.04, 0.79307, 13, -14.17, -0.47, 0.02272, 4, 11, 9.66, 14.6, 0.38432, 12, 4.9, 15.38, 0.28977, 13, -18.38, -4.84, 4e-05, 10, 3.93, 25.83, 0.32587, 3, 11, 4.09, 14.25, 0.27216, 12, 0.58, 18.91, 0.06063, 10, 0.31, 21.59, 0.66721, 1, 10, -2.96, 17.76, 1, 1, 10, -28.53, -12.18, 1], "hull": 32}, "sword2": {"x": -3.56, "y": 23.66, "width": 130, "height": 114}}, "7": {"7": {"x": -2.7, "y": 35.37, "scaleX": 1.3622, "width": 60, "height": 31}}, "8": {"8": {"y": 66.08, "width": 134, "height": 134}}, "9": {"9": {"width": 50, "height": 50}}, "10": {"10": {"x": 1.05, "width": 50, "height": 50}}, "11": {"11": {"x": 0.04, "y": 13.44, "scaleX": 0.7427, "scaleY": 0.7427, "width": 7, "height": 31}}, "12": {"11": {"x": 0.04, "y": 13.44, "width": 7, "height": 31}}, "13": {"11": {"x": 0.04, "y": 13.44, "width": 7, "height": 31}}, "14": {"11": {"x": 0.04, "y": 13.44, "scaleX": 0.7427, "scaleY": 0.7427, "width": 7, "height": 31}}, "15": {"11": {"x": 0.04, "y": 13.44, "width": 7, "height": 31}}, "16": {"11": {"x": 0.04, "y": 13.44, "scaleX": 0.7427, "scaleY": 0.7427, "width": 7, "height": 31}}, "17": {"11": {"x": 0.04, "y": 13.44, "width": 7, "height": 31}}, "18": {"11": {"x": 0.04, "y": 13.44, "scaleX": 0.7427, "scaleY": 0.7427, "width": 7, "height": 31}}, "19": {"11": {"x": 0.04, "y": 13.44, "width": 7, "height": 31}}, "20": {"11": {"x": 0.04, "y": 13.44, "scaleX": 0.7427, "scaleY": 0.7427, "width": 7, "height": 31}}}}], "animations": {"2": {"bones": {"10": {"rotate": [{"value": -5.05}]}}}, "animation": {"slots": {"3": {"attachment": [{"time": 0.2}, {"time": 0.5333, "name": "3"}]}, "4": {"attachment": [{"time": 0.2, "name": "sword"}, {"time": 0.5333, "name": "4"}]}, "4-add-black-line": {"attachment": [{"time": 0.2, "name": "sword2"}, {"time": 0.5667}]}, "5": {"attachment": [{"time": 0.2}, {"time": 0.5333, "name": "5"}]}, "6": {"attachment": [{"time": 0.2, "name": "sword2"}, {"time": 0.5333, "name": "6"}]}, "7": {"attachment": [{"time": 0.2}, {"time": 0.5333, "name": "7"}]}, "9": {"rgba": [{"time": 1.1, "color": "ffffffff"}, {"time": 1.5667, "color": "ffffff00"}], "attachment": [{"time": 0.6667, "name": "9"}]}, "10": {"attachment": [{"time": 0.6667, "name": "10"}]}, "11": {"attachment": [{"time": 0.5, "name": "11"}]}, "12": {"attachment": [{"time": 0.5, "name": "11"}]}, "13": {"attachment": [{"time": 0.5, "name": "11"}]}, "14": {"attachment": [{"time": 0.5, "name": "11"}]}, "15": {"attachment": [{"time": 0.5, "name": "11"}]}, "16": {"attachment": [{"time": 0.5, "name": "11"}]}, "17": {"attachment": [{"time": 0.5, "name": "11"}]}, "18": {"attachment": [{"time": 0.5, "name": "11"}]}, "19": {"attachment": [{"time": 0.5, "name": "11"}]}, "20": {"attachment": [{"time": 0.5, "name": "11"}]}}, "bones": {"17": {"rotate": [{"time": 0.6667, "curve": [1, 0, 1.333, 90]}, {"time": 1.6667, "value": 90}], "scale": [{"time": 0.6667, "curve": [0.811, 1.157, 0.956, 1.472, 0.811, 1.157, 0.956, 1.472]}, {"time": 1.1, "x": 1.472, "y": 1.472, "curve": [1.203, 1.472, 1.585, 0, 1.203, 1.472, 1.585, 0]}, {"time": 1.6667, "x": 0, "y": 0}]}, "16": {"scale": [{"time": 0.6667, "x": 0, "y": 0, "curve": [0.811, 0, 0.956, 1, 0.811, 0, 0.956, 1]}, {"time": 1.1}]}, "10": {"rotate": [{"curve": [0.044, 0, 0, 0.45]}, {"time": 0.1333, "value": 0.45, "curve": [0.244, 0.45, 0.133, -82.71]}, {"time": 0.4667, "value": -82.71, "curve": [0.511, -82.71, 0.467, 1.21]}, {"time": 0.6, "value": 1.21}, {"time": 0.7}]}, "bone2": {"rotate": [{"time": 0.4333, "value": -101.84}], "translate": [{"time": 0.4333, "x": -145.64, "y": -48.1, "curve": [0.433, -145.74, 0.447, -220.52, 0.433, -48.03, 0.447, -30.33]}, {"time": 0.8, "x": -220.52, "y": -30.33}], "scale": [{"time": 0.4333, "x": 0.2, "y": 0.2, "curve": [0.486, 0.2, 0.514, 2.153, 0.486, 0.2, 0.514, 1.201]}, {"time": 0.5667, "x": 2.153, "y": 1.201, "curve": [0.662, 2.153, 0.705, 0.5, 0.662, 1.201, 0.705, 0]}, {"time": 0.8, "x": 0.5, "y": 0}]}, "bone3": {"rotate": [{"time": 0.4333, "value": -71.47}, {"time": 0.7667, "value": -70.19}], "translate": [{"time": 0.4333, "x": -116.25, "y": -63.69}, {"time": 0.7667, "x": -64.82, "y": -43.74}], "scale": [{"time": 0.4333, "x": 0.2, "y": 0.2, "curve": [0.5, 0.2, 0.534, 1.394, 0.5, 0.2, 0.534, 1.209]}, {"time": 0.6, "x": 1.394, "y": 1.209, "curve": [0.668, 1.394, 0.699, 0.5, 0.668, 1.209, 0.699, 0]}, {"time": 0.7667, "x": 0.5, "y": 0}]}, "bone4": {"rotate": [{"time": 0.4333, "value": -160.64}], "translate": [{"time": 0.4333, "x": -132.73, "y": -58.35, "curve": [0.433, -132.79, 0.442, -172.26, 0.433, -58.3, 0.442, -2.64]}, {"time": 0.6667, "x": -172.26, "y": -2.64}], "scale": [{"time": 0.4333, "x": 0.2, "y": 0.2, "curve": [0.478, 0.2, 0.489, 1.407, 0.478, 0.2, 0.489, 1.276]}, {"time": 0.5333, "x": 1.407, "y": 1.276, "curve": [0.588, 1.407, 0.612, 0.5, 0.588, 1.276, 0.612, 0]}, {"time": 0.6667, "x": 0.5, "y": 0}]}, "bone5": {"rotate": [{"time": 0.4333, "value": -101.84}], "translate": [{"time": 0.4333, "x": -133.62, "y": -69.48, "curve": [0.433, -133.7, 0.445, -193.87, 0.433, -69.42, 0.445, -56.54]}, {"time": 0.7333, "x": -193.87, "y": -56.54}], "scale": [{"time": 0.4333, "x": 0.2, "y": 0.2, "curve": [0.486, 0.2, 0.514, 1.554, 0.486, 0.2, 0.514, 1.194]}, {"time": 0.5667, "x": 1.554, "y": 1.194, "curve": [0.635, 1.554, 0.665, 0.5, 0.635, 1.194, 0.665, 0]}, {"time": 0.7333, "x": 0.5, "y": 0}]}, "bone6": {"rotate": [{"time": 0.4333, "value": -101.84}, {"time": 0.7, "value": -122.88}], "translate": [{"time": 0.4333, "x": -131.46, "y": -80.72, "curve": [0.433, -131.54, 0.443, -78.51, 0.433, -80.67, 0.443, -105.22]}, {"time": 0.7, "x": -78.51, "y": -105.22}], "scale": [{"time": 0.4333, "x": 0.2, "y": 0.2, "curve": [0.466, 0.2, 0.467, 1.219, 0.466, 0.2, 0.467, 1.219]}, {"time": 0.5, "x": 1.219, "y": 1.219, "curve": [0.582, 1.219, 0.618, 0.5, 0.582, 1.219, 0.618, 0]}, {"time": 0.7, "x": 0.5, "y": 0}]}}}}}