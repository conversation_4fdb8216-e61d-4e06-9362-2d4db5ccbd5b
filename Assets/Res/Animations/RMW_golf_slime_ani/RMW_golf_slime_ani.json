{"skeleton": {"hash": "yD43wZ3q9EM", "spine": "4.1.11", "x": -162.73, "y": -19.13, "width": 239.09, "height": 235}, "bones": [{"name": "root"}, {"name": "slime", "parent": "root", "length": 117.1, "rotation": 91.09, "x": 0.05, "y": 1.24}, {"name": "slime2", "parent": "slime", "length": 21.6, "rotation": 29.88, "x": 117.1}, {"name": "slime3", "parent": "slime2", "length": 20.06, "rotation": 54.8, "x": 21.99, "y": 0.64}, {"name": "slime4", "parent": "slime3", "length": 22.89, "rotation": 33.29, "x": 20.06}, {"name": "hand", "parent": "root", "rotation": 91.09, "x": -55.61, "y": 44.76}, {"name": "Junior club", "parent": "hand", "length": 93.75, "rotation": 32.49, "x": 0.04, "y": 2.02}, {"name": "slime5", "parent": "slime", "x": 61.35, "y": -20.13}, {"name": "tx_LightThrom_108", "parent": "root", "x": 161.16, "y": 119.93}, {"name": "Junior club2", "parent": "root", "length": 93.75, "rotation": 123.58, "x": -57.63, "y": 44.76}], "slots": [{"name": "slime shadow", "bone": "root", "attachment": "slime shadow"}, {"name": "slime", "bone": "slime", "attachment": "slime"}, {"name": "eye", "bone": "slime5", "attachment": "open"}, {"name": "Junior club", "bone": "Junior club", "attachment": "Junior club"}, {"name": "Junior club2", "bone": "Junior club2"}, {"name": "hand", "bone": "hand", "attachment": "hand"}, {"name": "tx_LightThrom_108", "bone": "tx_LightThrom_108"}], "skins": [{"name": "default", "attachments": {"eye": {"close": {"x": -0.59, "y": -0.26, "rotation": -91.09, "width": 64, "height": 11}, "open": {"x": -2.09, "y": -0.23, "rotation": -91.09, "width": 62, "height": 52}}, "hand": {"hand": {"x": 16.3, "y": 10.31, "rotation": -91.09, "width": 83, "height": 98}}, "slime": {"slime": {"type": "mesh", "uvs": [0.44434, 0, 0.56967, 0.05541, 0.62049, 0.15451, 0.62057, 0.25105, 0.84995, 0.37647, 0.95671, 0.51556, 0.98792, 0.61674, 0.98721, 0.78594, 0.89002, 0.89063, 0.76704, 0.96217, 0.63188, 0.9878, 0.3237, 0.98792, 0.19151, 0.94027, 0.08501, 0.87395, 0.01273, 0.7698, 0.01208, 0.59165, 0.10643, 0.41086, 0.35358, 0.25321, 0.24476, 0.2164, 0.10399, 0.21627, 0.1355, 0.11938, 0.26555, 0.03123, 0.43285, 0, 0.48556, 0.25214, 0.47175, 0.17068, 0.38185, 0.13022, 0.25508, 0.13696, 0.51093, 0.65176, 0.28274, 0.55959, 0.73912, 0.53936, 0.35881, 0.8406, 0.71607, 0.83385], "triangles": [18, 26, 17, 18, 19, 26, 19, 20, 26, 26, 21, 25, 26, 20, 21, 24, 25, 0, 25, 22, 0, 25, 21, 22, 23, 24, 2, 24, 1, 2, 24, 0, 1, 17, 25, 24, 11, 30, 10, 11, 12, 30, 10, 31, 9, 10, 30, 31, 9, 31, 8, 12, 13, 30, 8, 31, 7, 30, 13, 28, 13, 14, 28, 30, 27, 31, 30, 28, 27, 7, 31, 6, 31, 27, 29, 6, 31, 29, 6, 29, 5, 27, 28, 23, 28, 17, 23, 14, 15, 28, 29, 23, 3, 29, 27, 23, 15, 16, 28, 28, 16, 17, 29, 4, 5, 29, 3, 4, 17, 24, 23, 3, 23, 2, 17, 26, 25], "vertices": [2, 2, 36.84, -13.54, 0.42752, 3, -3.02, -20.31, 0.57248, 3, 1, 146.43, -13.02, 0.01011, 2, 18.95, -25.9, 0.96668, 3, -23.43, -12.82, 0.0232, 2, 1, 130.23, -20.74, 0.23251, 2, 1.05, -24.52, 0.76749, 1, 1, 114.59, -20.45, 1, 1, 1, 93.59, -56.31, 1, 1, 1, 70.74, -72.74, 1, 1, 1, 54.26, -77.36, 1, 1, 1, 26.86, -76.73, 1, 1, 1, 10.19, -61.05, 1, 1, 1, -1.03, -41.41, 1, 1, 1, -4.77, -19.98, 1, 2, 1, -3.87, 28.71, 0.99952, 3, 4.17, 140.71, 0.00048, 1, 1, 4.25, 49.44, 1, 1, 1, 15.31, 66.06, 1, 1, 1, 32.39, 77.16, 1, 1, 1, 61.25, 76.72, 1, 1, 1, 90.25, 61.26, 1, 1, 1, 115.04, 21.73, 1, 3, 1, 121.33, 38.81, 0.014, 3, 25.84, 16.99, 0.16677, 4, 14.15, 11.03, 0.81923, 1, 4, 33.58, 0.21, 1, 1, 4, 21.61, -11.1, 1, 2, 3, 24.78, -13.17, 0.4525, 4, -3.29, -13.6, 0.5475, 2, 2, 37.77, -11.98, 0.37503, 3, -1.21, -20.17, 0.62497, 1, 1, 114.82, 0.88, 1, 1, 2, 10.9, -3.02, 1, 4, 1, 134.88, 16.88, 0.00189, 2, 23.83, 5.78, 0.00668, 3, 5.27, 1.46, 0.98895, 4, -11.56, 9.34, 0.00248, 3, 1, 134.17, 36.93, 0.0004, 3, 25.16, 4.03, 0.00845, 4, 6.48, 0.57, 0.99115, 1, 1, 50.02, -1.9, 1, 1, 1, 65.63, 33.86, 1, 1, 1, 67.54, -38.3, 1, 1, 1, 19.89, 22.71, 1, 1, 1, 19.91, -33.75, 1], "hull": 23}}, "slime shadow": {"slime shadow": {"x": 2.27, "y": -7.13, "width": 138, "height": 24}}, "tx_LightThrom_108": {"tx_LightThrom_108": {"x": -0.69, "y": -1.53, "scaleX": 1.6677, "scaleY": 1.6677, "width": 128, "height": 128}}}}, {"name": "01", "attachments": {"Junior club": {"Junior club": {"x": 70.18, "y": -7.47, "rotation": -123.58, "width": 145, "height": 217}}, "Junior club2": {"Junior club": {"x": 70.18, "y": -7.47, "rotation": -123.58, "width": 145, "height": 217}}}}, {"name": "02", "attachments": {"Junior club": {"Junior club": {"name": "intermediate club", "x": 70.18, "y": -7.47, "rotation": -123.58, "width": 151, "height": 220}}, "Junior club2": {"Junior club": {"name": "intermediate club", "x": 70.18, "y": -7.47, "rotation": -123.58, "width": 151, "height": 220}}}}, {"name": "03", "attachments": {"Junior club": {"Junior club": {"name": "Advanced Clubs", "x": 70.18, "y": -7.47, "rotation": -123.58, "width": 151, "height": 221}}, "Junior club2": {"Junior club": {"name": "Advanced Clubs2", "path": "Advanced Clubs", "x": 70.18, "y": -7.47, "rotation": -123.58, "width": 151, "height": 221}}}}], "animations": {"idle": {"bones": {"slime": {"rotate": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}], "translate": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}], "scale": [{"x": 1.018, "y": 0.982, "curve": [0.167, 1.018, 0.333, 0.973, 0.167, 0.982, 0.333, 1.018]}, {"time": 0.5, "x": 0.973, "y": 1.018, "curve": [0.667, 0.973, 0.833, 1.018, 0.667, 1.018, 0.833, 0.982]}, {"time": 1, "x": 1.018, "y": 0.982}], "shear": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}]}, "hand": {"rotate": [{"value": 0.27, "curve": [0.034, 0.11, 0.067, 0]}, {"time": 0.1, "curve": [0.267, 0, 0.433, 2.62]}, {"time": 0.6, "value": 2.62, "curve": [0.734, 2.62, 0.868, 0.94]}, {"time": 1, "value": 0.27}], "translate": [{"y": -0.46, "curve": [0.034, 0, 0.067, 0, 0.034, -0.19, 0.067, 0]}, {"time": 0.1, "curve": [0.267, 0, 0.433, 0, 0.267, 0, 0.433, -4.43]}, {"time": 0.6, "y": -4.43, "curve": [0.734, 0, 0.868, 0, 0.734, -4.43, 0.868, -1.58]}, {"time": 1, "y": -0.46}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 1}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 1}]}, "Junior club": {"rotate": [{"value": 0.62, "curve": [0.112, 0.37, 0.223, 0]}, {"time": 0.3333, "curve": [0.5, 0, 0.667, 0.84]}, {"time": 0.8333, "value": 0.84, "curve": [0.889, 0.84, 0.946, 0.75]}, {"time": 1, "value": 0.62}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1}]}, "slime2": {"rotate": [{"value": -4.55, "curve": [0.167, -4.55, 0.333, 1.55]}, {"time": 0.5, "value": 1.55, "curve": [0.667, 1.55, 0.833, -4.55]}, {"time": 1, "value": -4.55}], "translate": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}], "scale": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}], "shear": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}]}, "slime3": {"rotate": [{"value": -4.95, "curve": [0.046, -6.16, 0.089, -7.07]}, {"time": 0.1333, "value": -7.07, "curve": [0.3, -7.07, 0.467, 4.83]}, {"time": 0.6333, "value": 4.83, "curve": [0.756, 4.83, 0.879, -1.54]}, {"time": 1, "value": -4.95}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 1}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 1}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 1}]}, "slime4": {"rotate": [{"value": -1.45, "curve": [0.09, -5.65, 0.178, -10.18]}, {"time": 0.2667, "value": -10.18, "curve": [0.433, -10.18, 0.6, 5.71]}, {"time": 0.7667, "value": 5.71, "curve": [0.845, 5.71, 0.923, 2.27]}, {"time": 1, "value": -1.45}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 1}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 1}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 1}]}, "slime5": {"rotate": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}], "translate": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}], "scale": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}], "shear": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}]}, "Junior club2": {"rotate": [{"value": 0.62, "curve": [0.112, 0.37, 0.223, 0]}, {"time": 0.3333, "curve": [0.5, 0, 0.667, 0.84]}, {"time": 0.8333, "value": 0.84, "curve": [0.889, 0.84, 0.946, 0.75]}, {"time": 1, "value": 0.62}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1}]}}}, "lose": {"slots": {"eye": {"attachment": [{"time": 0.2333, "name": "close"}, {"time": 0.4, "name": "open"}, {"time": 0.8, "name": "close"}]}, "Junior club": {"attachment": [{"time": 0.6667}]}, "Junior club2": {"attachment": [{"time": 0.6667, "name": "Junior club"}, {"time": 1.6}]}, "tx_LightThrom_108": {"rgba": [{"time": 0.7667, "color": "ffffffff", "curve": [0.789, 1, 0.811, 1, 0.789, 1, 0.811, 1, 0.789, 1, 0.811, 1, 0.789, 1, 0.828, 0.17]}, {"time": 0.8333, "color": "ffffff00"}]}}, "bones": {"hand": {"rotate": [{"curve": [0.078, 0, 0.156, -14.89]}, {"time": 0.2333, "value": -14.89, "curve": [0.3, -14.89, 0.367, 1.64]}, {"time": 0.4333, "value": 1.64, "curve": [0.511, 1.64, 0.589, -32.16]}, {"time": 0.6667, "value": -32.16, "curve": [0.7, -32.16, 0.72, 28.94]}, {"time": 0.7667, "value": 29.54, "curve": [0.828, 30.33, 1.167, 0]}, {"time": 1.3667}], "translate": [{"curve": "stepped"}, {"time": 0.7, "curve": [0.722, 24.46, 0.744, 59.66, 0.722, 22.55, 0.744, 40.22]}, {"time": 0.7667, "x": 59.66, "y": 40.22, "curve": [0.844, 59.66, 0.922, 14.27, 0.844, 40.22, 0.922, 13.16]}, {"time": 1}], "scale": [{"curve": "stepped"}, {"time": 1.3667}], "shear": [{"curve": "stepped"}, {"time": 1.3667}]}, "Junior club": {"rotate": [{"curve": "stepped"}, {"time": 0.6667, "curve": [0.707, 127.69, 0.889, 543.77]}, {"time": 1, "value": 543.77}], "translate": [{"curve": "stepped"}, {"time": 0.6667, "curve": [0.707, 222.08, 0.889, 945.74, 0.707, -158.69, 0.889, -675.82]}, {"time": 1, "x": 945.74, "y": -675.82}], "scale": [{"curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.6667}]}, "slime": {"rotate": [{"curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2333}], "translate": [{"curve": "stepped"}, {"time": 0.6667, "curve": [0.722, 0, 0.778, 5.56, 0.722, 0, 0.778, 12.98]}, {"time": 0.8333, "x": 5.56, "y": 12.98, "curve": [0.878, 5.56, 0.922, 0, 0.878, 12.98, 0.922, 0]}, {"time": 0.9667, "curve": [1.011, 0, 1.056, 0, 1.011, 0, 1.056, 2.3]}, {"time": 1.1, "y": 2.3, "curve": [1.144, 0, 1.189, 0, 1.144, 2.3, 1.189, 0.77]}, {"time": 1.2333}], "scale": [{"curve": [0.067, 1, 0.133, 1.049, 0.067, 1, 0.133, 0.952]}, {"time": 0.2, "x": 1.049, "y": 0.952, "curve": [0.278, 1.049, 0.356, 0.974, 0.278, 0.952, 0.356, 1.048]}, {"time": 0.4333, "x": 0.974, "y": 1.048, "curve": [0.511, 0.974, 0.589, 1, 0.511, 1.048, 0.589, 1]}, {"time": 0.6667, "curve": [0.722, 1, 0.778, 1.056, 0.722, 1, 0.778, 0.926]}, {"time": 0.8333, "x": 1.056, "y": 0.926, "curve": [0.844, 1.056, 0.856, 1.056, 0.844, 0.926, 0.856, 0.926]}, {"time": 0.8667, "x": 1.049, "y": 0.934, "curve": [0.9, 1.026, 0.933, 0.931, 0.9, 0.958, 0.933, 1.046]}, {"time": 0.9667, "x": 0.931, "y": 1.046, "curve": [1.011, 0.931, 1.056, 1.012, 1.011, 1.046, 1.056, 0.988]}, {"time": 1.1, "x": 1.012, "y": 0.988, "curve": [1.144, 1.012, 1.189, 1.004, 1.144, 0.988, 1.189, 0.996]}, {"time": 1.2333}], "shear": [{"curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2333}]}, "slime2": {"rotate": [{}, {"time": 0.2333, "value": -8.72}, {"time": 0.6667, "curve": [0.744, 0, 0.822, -9.86]}, {"time": 0.9, "value": -9.86, "curve": [0.944, -9.86, 0.989, 16.49]}, {"time": 1.0333, "value": 16.49, "curve": [1.078, 16.49, 1.122, -0.07]}, {"time": 1.1667, "value": -0.07, "curve": [1.211, -0.07, 1.256, -0.02]}, {"time": 1.3}], "translate": [{"curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1.3}], "scale": [{"curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1.3}], "shear": [{"curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1.3}]}, "slime3": {"rotate": [{}, {"time": 0.4, "value": -8.72}, {"time": 0.7667, "curve": [0.844, 0, 0.922, -18.9]}, {"time": 1, "value": -18.9, "curve": [1.044, -18.9, 1.089, 9.87]}, {"time": 1.1333, "value": 9.87, "curve": [1.178, 9.87, 1.222, -4.29]}, {"time": 1.2667, "value": -4.29, "curve": [1.311, -4.29, 1.356, -1.43]}, {"time": 1.4}], "translate": [{"curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.4}], "scale": [{"curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.4}], "shear": [{"curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.4}]}, "slime4": {"rotate": [{}, {"time": 0.5333, "value": -8.72}, {"time": 0.8333, "curve": [0.911, 0, 0.989, -11.58]}, {"time": 1.0667, "value": -11.58, "curve": [1.111, -11.58, 1.156, 15.23]}, {"time": 1.2, "value": 15.23, "curve": [1.244, 15.23, 1.289, -0.87]}, {"time": 1.3333, "value": -0.87, "curve": [1.378, -0.87, 1.422, -0.29]}, {"time": 1.4667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.4667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.4667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.4667}]}, "slime5": {"rotate": [{"curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.6667}]}, "tx_LightThrom_108": {"scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7333, "x": 0, "y": 0, "curve": [0.744, 0, 0.756, 1, 0.744, 0, 0.756, 1]}, {"time": 0.7667, "curve": [0.789, 1, 0.811, 1.827, 0.789, 1, 0.811, 1.827]}, {"time": 0.8333, "x": 1.827, "y": 1.827}]}, "Junior club2": {"rotate": [{"value": -32.11, "curve": "stepped"}, {"time": 0.6667, "value": -32.11, "curve": [0.749, 127.61, 1.266, 759.12]}, {"time": 1.6, "value": 1034.44}], "translate": [{"curve": "stepped"}, {"time": 0.6667, "curve": [0.963, 175.46, 1.302, 514.68, 0.941, 611.01, 1.289, 995]}, {"time": 1.6, "x": 751.46, "y": 995}], "scale": [{"curve": "stepped"}, {"time": 0.6667}, {"time": 1.6, "x": 0.439, "y": 0.439}], "shear": [{"curve": "stepped"}, {"time": 0.6667}]}}}, "win": {"slots": {"eye": {"attachment": [{"time": 0.7667, "name": "close"}, {"time": 0.9333, "name": "open"}]}, "tx_LightThrom_108": {"rgba": [{"time": 0.7667, "color": "ffffffff", "curve": [0.789, 1, 0.811, 1, 0.789, 1, 0.811, 1, 0.789, 1, 0.811, 1, 0.789, 1, 0.828, 0.17]}, {"time": 0.8333, "color": "ffffff00"}], "attachment": [{"name": "tx_LightThrom_108"}, {"time": 0.7333, "name": "tx_LightThrom_108"}]}}, "bones": {"hand": {"rotate": [{"curve": [0.078, 0, 0.156, -14.89]}, {"time": 0.2333, "value": -14.89, "curve": [0.3, -14.89, 0.367, 1.64]}, {"time": 0.4333, "value": 1.64, "curve": [0.511, 1.64, 0.589, -32.16]}, {"time": 0.6667, "value": -32.16, "curve": [0.7, -32.16, 0.733, 192.77]}, {"time": 0.7667, "value": 259.24, "curve": [0.783, 291.54, 0.923, 467.37]}, {"time": 0.9333, "value": 467.37}], "translate": [{"curve": "stepped"}, {"time": 0.7, "curve": [0.72, 6, 0.759, 50.96, 0.72, 1.78, 0.758, 4.13]}, {"time": 0.7667, "x": 56.92, "y": 9.27, "curve": [0.784, 70.5, 0.807, 116.35, 0.781, 17.84, 0.806, 24.94]}, {"time": 0.8333, "x": 105.39, "y": 34.57, "curve": [0.865, 92.49, 0.899, 19.47, 0.865, 45.46, 0.9, 74.99]}, {"time": 0.9333, "x": 19.47, "y": 74.99}], "scale": [{"curve": "stepped"}, {"time": 1.3667}], "shear": [{"curve": "stepped"}, {"time": 1.3667}]}, "Junior club": {"rotate": [{"curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.6667}]}, "slime": {"rotate": [{"curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2333}], "translate": [{"curve": "stepped"}, {"time": 0.6667, "curve": [0.722, 0, 0.778, 5.56, 0.722, 0, 0.778, 12.98]}, {"time": 0.8333, "x": 5.56, "y": 12.98, "curve": [0.878, 5.56, 0.922, 0, 0.878, 12.98, 0.922, 0]}, {"time": 0.9667, "curve": [1.011, 0, 1.056, 0, 1.011, 0, 1.056, 2.3]}, {"time": 1.1, "y": 2.3, "curve": [1.144, 0, 1.189, 0, 1.144, 2.3, 1.189, 0.77]}, {"time": 1.2333}], "scale": [{"curve": [0.067, 1, 0.133, 1.049, 0.067, 1, 0.133, 0.952]}, {"time": 0.2, "x": 1.049, "y": 0.952, "curve": [0.278, 1.049, 0.356, 0.974, 0.278, 0.952, 0.356, 1.048]}, {"time": 0.4333, "x": 0.974, "y": 1.048, "curve": [0.511, 0.974, 0.589, 1, 0.511, 1.048, 0.589, 1]}, {"time": 0.6667, "curve": [0.722, 1, 0.778, 1.056, 0.722, 1, 0.778, 0.926]}, {"time": 0.8333, "x": 1.056, "y": 0.926, "curve": [0.844, 1.056, 0.856, 1.056, 0.844, 0.926, 0.856, 0.926]}, {"time": 0.8667, "x": 1.049, "y": 0.934, "curve": [0.9, 1.026, 0.933, 0.931, 0.9, 0.958, 0.933, 1.046]}, {"time": 0.9667, "x": 0.931, "y": 1.046, "curve": [1.011, 0.931, 1.056, 1.012, 1.011, 1.046, 1.056, 0.988]}, {"time": 1.1, "x": 1.012, "y": 0.988, "curve": [1.144, 1.012, 1.189, 1.004, 1.144, 0.988, 1.189, 0.996]}, {"time": 1.2333}], "shear": [{"curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2333}]}, "slime2": {"rotate": [{}, {"time": 0.2333, "value": -8.72}, {"time": 0.6667, "curve": [0.744, 0, 0.822, -9.86]}, {"time": 0.9, "value": -9.86, "curve": [0.944, -9.86, 0.989, 16.49]}, {"time": 1.0333, "value": 16.49, "curve": [1.078, 16.49, 1.122, -0.07]}, {"time": 1.1667, "value": -0.07, "curve": [1.211, -0.07, 1.256, -0.02]}, {"time": 1.3}], "translate": [{"curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1.3}], "scale": [{"curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1.3}], "shear": [{"curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1.3}]}, "slime3": {"rotate": [{}, {"time": 0.4, "value": -8.72}, {"time": 0.7667, "curve": [0.844, 0, 0.922, -18.9]}, {"time": 1, "value": -18.9, "curve": [1.044, -18.9, 1.089, 9.87]}, {"time": 1.1333, "value": 9.87, "curve": [1.178, 9.87, 1.222, -4.29]}, {"time": 1.2667, "value": -4.29, "curve": [1.311, -4.29, 1.356, -1.43]}, {"time": 1.4}], "translate": [{"curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.4}], "scale": [{"curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.4}], "shear": [{"curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.4}]}, "slime4": {"rotate": [{}, {"time": 0.5333, "value": -8.72}, {"time": 0.8333, "curve": [0.911, 0, 0.989, -11.58]}, {"time": 1.0667, "value": -11.58, "curve": [1.111, -11.58, 1.156, 15.23]}, {"time": 1.2, "value": 15.23, "curve": [1.244, 15.23, 1.289, -0.87]}, {"time": 1.3333, "value": -0.87, "curve": [1.378, -0.87, 1.422, -0.29]}, {"time": 1.4667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.4667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.4667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.4667}]}, "slime5": {"rotate": [{"curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.6667}]}, "tx_LightThrom_108": {"scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7333, "x": 0, "y": 0, "curve": [0.744, 0, 0.756, 1, 0.744, 0, 0.756, 1]}, {"time": 0.7667, "curve": [0.789, 1, 0.811, 1.827, 0.789, 1, 0.811, 1.827]}, {"time": 0.8333, "x": 1.827, "y": 1.827}]}, "Junior club2": {"rotate": [{"curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.6667}]}}, "drawOrder": [{"time": 0.8333, "offsets": [{"slot": "Junior club", "offset": -2}, {"slot": "hand", "offset": -3}]}]}}}