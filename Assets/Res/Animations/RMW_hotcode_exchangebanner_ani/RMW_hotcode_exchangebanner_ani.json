{"skeleton": {"hash": "pI8vIGdwRHg", "spine": "4.1.11", "x": -529.59, "y": -0.61, "width": 1059.24, "height": 499.88}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root"}, {"name": "N", "parent": "bone", "x": -305.35, "y": 94.94}, {"name": "N-a", "parent": "N", "x": 0.46, "y": 23.48}, {"name": "F", "parent": "bone", "x": -76.92, "y": 155.06}, {"name": "F-c", "parent": "F", "length": 31.81, "rotation": -169.59, "x": -17.88, "y": 7.02}, {"name": "F-a", "parent": "F", "x": -10.17, "y": 50.42}, {"name": "W", "parent": "bone", "x": 322.77, "y": 150.9}, {"name": "W-a", "parent": "W", "x": 4.23, "y": 45.31}, {"name": "W-f", "parent": "W", "x": 3.02, "y": 19.33}, {"name": "W-f2", "parent": "W-f", "length": 38.47, "rotation": 169.34, "x": -9.48, "y": 2.07}, {"name": "W-f3", "parent": "W-f2", "length": 46.5, "rotation": -4.31, "x": 38.99, "y": -0.35}, {"name": "W-f4", "parent": "W-f", "length": 61.27, "rotation": -9.19, "x": 11.87, "y": -0.15}, {"name": "W-f5", "parent": "W-f4", "length": 55.65, "rotation": -1.4, "x": 61.27}, {"name": "coin01", "parent": "bone", "x": 3.3, "y": 229.91}, {"name": "coin02", "parent": "bone", "x": 2.6, "y": 260.07}, {"name": "tutem", "parent": "bone", "x": 79.04, "y": 1.02}, {"name": "star", "parent": "tutem", "x": -68.93, "y": 113.71}, {"name": "F-d", "parent": "F", "x": -11.1, "y": 13.47}, {"name": "F-d2", "parent": "F-d", "length": 26.02, "rotation": -85.49, "x": 10.92, "y": -15.7}, {"name": "F-d3", "parent": "F-d2", "length": 25.96, "rotation": -2.25, "x": 26.02}, {"name": "F-d4", "parent": "F-d3", "length": 23.23, "rotation": -4.79, "x": 25.96}, {"name": "F2", "parent": "F", "rotation": 36.81, "x": 1.23, "y": -0.84}, {"name": "N-c", "parent": "N", "length": 34.75, "rotation": -172.57, "x": -37.57, "y": 24.63}, {"name": "N-c2", "parent": "N-c", "length": 32.69, "rotation": -9.53, "x": 34.75}, {"name": "N-c3", "parent": "N-c2", "length": 38.75, "rotation": 52.75, "x": 32.69}, {"name": "F-c2", "parent": "F-c", "length": 46.43, "rotation": -35, "x": 34.38, "y": -2.12}, {"name": "F-c3", "parent": "F-c2", "length": 34.92, "rotation": -19.58, "x": 46.43}, {"name": "F-c7", "parent": "F-c3", "length": 40.3, "rotation": 60.67, "x": 34.92}, {"name": "F-c8", "parent": "F-c7", "length": 42.13, "rotation": 4.39, "x": 40.3}, {"name": "F-c4", "parent": "F2", "length": 31.81, "rotation": 153.6, "x": -10.59, "y": 17.75, "scaleX": -1}, {"name": "F-c5", "parent": "F-c4", "length": 46.43, "rotation": -35, "x": 34.38, "y": -2.12}, {"name": "F-c6", "parent": "F-c5", "length": 34.92, "rotation": -19.58, "x": 46.43}, {"name": "F-c9", "parent": "F-c6", "length": 40.3, "rotation": 60.67, "x": 34.92}, {"name": "F-c10", "parent": "F-c9", "length": 42.13, "rotation": 4.39, "x": 40.3}, {"name": "N2", "parent": "N", "rotation": 19.85, "x": 0.71, "y": 11.5, "scaleX": -1}, {"name": "N-c4", "parent": "N2", "length": 34.75, "rotation": -172.57, "x": -38.28, "y": 13.13}, {"name": "N-c5", "parent": "N-c4", "length": 32.69, "rotation": -9.53, "x": 34.75}, {"name": "N-c6", "parent": "N-c5", "length": 38.75, "rotation": 52.75, "x": 32.69}, {"name": "star2", "parent": "tutem", "x": 38.16, "y": 119.91}, {"name": "Snow", "parent": "bone", "y": 203.55}, {"name": "snow-1", "parent": "Snow", "x": 370.97, "y": 266.28}, {"name": "snow-2", "parent": "Snow", "x": -221.38, "y": 193.1}, {"name": "snow-3", "parent": "Snow", "x": 71.31, "y": 119.93}, {"name": "snow-4", "parent": "Snow", "x": 545.2, "y": 60.69}, {"name": "snow-5", "parent": "Snow", "x": 287.35, "y": -89.14}, {"name": "snow-6", "parent": "Snow", "x": 237.81, "y": 306.23}, {"name": "snow-7", "parent": "Snow", "x": 191.76, "y": 282.5}, {"name": "snow-8", "parent": "Snow", "x": 55.02, "y": 250.41}, {"name": "snow-9", "parent": "Snow", "x": -148.7, "y": 138.79}, {"name": "snow-10", "parent": "Snow", "x": -353.81, "y": -57.96}, {"name": "snow-11", "parent": "Snow", "x": -451.48, "y": 22.97}, {"name": "snow-12", "parent": "Snow", "x": 500.98, "y": 15.83}, {"name": "snow-13", "parent": "Snow", "x": 508.12, "y": 177.75}, {"name": "snow-14", "parent": "Snow", "x": 317.63, "y": -48.46}, {"name": "snow-15", "parent": "Snow", "x": -313.9, "y": 305.51}, {"name": "snow-16", "parent": "Snow", "x": -456.58, "y": 191.37}, {"name": "snow-17", "parent": "Snow", "x": 71.34, "y": -54.76}, {"name": "snow-18", "parent": "Snow", "x": 531.49, "y": 309.08}, {"name": "snow-19", "parent": "Snow", "x": 306.77, "y": 309.08}, {"name": "snow-20", "parent": "Snow", "x": 231.86, "y": 309.08, "scaleX": 0.7428, "scaleY": 0.7428}, {"name": "snow-21", "parent": "Snow", "x": -189.05, "y": 312.65, "scaleX": 0.7428, "scaleY": 0.7428}, {"name": "snow-22", "parent": "Snow", "x": 479.82, "y": 314.06}, {"name": "snow-24", "parent": "Snow", "x": -343.17, "y": 314.06}, {"name": "tx_Point_070", "parent": "coin01", "x": -174.96, "y": -126.67, "scaleX": 0.6915, "scaleY": 0.6915}, {"name": "tx_Point_071", "parent": "coin01", "x": -472.38, "y": -153.59, "scaleX": 0.6915, "scaleY": 0.6915}, {"name": "tx_Point_072", "parent": "coin01", "x": 359.89, "y": -209.75, "scaleX": 0.4818, "scaleY": 0.4818}, {"name": "tx_Point_073", "parent": "coin01", "x": -233.29, "y": -181.68, "scaleX": 0.4993, "scaleY": 0.4993}, {"name": "tx_Point_074", "parent": "coin01", "rotation": -29.66, "x": -504.37, "y": 178.96, "scaleX": 0.4702, "scaleY": 0.4702}, {"name": "tx_Point_075", "parent": "coin02", "x": 159.83, "y": 41.91, "scaleX": 0.4564, "scaleY": 0.4564}, {"name": "tx_Point_076", "parent": "coin02", "rotation": -13.58, "x": -433.95, "y": 72.84, "scaleX": 0.418, "scaleY": 0.418}, {"name": "tx_Point_077", "parent": "bone", "x": -274.31, "y": 397.73, "scaleX": 0.4733, "scaleY": 0.4733}, {"name": "tx_Point_078", "parent": "bone", "x": -453.31, "y": 445.92, "scaleX": 0.3797, "scaleY": 0.3797}, {"name": "tx_Point_079", "parent": "bone", "x": 283.32, "y": 459.69, "scaleX": 0.3231, "scaleY": 0.3231}, {"name": "tx_Point_080", "parent": "bone", "x": 458.37, "y": 272.43, "scaleX": 0.3797, "scaleY": 0.3797}, {"name": "tx_Point_081", "parent": "bone", "x": 494.38, "y": 401.73, "scaleX": 0.3797, "scaleY": 0.3797}, {"name": "tx_Point_082", "parent": "bone", "x": -411.86, "y": 276.57, "scaleX": 0.3797, "scaleY": 0.3797}], "slots": [{"name": "bg1", "bone": "root"}, {"name": "root", "bone": "root", "attachment": "root"}, {"name": "BG", "bone": "root"}, {"name": "F-d", "bone": "F-d", "attachment": "F-d"}, {"name": "F-c", "bone": "F-c", "attachment": "F-c"}, {"name": "F-c2", "bone": "F-c4", "attachment": "F-c"}, {"name": "F-b", "bone": "F", "attachment": "F-b"}, {"name": "F-a", "bone": "F-a", "attachment": "F-a"}, {"name": "W-f", "bone": "W-f", "attachment": "W-f"}, {"name": "W-e", "bone": "W", "attachment": "W-e"}, {"name": "W-d", "bone": "W", "attachment": "W-d"}, {"name": "W-c", "bone": "W", "attachment": "W-c"}, {"name": "W-b", "bone": "W", "attachment": "W-b"}, {"name": "W-a", "bone": "W-a", "attachment": "W-a"}, {"name": "N-c", "bone": "N-c", "attachment": "N-c"}, {"name": "N-c2", "bone": "N-c4", "attachment": "N-c"}, {"name": "N-b", "bone": "N", "attachment": "N-b"}, {"name": "N-a", "bone": "N-a", "attachment": "N-a"}, {"name": "tutem", "bone": "tutem", "attachment": "tutem"}, {"name": "star", "bone": "star", "attachment": "star"}, {"name": "star2", "bone": "star2", "attachment": "star"}, {"name": "card-g", "bone": "coin02", "attachment": "card-g"}, {"name": "card-f", "bone": "coin01", "attachment": "card-f"}, {"name": "card-e", "bone": "coin02", "attachment": "card-e"}, {"name": "card-d", "bone": "coin01", "attachment": "card-d"}, {"name": "card-c", "bone": "coin02", "attachment": "card-c"}, {"name": "card-a", "bone": "coin01", "attachment": "card-a"}, {"name": "gloden-e", "bone": "coin01", "attachment": "gloden-e"}, {"name": "gloden-d", "bone": "coin02", "attachment": "gloden-d"}, {"name": "gloden-b", "bone": "coin02", "attachment": "gloden-b"}, {"name": "card-h", "bone": "coin01", "attachment": "card-h"}, {"name": "bg-qian", "bone": "bone"}, {"name": "gloden-c", "bone": "coin01", "attachment": "gloden-c"}, {"name": "coin-f", "bone": "coin01", "attachment": "coin-f"}, {"name": "coin-k", "bone": "coin01", "attachment": "coin-k"}, {"name": "coin-d", "bone": "coin02", "attachment": "coin-d"}, {"name": "coin-c", "bone": "coin02", "attachment": "coin-c"}, {"name": "coin-a", "bone": "coin01", "attachment": "coin-a"}, {"name": "coin-h", "bone": "coin02", "attachment": "coin-h"}, {"name": "mask", "bone": "bone"}, {"name": "snow-1", "bone": "snow-1", "attachment": "snow-1"}, {"name": "snow-24", "bone": "snow-24", "attachment": "snow-1"}, {"name": "snow-22", "bone": "snow-22", "attachment": "snow-3"}, {"name": "snow-20", "bone": "snow-20", "attachment": "snow-1"}, {"name": "snow-21", "bone": "snow-21", "attachment": "snow-1"}, {"name": "snow-15", "bone": "snow-15", "attachment": "snow-1"}, {"name": "snow-17", "bone": "snow-17", "attachment": "snow-4"}, {"name": "snow-18", "bone": "snow-18", "attachment": "snow-1"}, {"name": "snow-19", "bone": "snow-19", "attachment": "snow-1"}, {"name": "snow-16", "bone": "snow-16", "attachment": "snow-3"}, {"name": "snow-6", "bone": "snow-6", "attachment": "snow-2"}, {"name": "snow-7", "bone": "snow-7", "attachment": "snow-2"}, {"name": "snow-8", "bone": "snow-8", "attachment": "snow-2"}, {"name": "snow-9", "bone": "snow-9", "attachment": "snow-2"}, {"name": "snow-10", "bone": "snow-10", "attachment": "snow-2"}, {"name": "snow-11", "bone": "snow-11", "attachment": "snow-4"}, {"name": "snow-12", "bone": "snow-12", "attachment": "snow-3"}, {"name": "snow-13", "bone": "snow-13", "attachment": "snow-2"}, {"name": "snow-14", "bone": "snow-14", "attachment": "snow-4"}, {"name": "snow-2", "bone": "snow-2", "attachment": "snow-1"}, {"name": "snow-3", "bone": "snow-3", "attachment": "snow-1"}, {"name": "snow-4", "bone": "snow-4", "attachment": "snow-1"}, {"name": "snow-5", "bone": "snow-5", "attachment": "snow-1"}, {"name": "tx_Point_070", "bone": "tx_Point_070", "color": "f9e0ffff", "attachment": "tx_Point_070"}, {"name": "tx_Point_077", "bone": "tx_Point_077", "color": "cffaffff", "attachment": "tx_Point_070"}, {"name": "tx_Point_078", "bone": "tx_Point_078", "color": "80f0fdb9", "attachment": "tx_Point_070"}, {"name": "tx_Point_080", "bone": "tx_Point_080", "color": "cffaffff", "attachment": "tx_Point_070"}, {"name": "tx_Point_081", "bone": "tx_Point_081", "color": "cffaffdc", "attachment": "tx_Point_070"}, {"name": "tx_Point_082", "bone": "tx_Point_082", "color": "cffaffff", "attachment": "tx_Point_070"}, {"name": "tx_Point_079", "bone": "tx_Point_079", "color": "86f2ffcf", "attachment": "tx_Point_070"}, {"name": "tx_Point_075", "bone": "tx_Point_075", "color": "fffde0ff", "attachment": "tx_Point_070"}, {"name": "tx_Point_076", "bone": "tx_Point_076", "color": "fae0ffff", "attachment": "tx_Point_070"}, {"name": "tx_Point_073", "bone": "tx_Point_073", "color": "fffef1ff", "attachment": "tx_Point_070"}, {"name": "tx_Point_074", "bone": "tx_Point_074", "color": "fff7c2ff", "attachment": "tx_Point_070"}, {"name": "tx_Point_071", "bone": "tx_Point_071", "color": "f9e0ffff", "attachment": "tx_Point_070"}, {"name": "tx_Point_072", "bone": "tx_Point_072", "color": "f9e0ffff", "attachment": "tx_Point_070"}, {"name": "top", "bone": "root"}], "skins": [{"name": "default", "attachments": {"card-a": {"card-a": {"x": 249.2, "y": 158.09, "width": 89, "height": 100}}, "card-c": {"card-c": {"x": -513.6, "y": -113.57, "width": 108, "height": 115}}, "card-d": {"card-d": {"x": 160.2, "y": -24.91, "width": 77, "height": 64}}, "card-e": {"card-e": {"x": -342.1, "y": 150.93, "width": 83, "height": 86}}, "card-f": {"card-f": {"x": 351.7, "y": 147.09, "width": 30, "height": 76}}, "card-g": {"card-g": {"x": -558.1, "y": 168.43, "width": 43, "height": 39}}, "card-h": {"card-h": {"x": 431.2, "y": 198.59, "width": 63, "height": 67}}, "coin-a": {"coin-a": {"x": -496.8, "y": 166.59, "width": 81, "height": 69}}, "coin-c": {"coin-c": {"x": 340.4, "y": 176.43, "width": 52, "height": 49}}, "coin-d": {"coin-d": {"x": 173.4, "y": 13.43, "width": 72, "height": 69}}, "coin-f": {"coin-f": {"x": -234.3, "y": -200.91, "width": 86, "height": 60}}, "coin-h": {"coin-h": {"x": 61.9, "y": 110.43, "width": 825, "height": 197}}, "coin-k": {"coin-k": {"x": 538.2, "y": 159.59, "width": 59, "height": 59}}, "F-a": {"F-a": {"x": -19.91, "y": 78.54, "width": 244, "height": 209}}, "F-b": {"F-b": {"x": -13.58, "y": -16.04, "width": 161, "height": 157}}, "F-c": {"F-c": {"type": "mesh", "uvs": [0.40675, 0, 0.41549, 0, 0.4994, 0.04115, 0.57969, 0.09675, 0.68964, 0.19804, 0.76466, 0.31959, 0.7998, 0.31913, 0.87172, 0.28254, 0.89032, 0.31101, 0.94073, 0.43275, 0.98607, 0.58891, 0.98691, 0.62445, 0.90405, 0.69604, 0.77343, 0.75675, 0.79271, 0.83055, 0.79149, 0.86663, 0.73034, 0.86472, 0.66224, 0.83386, 0.60484, 0.79923, 0.59286, 0.98523, 0.58466, 1, 0.58088, 1, 0.48922, 0.91977, 0.35745, 0.72497, 0.33467, 0.77358, 0.28853, 0.88417, 0.27327, 0.88555, 0.26177, 0.86976, 0.2335, 0.75207, 0.19018, 0.48572, 0.17079, 0.46677, 0.09576, 0.40372, 0.00916, 0.35365, 0.00365, 0.33715, 0.0047, 0.31355, 0.03784, 0.29116, 0.18903, 0.21254, 0.36547, 0.14952, 0.46461, 0.12292, 0.39666, 0.03205, 0.51665, 0.25002, 0.38222, 0.32785, 0.29179, 0.42938, 0.58264, 0.32785, 0.46044, 0.46322, 0.38222, 0.62228, 0.66085, 0.37185, 0.61442, 0.53767, 0.59486, 0.68658, 0.72196, 0.41246, 0.73662, 0.55798, 0.76351, 0.67642, 0.82706, 0.40569, 0.86127, 0.54106, 0.8906, 0.59859], "triangles": [26, 27, 25, 25, 27, 24, 27, 28, 24, 24, 28, 23, 23, 28, 45, 45, 28, 29, 45, 29, 42, 45, 42, 44, 29, 30, 42, 42, 30, 36, 30, 31, 36, 42, 36, 41, 32, 35, 31, 31, 35, 36, 32, 33, 35, 35, 33, 34, 42, 41, 44, 36, 37, 41, 41, 37, 38, 38, 39, 1, 39, 0, 1, 38, 1, 2, 44, 43, 47, 44, 40, 43, 44, 41, 40, 4, 43, 3, 41, 38, 40, 40, 38, 3, 43, 40, 3, 3, 38, 2, 20, 21, 19, 21, 22, 19, 19, 22, 18, 22, 23, 48, 22, 48, 18, 48, 23, 45, 51, 17, 48, 51, 48, 50, 50, 48, 47, 18, 48, 17, 45, 44, 48, 48, 44, 47, 52, 49, 5, 5, 6, 52, 47, 49, 50, 52, 50, 49, 47, 46, 49, 47, 43, 46, 49, 46, 5, 5, 46, 4, 46, 43, 4, 15, 16, 14, 14, 16, 13, 16, 17, 13, 13, 17, 51, 13, 51, 12, 51, 54, 12, 12, 54, 11, 51, 53, 54, 53, 50, 52, 53, 51, 50, 54, 10, 11, 10, 54, 9, 54, 53, 9, 53, 52, 9, 52, 8, 9, 8, 6, 7, 8, 52, 6], "vertices": [2, 27, 67.11, -13.22, 0.3959, 28, 4.25, -34.54, 0.6041, 2, 27, 65.75, -14.53, 0.3969, 28, 2.44, -34, 0.60309, 3, 26, 84.36, -37.43, 0.00036, 27, 48.28, -22.56, 0.55724, 28, -13.12, -22.7, 0.44241, 3, 26, 64.98, -36.76, 0.05388, 27, 29.8, -28.42, 0.85893, 28, -27.28, -9.46, 0.08719, 2, 26, 36.81, -32.27, 0.54466, 27, 1.75, -33.63, 0.45534, 3, 5, 33.51, -28.1, 0.12519, 26, 14.18, -21.77, 0.84002, 27, -23.08, -31.32, 0.03479, 3, 5, 26.03, -26.79, 0.38681, 26, 7.31, -25, 0.60886, 27, -28.48, -36.66, 0.00433, 2, 5, 9.72, -29.6, 0.72659, 26, -4.44, -36.65, 0.27341, 2, 5, 6.57, -24.51, 0.76714, 26, -9.94, -34.29, 0.23286, 2, 5, -0.71, -3.86, 0.99433, 26, -27.74, -21.55, 0.00567, 2, 5, -5.94, 21.87, 1, 26, -46.79, -3.47, 0, 2, 5, -5.12, 27.35, 1, 26, -49.26, 1.49, 0, 3, 5, 14.5, 35.1, 0.99218, 26, -37.63, 19.1, 0.00767, 29, -100.21, 108.08, 0.00016, 4, 5, 43.96, 39.32, 0.6175, 26, -15.92, 39.45, 0.36809, 27, -71.96, 16.27, 0.0006, 29, -70.48, 106.86, 0.01382, 4, 5, 41.95, 51.4, 0.54866, 26, -24.5, 48.18, 0.43424, 27, -82.97, 21.63, 1e-05, 29, -70.26, 119.1, 0.01709, 3, 5, 43.22, 56.88, 0.5463, 26, -26.6, 53.41, 0.43656, 29, -68.01, 124.27, 0.01714, 4, 5, 56.16, 54.21, 0.50882, 26, -14.46, 58.64, 0.46658, 27, -77.02, 34.84, 0.00277, 29, -55.77, 119.28, 0.02184, 5, 5, 69.76, 46.81, 0.3778, 26, 0.92, 60.38, 0.55957, 27, -63.12, 41.64, 0.01912, 28, -11.71, 105.86, 0.00054, 29, -43.75, 109.53, 0.04296, 5, 5, 80.98, 39.26, 0.18153, 26, 14.44, 60.63, 0.65139, 27, -50.46, 46.4, 0.06399, 28, -1.36, 97.16, 0.00458, 29, -34.09, 100.06, 0.09851, 5, 5, 88.76, 67.33, 0.07926, 26, 4.72, 88.09, 0.69216, 27, -68.82, 69.02, 0.08619, 28, 9.37, 124.24, 3e-05, 29, -21.33, 126.25, 0.14235, 4, 5, 90.92, 69.28, 0.07907, 26, 5.37, 90.92, 0.69236, 27, -69.15, 71.9, 0.08618, 29, -18.85, 127.77, 0.14238, 5, 5, 91.73, 69.13, 0.07907, 26, 6.11, 91.26, 0.69235, 27, -68.57, 72.47, 0.08619, 28, 12.5, 125.72, 0, 29, -18.09, 127.48, 0.14239, 5, 5, 108.94, 53.24, 0.07031, 26, 29.32, 88.12, 0.65956, 27, -45.65, 77.29, 0.10082, 28, 27.93, 108.09, 0.00667, 29, -4.05, 108.72, 0.16264, 5, 5, 131.44, 18.21, 0.00853, 26, 67.85, 72.33, 0.31138, 27, -4.06, 75.33, 0.16892, 28, 46.59, 70.87, 0.04648, 29, 11.7, 70.18, 0.46469, 5, 5, 137.65, 24.78, 0.00181, 26, 69.17, 81.27, 0.23194, 27, -5.81, 84.19, 0.15709, 28, 53.46, 76.74, 0.02622, 29, 19, 75.51, 0.58294, 4, 26, 71.05, 101.11, 0.20352, 27, -10.68, 103.51, 0.15096, 28, 67.91, 90.45, 0.00889, 29, 34.46, 88.07, 0.63664, 4, 26, 73.96, 102.68, 0.20334, 27, -8.47, 105.96, 0.15091, 28, 71.14, 89.72, 0.00881, 29, 37.62, 87.1, 0.63693, 4, 26, 77.24, 101.47, 0.2033, 27, -4.97, 105.93, 0.15089, 28, 72.82, 86.66, 0.00911, 29, 39.06, 83.91, 0.6367, 4, 26, 90.43, 87.31, 0.19421, 27, 12.2, 97.01, 0.1476, 28, 73.46, 67.32, 0.01955, 29, 38.22, 64.58, 0.63864, 4, 26, 116.23, 53.43, 0.04051, 27, 47.87, 73.73, 0.04578, 28, 70.62, 24.82, 0.01425, 29, 32.14, 22.42, 0.89945, 4, 26, 121.27, 52.48, 0.02013, 27, 52.93, 74.53, 0.02343, 28, 73.8, 20.8, 0.0036, 29, 34.99, 18.17, 0.95285, 3, 26, 140.1, 50.28, 7e-05, 27, 71.41, 78.77, 8e-05, 29, 46.63, 3.2, 0.99985, 1, 29, 61.32, -10.77, 1, 1, 29, 61.51, -13.6, 1, 1, 29, 59.99, -16.96, 1, 1, 29, 52.05, -17.67, 1, 2, 28, 58.76, -16.11, 0.00474, 29, 17.17, -17.48, 0.99526, 1, 28, 19.42, -14.71, 1, 2, 27, 44.78, -8.17, 0.40255, 28, -2.29, -12.6, 0.59745, 2, 27, 65.19, -8.11, 0.39609, 28, 7.76, -30.36, 0.60391, 2, 26, 67.41, -9.35, 0.0016, 27, 22.9, -1.78, 0.9984, 4, 26, 88.76, 13.77, 0.01374, 27, 35.27, 27.16, 0.14651, 28, 23.86, 12.99, 0.76084, 29, -15.4, 14.21, 0.07891, 4, 26, 99.93, 36.3, 0.04697, 27, 38.25, 52.13, 0.08392, 28, 47.08, 22.63, 0.17569, 29, 8.5, 22.04, 0.69342, 2, 26, 49.4, -4.24, 0.12389, 27, 4.22, -3, 0.87611, 5, 5, 102.19, -17.93, 0.00125, 26, 64.61, 25.94, 0.21536, 27, 8.44, 30.54, 0.44164, 28, 13.66, 38.04, 0.20469, 29, -23.65, 39.97, 0.13706, 5, 5, 123.28, 3.42, 0.00642, 26, 69.65, 55.54, 0.29523, 27, 3.27, 60.11, 0.20265, 28, 36.9, 57.03, 0.09428, 29, 0.98, 57.12, 0.40141, 2, 26, 31.18, -5.03, 0.96958, 27, -12.68, -9.85, 0.03042, 5, 5, 71.57, -0.5, 0.04436, 26, 29.54, 22.66, 0.85131, 27, -23.51, 15.69, 0.06019, 28, -14.93, 58.62, 0.00994, 29, -50.58, 62.68, 0.0342, 5, 5, 79.92, 21.59, 0.14456, 26, 23.71, 45.54, 0.68745, 27, -36.67, 35.3, 0.07297, 28, -4.28, 79.7, 0.01082, 29, -38.35, 82.88, 0.08421, 3, 5, 45.2, -15.51, 0.00385, 26, 16.54, -4.76, 0.99234, 27, -26.56, -14.5, 0.00381, 3, 5, 46.18, 7.39, 0.31215, 26, 4.21, 14.56, 0.68282, 29, -74.11, 75.06, 0.00503, 4, 5, 43.81, 26.61, 0.65284, 26, -8.75, 28.95, 0.33714, 27, -61.69, 8.78, 0.00048, 29, -72.94, 94.39, 0.00954, 2, 5, 22.68, -12.45, 0.64072, 26, -3.66, -15.17, 0.35928, 2, 5, 19.22, 9.65, 0.99999, 29, -100.2, 82.2, 1e-05, 3, 5, 14.61, 19.63, 0.99979, 26, -28.67, 6.48, 0.00019, 29, -102.92, 92.84, 2e-05], "hull": 40}}, "F-c2": {"F-c": {"type": "mesh", "uvs": [0.40675, 0, 0.41549, 0, 0.4994, 0.04115, 0.57969, 0.09675, 0.68964, 0.19804, 0.76466, 0.31959, 0.7998, 0.31913, 0.87172, 0.28254, 0.89032, 0.31101, 0.94073, 0.43275, 0.98607, 0.58891, 0.98691, 0.62445, 0.90405, 0.69604, 0.77343, 0.75675, 0.79271, 0.83055, 0.79149, 0.86663, 0.73034, 0.86472, 0.66224, 0.83386, 0.60484, 0.79923, 0.59286, 0.98523, 0.58466, 1, 0.58088, 1, 0.48922, 0.91977, 0.35745, 0.72497, 0.33467, 0.77358, 0.28853, 0.88417, 0.27327, 0.88555, 0.26177, 0.86976, 0.2335, 0.75207, 0.19018, 0.48572, 0.17079, 0.46677, 0.09576, 0.40372, 0.00916, 0.35365, 0.00365, 0.33715, 0.0047, 0.31355, 0.03784, 0.29116, 0.18903, 0.21254, 0.36547, 0.14952, 0.46461, 0.12292, 0.39666, 0.03205, 0.51665, 0.25002, 0.38222, 0.32785, 0.29179, 0.42938, 0.58264, 0.32785, 0.46044, 0.46322, 0.38222, 0.62228, 0.66085, 0.37185, 0.61442, 0.53767, 0.59486, 0.68658, 0.72196, 0.41246, 0.73662, 0.55798, 0.76351, 0.67642, 0.82706, 0.40569, 0.86127, 0.54106, 0.8906, 0.59859], "triangles": [26, 27, 25, 25, 27, 24, 27, 28, 24, 24, 28, 23, 23, 28, 45, 45, 28, 29, 45, 29, 42, 45, 42, 44, 29, 30, 42, 42, 30, 36, 30, 31, 36, 42, 36, 41, 32, 35, 31, 31, 35, 36, 32, 33, 35, 35, 33, 34, 42, 41, 44, 36, 37, 41, 41, 37, 38, 38, 39, 1, 39, 0, 1, 38, 1, 2, 44, 43, 47, 44, 40, 43, 44, 41, 40, 4, 43, 3, 41, 38, 40, 40, 38, 3, 43, 40, 3, 3, 38, 2, 20, 21, 19, 21, 22, 19, 19, 22, 18, 22, 23, 48, 22, 48, 18, 48, 23, 45, 51, 17, 48, 51, 48, 50, 50, 48, 47, 18, 48, 17, 45, 44, 48, 48, 44, 47, 52, 49, 5, 5, 6, 52, 47, 49, 50, 52, 50, 49, 47, 46, 49, 47, 43, 46, 49, 46, 5, 5, 46, 4, 46, 43, 4, 15, 16, 14, 14, 16, 13, 16, 17, 13, 13, 17, 51, 13, 51, 12, 51, 54, 12, 12, 54, 11, 51, 53, 54, 53, 50, 52, 53, 51, 50, 54, 10, 11, 10, 54, 9, 54, 53, 9, 53, 52, 9, 52, 8, 9, 8, 6, 7, 8, 52, 6], "vertices": [2, 32, 67.11, -13.22, 0.3959, 33, 4.25, -34.54, 0.6041, 2, 32, 65.75, -14.53, 0.3969, 33, 2.44, -34, 0.60309, 3, 31, 84.36, -37.43, 0.00036, 32, 48.28, -22.56, 0.55724, 33, -13.12, -22.7, 0.44241, 3, 31, 64.98, -36.76, 0.05388, 32, 29.8, -28.42, 0.85893, 33, -27.28, -9.46, 0.08719, 2, 31, 36.81, -32.27, 0.54466, 32, 1.75, -33.63, 0.45534, 3, 30, 33.51, -28.1, 0.12519, 31, 14.18, -21.77, 0.84002, 32, -23.08, -31.32, 0.03479, 3, 30, 26.03, -26.79, 0.38681, 31, 7.31, -25, 0.60886, 32, -28.48, -36.66, 0.00433, 2, 30, 9.72, -29.6, 0.72659, 31, -4.44, -36.65, 0.27341, 2, 30, 6.57, -24.51, 0.76714, 31, -9.94, -34.29, 0.23286, 2, 30, -0.71, -3.86, 0.99433, 31, -27.74, -21.55, 0.00567, 2, 30, -5.94, 21.87, 1, 31, -46.79, -3.47, 0, 2, 30, -5.12, 27.35, 1, 31, -49.26, 1.49, 0, 3, 30, 14.5, 35.1, 0.99218, 31, -37.63, 19.1, 0.00767, 34, -100.21, 108.08, 0.00016, 4, 30, 43.96, 39.32, 0.6175, 31, -15.92, 39.45, 0.36809, 32, -71.96, 16.27, 0.0006, 34, -70.48, 106.86, 0.01382, 4, 30, 41.95, 51.4, 0.54866, 31, -24.5, 48.18, 0.43424, 32, -82.97, 21.63, 1e-05, 34, -70.26, 119.1, 0.01709, 3, 30, 43.22, 56.88, 0.5463, 31, -26.6, 53.41, 0.43656, 34, -68.01, 124.27, 0.01714, 4, 30, 56.16, 54.21, 0.50882, 31, -14.46, 58.64, 0.46658, 32, -77.02, 34.84, 0.00277, 34, -55.77, 119.28, 0.02184, 5, 30, 69.76, 46.81, 0.3778, 31, 0.92, 60.38, 0.55957, 32, -63.12, 41.64, 0.01912, 33, -11.71, 105.86, 0.00054, 34, -43.75, 109.53, 0.04296, 5, 30, 80.98, 39.26, 0.18153, 31, 14.44, 60.63, 0.65139, 32, -50.46, 46.4, 0.06399, 33, -1.36, 97.16, 0.00458, 34, -34.09, 100.06, 0.09851, 5, 30, 88.76, 67.33, 0.07926, 31, 4.72, 88.09, 0.69216, 32, -68.82, 69.02, 0.08619, 33, 9.37, 124.24, 3e-05, 34, -21.33, 126.25, 0.14235, 4, 30, 90.92, 69.28, 0.07907, 31, 5.37, 90.92, 0.69236, 32, -69.15, 71.9, 0.08618, 34, -18.85, 127.77, 0.14238, 5, 30, 91.73, 69.13, 0.07907, 31, 6.11, 91.26, 0.69235, 32, -68.57, 72.47, 0.08619, 33, 12.5, 125.72, 0, 34, -18.09, 127.48, 0.14239, 5, 30, 108.94, 53.24, 0.07031, 31, 29.32, 88.12, 0.65956, 32, -45.65, 77.29, 0.10082, 33, 27.93, 108.09, 0.00667, 34, -4.05, 108.72, 0.16264, 5, 30, 131.44, 18.21, 0.00853, 31, 67.85, 72.33, 0.31138, 32, -4.06, 75.33, 0.16892, 33, 46.59, 70.87, 0.04648, 34, 11.7, 70.18, 0.46469, 5, 30, 137.65, 24.78, 0.00181, 31, 69.17, 81.27, 0.23194, 32, -5.81, 84.19, 0.15709, 33, 53.46, 76.74, 0.02622, 34, 19, 75.51, 0.58294, 4, 31, 71.05, 101.11, 0.20352, 32, -10.68, 103.51, 0.15096, 33, 67.91, 90.45, 0.00889, 34, 34.46, 88.07, 0.63664, 4, 31, 73.96, 102.68, 0.20334, 32, -8.47, 105.96, 0.15091, 33, 71.14, 89.72, 0.00881, 34, 37.62, 87.1, 0.63693, 4, 31, 77.24, 101.47, 0.2033, 32, -4.97, 105.93, 0.15089, 33, 72.82, 86.66, 0.00911, 34, 39.06, 83.91, 0.6367, 4, 31, 90.43, 87.31, 0.19421, 32, 12.2, 97.01, 0.1476, 33, 73.46, 67.32, 0.01955, 34, 38.22, 64.58, 0.63864, 4, 31, 116.23, 53.43, 0.04051, 32, 47.87, 73.73, 0.04578, 33, 70.62, 24.82, 0.01425, 34, 32.14, 22.42, 0.89945, 4, 31, 121.27, 52.48, 0.02013, 32, 52.93, 74.53, 0.02343, 33, 73.8, 20.8, 0.0036, 34, 34.99, 18.17, 0.95285, 3, 31, 140.1, 50.28, 7e-05, 32, 71.41, 78.77, 8e-05, 34, 46.63, 3.2, 0.99985, 1, 34, 61.32, -10.77, 1, 1, 34, 61.51, -13.6, 1, 1, 34, 59.99, -16.96, 1, 1, 34, 52.05, -17.67, 1, 2, 33, 58.76, -16.11, 0.00474, 34, 17.17, -17.48, 0.99526, 1, 33, 19.42, -14.71, 1, 2, 32, 44.78, -8.17, 0.40255, 33, -2.29, -12.6, 0.59745, 2, 32, 65.19, -8.11, 0.39609, 33, 7.76, -30.36, 0.60391, 2, 31, 67.41, -9.35, 0.0016, 32, 22.9, -1.78, 0.9984, 4, 31, 88.76, 13.77, 0.01374, 32, 35.27, 27.16, 0.14651, 33, 23.86, 12.99, 0.76084, 34, -15.4, 14.21, 0.07891, 4, 31, 99.93, 36.3, 0.04697, 32, 38.25, 52.13, 0.08392, 33, 47.08, 22.63, 0.17569, 34, 8.5, 22.04, 0.69342, 2, 31, 49.4, -4.24, 0.12389, 32, 4.22, -3, 0.87611, 5, 30, 102.19, -17.93, 0.00125, 31, 64.61, 25.94, 0.21536, 32, 8.44, 30.54, 0.44164, 33, 13.66, 38.04, 0.20469, 34, -23.65, 39.97, 0.13706, 5, 30, 123.28, 3.42, 0.00642, 31, 69.65, 55.54, 0.29523, 32, 3.27, 60.11, 0.20265, 33, 36.9, 57.03, 0.09428, 34, 0.98, 57.12, 0.40141, 2, 31, 31.18, -5.03, 0.96958, 32, -12.68, -9.85, 0.03042, 5, 30, 71.57, -0.5, 0.04436, 31, 29.54, 22.66, 0.85131, 32, -23.51, 15.69, 0.06019, 33, -14.93, 58.62, 0.00994, 34, -50.58, 62.68, 0.0342, 5, 30, 79.92, 21.59, 0.14456, 31, 23.71, 45.54, 0.68745, 32, -36.67, 35.3, 0.07297, 33, -4.28, 79.7, 0.01082, 34, -38.35, 82.88, 0.08421, 3, 30, 45.2, -15.51, 0.00385, 31, 16.54, -4.76, 0.99234, 32, -26.56, -14.5, 0.00381, 3, 30, 46.18, 7.39, 0.31215, 31, 4.21, 14.56, 0.68282, 34, -74.11, 75.06, 0.00503, 4, 30, 43.81, 26.61, 0.65284, 31, -8.75, 28.95, 0.33714, 32, -61.69, 8.78, 0.00048, 34, -72.94, 94.39, 0.00954, 2, 30, 22.68, -12.45, 0.64072, 31, -3.66, -15.17, 0.35928, 2, 30, 19.22, 9.65, 0.99999, 34, -100.2, 82.2, 1e-05, 3, 30, 14.61, 19.63, 0.99979, 31, -28.67, 6.48, 0.00019, 34, -102.92, 92.84, 2e-05], "hull": 40}}, "F-d": {"F-d": {"type": "mesh", "uvs": [0.40605, 0.00549, 0.57402, 0.00548, 0.70763, 0.05729, 0.78938, 0.12344, 0.8305, 0.19678, 0.84942, 0.26524, 0.8493, 0.33838, 0.82007, 0.38821, 0.65424, 0.4868, 0.83181, 0.55637, 0.88403, 0.58733, 0.92482, 0.62366, 0.99457, 0.70674, 0.94556, 0.79419, 0.9056, 0.86549, 0.81006, 0.97933, 0.78209, 0.99686, 0.74687, 0.99464, 0.603, 0.89474, 0.51342, 0.818, 0.42421, 0.74158, 0.42201, 0.70938, 0.55752, 0.62806, 0.75186, 0.56, 0.65684, 0.49006, 0.37682, 0.51649, 0.22276, 0.42505, 0.15251, 0.36692, 0.07563, 0.27656, 0.00638, 0.17188, 0.04742, 0.12288, 0.12237, 0.07873, 0.23492, 0.03379, 0.338, 0.01321, 0.76338, 0.64236, 0.88893, 0.74205, 0.60563, 0.71521, 0.7344, 0.80531, 0.77625, 0.89734], "triangles": [18, 37, 38, 15, 38, 14, 17, 18, 38, 17, 38, 15, 16, 17, 15, 20, 21, 36, 13, 35, 12, 35, 37, 34, 37, 35, 13, 19, 20, 36, 19, 36, 37, 14, 37, 13, 18, 19, 37, 38, 37, 14, 23, 24, 9, 34, 23, 9, 34, 9, 10, 34, 10, 11, 22, 23, 34, 36, 22, 34, 21, 22, 36, 35, 34, 11, 35, 11, 12, 37, 36, 34, 28, 30, 31, 29, 30, 28, 4, 5, 8, 32, 28, 31, 33, 27, 28, 0, 3, 4, 6, 7, 5, 33, 26, 27, 0, 1, 2, 26, 33, 0, 32, 33, 28, 4, 26, 0, 3, 0, 2, 4, 8, 26, 5, 7, 8, 9, 24, 8, 25, 26, 8, 25, 8, 24], "vertices": [1, 18, -25.93, 84.01, 1, 1, 18, -8.13, 84.02, 1, 1, 18, 6.04, 74.79, 1, 1, 18, 14.7, 63.02, 1, 1, 18, 19.06, 49.97, 1, 1, 18, 21.06, 37.78, 1, 1, 18, 21.05, 24.76, 1, 1, 18, 17.95, 15.89, 1, 1, 18, 0.38, -1.66, 1, 2, 18, 19.2, -14.04, 0.00044, 19, -1, 8.38, 0.99956, 2, 19, 4.93, 13.47, 0.99208, 20, -21.6, 12.63, 0.00792, 2, 19, 11.72, 17.27, 0.91256, 20, -14.97, 16.69, 0.08744, 2, 19, 27.04, 23.47, 0.4722, 20, 0.1, 23.5, 0.5278, 3, 19, 42.15, 17.07, 0.06907, 20, 15.45, 17.69, 0.90974, 21, -11.94, 16.75, 0.02119, 2, 20, 27.97, 12.96, 0.54458, 21, 0.92, 13.08, 0.45542, 1, 21, 21.61, 3.86, 1, 2, 20, 50.81, -1.05, 0, 21, 24.86, 1.03, 1, 1, 21, 24.63, -2.71, 1, 4, 18, -5.06, -74.27, 0, 19, 57.14, -20.54, 0.01479, 20, 31.9, -19.3, 0.39448, 21, 7.54, -18.73, 0.59073, 4, 18, -14.55, -60.61, 1e-05, 19, 42.77, -28.93, 0.18449, 20, 17.88, -28.25, 0.69479, 21, -5.69, -28.82, 0.12071, 4, 18, -24.01, -47.01, 2e-05, 19, 28.47, -37.28, 0.43284, 20, 3.91, -37.16, 0.55935, 21, -18.86, -38.87, 0.00779, 4, 18, -24.24, -41.28, 2e-05, 19, 22.73, -37.07, 0.47393, 20, -1.82, -37.17, 0.52367, 21, -24.58, -39.35, 0.00238, 3, 18, -9.88, -26.8, 3e-05, 19, 9.43, -21.61, 0.80343, 20, -15.72, -22.24, 0.19654, 3, 18, 10.72, -14.69, 0.00725, 19, -1.02, -0.12, 0.99275, 20, -27.01, -1.18, 0, 1, 18, 0.65, -2.24, 1, 1, 18, -29.03, -6.94, 1, 1, 18, -45.36, 9.33, 1, 1, 18, -52.81, 19.68, 1, 1, 18, -60.96, 35.76, 1, 1, 18, -68.3, 54.4, 1, 1, 18, -63.95, 63.12, 1, 1, 18, -56, 70.98, 1, 1, 18, -44.07, 78.98, 1, 1, 18, -33.15, 82.64, 1, 1, 19, 13.69, -0.05, 1, 2, 19, 32.43, 11.82, 0.22445, 20, 5.94, 12.06, 0.77555, 4, 18, -4.78, -42.32, 2e-05, 19, 25.3, -17.74, 0.51231, 20, -0.02, -17.76, 0.48368, 21, -24.4, -19.86, 0.00399, 4, 18, 8.87, -58.35, 0, 19, 42.36, -5.4, 0.00776, 20, 16.55, -4.75, 0.95807, 21, -8.98, -5.52, 0.03416, 3, 18, 13.31, -74.73, 0, 20, 33.09, -0.96, 0.00185, 21, 7.19, -0.37, 0.99815], "hull": 34}}, "gloden-b": {"gloden-b": {"x": -420.1, "y": 62.93, "width": 47, "height": 36}}, "gloden-c": {"gloden-c": {"x": -449.8, "y": -164.91, "width": 87, "height": 86}}, "gloden-d": {"gloden-d": {"x": 516.9, "y": 10.43, "width": 63, "height": 63}}, "gloden-e": {"gloden-e": {"x": -166.8, "y": -151.91, "width": 67, "height": 68}}, "N-a": {"N-a": {"x": -37.61, "y": 87.07, "width": 259, "height": 186}}, "N-b": {"N-b": {"x": -3.65, "y": -5.95, "width": 192, "height": 153}}, "N-c": {"N-c": {"type": "mesh", "uvs": [0.48187, 0.00804, 0.53218, 0.09829, 0.61291, 0.17019, 0.6993, 0.21325, 0.85159, 0.24491, 0.97184, 0.22623, 0.9915, 0.28704, 0.99102, 0.33957, 0.91357, 0.34963, 0.95762, 0.46801, 0.82692, 0.49993, 0.70492, 0.55899, 0.66828, 0.59211, 0.60908, 0.65799, 0.5687, 0.73035, 0.5394, 0.82648, 0.51516, 0.859, 0.47151, 0.84932, 0.31155, 0.65606, 0.20479, 0.63142, 0.12136, 0.69158, 0.06563, 0.74625, 0.01886, 0.74606, 0.00905, 0.73, 0.00857, 0.6494, 0.06917, 0.48489, 0.16458, 0.33738, 0.16488, 0.20425, 0.16508, 0.11134, 0.33397, 0.03433, 0.41917, 0.00813, 0.75415, 0.36148, 0.56995, 0.29761, 0.44074, 0.18708, 0.29777, 0.27796, 0.27303, 0.48921, 0.42149, 0.34674, 0.63044, 0.40078, 0.54521, 0.48675, 0.47648, 0.59483, 0.31152, 0.15269], "triangles": [27, 28, 40, 34, 27, 40, 26, 27, 34, 35, 26, 34, 35, 34, 36, 35, 36, 38, 25, 26, 35, 39, 35, 38, 19, 25, 35, 18, 19, 35, 24, 25, 19, 39, 18, 35, 13, 39, 12, 20, 24, 19, 21, 23, 24, 14, 39, 13, 21, 22, 23, 24, 20, 21, 14, 18, 39, 14, 17, 18, 15, 17, 14, 16, 17, 15, 40, 28, 29, 1, 33, 30, 1, 30, 0, 33, 1, 2, 29, 30, 33, 40, 29, 33, 34, 40, 33, 32, 33, 2, 32, 2, 3, 36, 34, 33, 36, 33, 32, 37, 32, 31, 37, 38, 36, 37, 36, 32, 38, 37, 11, 12, 38, 11, 39, 38, 12, 6, 4, 5, 6, 8, 4, 7, 8, 6, 31, 3, 4, 31, 4, 8, 32, 3, 31, 10, 31, 8, 10, 8, 9, 10, 11, 37, 10, 37, 31], "vertices": [2, 24, 21.45, -36.66, 0.91527, 25, -35.99, -13.25, 0.08473, 3, 23, 45.82, -28.08, 0.00178, 24, 15.56, -25.86, 0.96796, 25, -30.95, -2.03, 0.03026, 2, 23, 38.23, -18.25, 0.1051, 24, 6.45, -17.42, 0.8949, 2, 23, 29.57, -11.82, 0.63972, 24, -3.15, -12.51, 0.36028, 1, 23, 13.61, -5.84, 1, 1, 23, 0.32, -6.41, 1, 1, 23, -0.85, 1.23, 1, 1, 23, 0.03, 7.57, 1, 1, 23, 8.56, 7.7, 1, 2, 23, 5.67, 22.64, 0.99998, 24, -32.43, 17.51, 2e-05, 3, 23, 20.3, 24.66, 0.86392, 24, -18.34, 21.93, 0.10856, 25, -13.43, 53.89, 0.02753, 3, 23, 34.42, 30.09, 0.40341, 24, -5.31, 29.61, 0.41596, 25, 0.57, 48.17, 0.18063, 3, 23, 38.9, 33.58, 0.24574, 24, -1.47, 33.8, 0.46916, 25, 6.23, 47.65, 0.2851, 3, 23, 46.34, 40.71, 0.08731, 24, 4.68, 42.07, 0.44524, 25, 16.54, 47.75, 0.46745, 3, 23, 51.85, 48.9, 0.02315, 24, 8.76, 51.05, 0.39202, 25, 26.15, 49.95, 0.58484, 3, 23, 56.53, 60.11, 0.00128, 24, 11.52, 62.89, 0.36811, 25, 37.25, 54.91, 0.63061, 3, 23, 59.66, 63.71, 5e-05, 24, 14.01, 66.95, 0.36641, 25, 41.99, 55.39, 0.63354, 2, 24, 18.81, 65.94, 0.36507, 25, 44.09, 50.96, 0.63493, 3, 23, 78.47, 36.28, 0.00014, 24, 37.1, 43.02, 0.19364, 25, 36.92, 22.53, 0.80622, 2, 24, 48.84, 40.44, 0.03065, 25, 41.97, 11.62, 0.96935, 2, 24, 57.66, 48.11, 0.00027, 25, 53.41, 9.24, 0.99973, 1, 25, 62.42, 8.78, 1, 1, 25, 65.64, 4.82, 1, 1, 25, 64.8, 2.75, 1, 1, 25, 57.23, -3.53, 1, 1, 25, 37.52, -11.14, 1, 2, 24, 54.54, 4.76, 0.00027, 25, 17.01, -14.51, 0.99973, 2, 24, 55.1, -11.47, 0.1722, 25, 4.43, -24.79, 0.8278, 2, 24, 55.49, -22.8, 0.33046, 25, -4.35, -31.96, 0.66954, 2, 24, 37.44, -32.87, 0.74051, 25, -23.29, -23.68, 0.25949, 2, 24, 28.28, -36.4, 0.87736, 25, -31.65, -18.52, 0.12264, 3, 23, 25.98, 6.89, 0.95288, 24, -9.79, 5.34, 0.04056, 25, -21.46, 37.04, 0.00656, 2, 23, 44.88, -3.44, 0.00104, 24, 10.56, -1.71, 0.99896, 2, 24, 25.13, -14.67, 0.93435, 25, -16.25, -2.86, 0.06565, 2, 24, 40.29, -3.02, 0.09929, 25, 2.2, -7.88, 0.90071, 2, 24, 42.04, 22.83, 0.04203, 25, 23.84, 6.37, 0.95797, 2, 24, 26.51, 4.87, 0.77272, 25, 0.14, 7.86, 0.22728, 3, 23, 39.97, 9.9, 0.24715, 24, 3.51, 10.62, 0.6933, 25, -9.2, 29.66, 0.05954, 3, 23, 50.54, 19.1, 0.09324, 24, 12.41, 21.45, 0.60941, 25, 4.8, 29.12, 0.29735, 3, 23, 59.68, 31.2, 0.03334, 24, 19.41, 34.9, 0.39889, 25, 19.74, 31.69, 0.56777, 2, 24, 39.36, -18.35, 0.55492, 25, -10.57, -16.42, 0.44508], "hull": 31}}, "N-c2": {"N-c": {"type": "mesh", "uvs": [0.48187, 0.00804, 0.53218, 0.09829, 0.61291, 0.17019, 0.6993, 0.21325, 0.85159, 0.24491, 0.97184, 0.22623, 0.9915, 0.28704, 0.99102, 0.33957, 0.91357, 0.34963, 0.95762, 0.46801, 0.82692, 0.49993, 0.70492, 0.55899, 0.66828, 0.59211, 0.60908, 0.65799, 0.5687, 0.73035, 0.5394, 0.82648, 0.51516, 0.859, 0.47151, 0.84932, 0.31155, 0.65606, 0.20479, 0.63142, 0.12136, 0.69158, 0.06563, 0.74625, 0.01886, 0.74606, 0.00905, 0.73, 0.00857, 0.6494, 0.06917, 0.48489, 0.16458, 0.33738, 0.16488, 0.20425, 0.16508, 0.11134, 0.33397, 0.03433, 0.41917, 0.00813, 0.75415, 0.36148, 0.56995, 0.29761, 0.44074, 0.18708, 0.29777, 0.27796, 0.27303, 0.48921, 0.42149, 0.34674, 0.63044, 0.40078, 0.54521, 0.48675, 0.47648, 0.59483, 0.31152, 0.15269], "triangles": [27, 28, 40, 34, 27, 40, 26, 27, 34, 35, 26, 34, 35, 34, 36, 35, 36, 38, 25, 26, 35, 39, 35, 38, 19, 25, 35, 18, 19, 35, 24, 25, 19, 39, 18, 35, 13, 39, 12, 20, 24, 19, 21, 23, 24, 14, 39, 13, 21, 22, 23, 24, 20, 21, 14, 18, 39, 14, 17, 18, 15, 17, 14, 16, 17, 15, 40, 28, 29, 1, 33, 30, 1, 30, 0, 33, 1, 2, 29, 30, 33, 40, 29, 33, 34, 40, 33, 32, 33, 2, 32, 2, 3, 36, 34, 33, 36, 33, 32, 37, 32, 31, 37, 38, 36, 37, 36, 32, 38, 37, 11, 12, 38, 11, 39, 38, 12, 6, 4, 5, 6, 8, 4, 7, 8, 6, 31, 3, 4, 31, 4, 8, 32, 3, 31, 10, 31, 8, 10, 8, 9, 10, 11, 37, 10, 37, 31], "vertices": [2, 37, 21.45, -36.66, 0.91527, 38, -35.99, -13.25, 0.08473, 3, 36, 45.82, -28.08, 0.00178, 37, 15.56, -25.86, 0.96796, 38, -30.95, -2.03, 0.03026, 2, 36, 38.23, -18.25, 0.1051, 37, 6.45, -17.42, 0.8949, 2, 36, 29.57, -11.82, 0.63972, 37, -3.15, -12.51, 0.36028, 1, 36, 13.61, -5.84, 1, 1, 36, 0.32, -6.41, 1, 1, 36, -0.85, 1.23, 1, 1, 36, 0.03, 7.57, 1, 1, 36, 8.56, 7.7, 1, 2, 36, 5.67, 22.64, 0.99998, 37, -32.43, 17.51, 2e-05, 3, 36, 20.3, 24.66, 0.86392, 37, -18.34, 21.93, 0.10856, 38, -13.43, 53.89, 0.02753, 3, 36, 34.42, 30.09, 0.40341, 37, -5.31, 29.61, 0.41596, 38, 0.57, 48.17, 0.18063, 3, 36, 38.9, 33.58, 0.24574, 37, -1.47, 33.8, 0.46916, 38, 6.23, 47.65, 0.2851, 3, 36, 46.34, 40.71, 0.08731, 37, 4.68, 42.07, 0.44524, 38, 16.54, 47.75, 0.46745, 3, 36, 51.85, 48.9, 0.02315, 37, 8.76, 51.05, 0.39202, 38, 26.15, 49.95, 0.58484, 3, 36, 56.53, 60.11, 0.00128, 37, 11.52, 62.89, 0.36811, 38, 37.25, 54.91, 0.63061, 3, 36, 59.66, 63.71, 5e-05, 37, 14.01, 66.95, 0.36641, 38, 41.99, 55.39, 0.63354, 2, 37, 18.81, 65.94, 0.36507, 38, 44.09, 50.96, 0.63493, 3, 36, 78.47, 36.28, 0.00014, 37, 37.1, 43.02, 0.19364, 38, 36.92, 22.53, 0.80622, 2, 37, 48.84, 40.44, 0.03065, 38, 41.97, 11.62, 0.96935, 2, 37, 57.66, 48.11, 0.00027, 38, 53.41, 9.24, 0.99973, 1, 38, 62.42, 8.78, 1, 1, 38, 65.64, 4.82, 1, 1, 38, 64.8, 2.75, 1, 1, 38, 57.23, -3.53, 1, 1, 38, 37.52, -11.14, 1, 2, 37, 54.54, 4.76, 0.00027, 38, 17.01, -14.51, 0.99973, 2, 37, 55.1, -11.47, 0.1722, 38, 4.43, -24.79, 0.8278, 2, 37, 55.49, -22.8, 0.33046, 38, -4.35, -31.96, 0.66954, 2, 37, 37.44, -32.87, 0.74051, 38, -23.29, -23.68, 0.25949, 2, 37, 28.28, -36.4, 0.87736, 38, -31.65, -18.52, 0.12264, 3, 36, 25.98, 6.89, 0.95288, 37, -9.79, 5.34, 0.04056, 38, -21.46, 37.04, 0.00656, 2, 36, 44.88, -3.44, 0.00104, 37, 10.56, -1.71, 0.99896, 2, 37, 25.13, -14.67, 0.93435, 38, -16.25, -2.86, 0.06565, 2, 37, 40.29, -3.02, 0.09929, 38, 2.2, -7.88, 0.90071, 2, 37, 42.04, 22.83, 0.04203, 38, 23.84, 6.37, 0.95797, 2, 37, 26.51, 4.87, 0.77272, 38, 0.14, 7.86, 0.22728, 3, 36, 39.97, 9.9, 0.24715, 37, 3.51, 10.62, 0.6933, 38, -9.2, 29.66, 0.05954, 3, 36, 50.54, 19.1, 0.09324, 37, 12.41, 21.45, 0.60941, 38, 4.8, 29.12, 0.29735, 3, 36, 59.68, 31.2, 0.03334, 37, 19.41, 34.9, 0.39889, 38, 19.74, 31.69, 0.56777, 2, 37, 39.36, -18.35, 0.55492, 38, -10.57, -16.42, 0.44508], "hull": 31}}, "root": {"root": {"type": "clipping", "end": "root", "vertexCount": 4, "vertices": [-529.59, 499.18, -529.28, -0.61, 529.65, 0.12, 529.54, 499.27]}}, "snow-1": {"snow-1": {"x": -0.91, "y": -1.02, "width": 24, "height": 24}}, "snow-2": {"snow-1": {"x": -0.91, "y": -1.02, "width": 24, "height": 24}}, "snow-3": {"snow-1": {"x": -0.91, "y": -1.02, "width": 24, "height": 24}}, "snow-4": {"snow-1": {"x": -0.91, "y": -1.02, "width": 24, "height": 24}}, "snow-5": {"snow-1": {"x": -0.91, "y": -1.02, "width": 24, "height": 24}}, "snow-6": {"snow-2": {"x": -0.91, "y": -1.02, "width": 24, "height": 22}}, "snow-7": {"snow-2": {"x": -0.91, "y": -1.02, "width": 24, "height": 22}}, "snow-8": {"snow-2": {"x": -0.91, "y": -1.02, "width": 24, "height": 22}}, "snow-9": {"snow-2": {"x": -0.91, "y": -1.02, "width": 24, "height": 22}}, "snow-10": {"snow-2": {"x": -0.91, "y": -1.02, "width": 24, "height": 22}}, "snow-11": {"snow-4": {"x": -0.91, "y": -1.02, "width": 26, "height": 24}}, "snow-12": {"snow-3": {"x": -0.91, "y": -1.02, "width": 13, "height": 11}}, "snow-13": {"snow-2": {"x": -0.91, "y": -1.02, "width": 24, "height": 22}}, "snow-14": {"snow-4": {"x": -0.91, "y": -1.02, "width": 26, "height": 24}}, "snow-15": {"snow-1": {"x": -0.91, "y": -1.02, "width": 24, "height": 24}}, "snow-16": {"snow-3": {"x": -0.91, "y": -1.02, "width": 13, "height": 11}}, "snow-17": {"snow-4": {"x": -0.91, "y": -1.02, "width": 26, "height": 24}}, "snow-18": {"snow-1": {"x": -0.91, "y": -1.02, "width": 24, "height": 24}}, "snow-19": {"snow-1": {"x": -0.91, "y": -1.02, "width": 24, "height": 24}}, "snow-20": {"snow-1": {"x": -0.91, "y": -1.02, "width": 24, "height": 24}}, "snow-21": {"snow-1": {"x": -0.91, "y": -1.02, "width": 24, "height": 24}}, "snow-22": {"snow-3": {"x": -0.91, "y": -1.02, "width": 13, "height": 11}}, "snow-24": {"snow-1": {"x": -0.91, "y": -1.02, "width": 24, "height": 24}}, "star": {"star": {"x": 1.39, "y": 1.77, "width": 49, "height": 77}}, "star2": {"star": {"x": 1.39, "y": 1.77, "width": 49, "height": 77}}, "tutem": {"tutem": {"x": -5.04, "y": 105.48, "width": 354, "height": 215}}, "tx_Point_070": {"tx_Point_070": {"x": 0.05, "y": -0.99, "width": 128, "height": 128}}, "tx_Point_071": {"tx_Point_070": {"x": 0.05, "y": -0.99, "width": 128, "height": 128}}, "tx_Point_072": {"tx_Point_070": {"x": 0.05, "y": -0.99, "width": 128, "height": 128}}, "tx_Point_073": {"tx_Point_070": {"x": 0.05, "y": -0.99, "width": 128, "height": 128}}, "tx_Point_074": {"tx_Point_070": {"x": 0.05, "y": -0.99, "width": 128, "height": 128}}, "tx_Point_075": {"tx_Point_070": {"x": 0.05, "y": -0.99, "width": 128, "height": 128}}, "tx_Point_076": {"tx_Point_070": {"x": 0.05, "y": -0.99, "width": 128, "height": 128}}, "tx_Point_077": {"tx_Point_070": {"x": 0.05, "y": -0.99, "width": 128, "height": 128}}, "tx_Point_078": {"tx_Point_070": {"x": 0.05, "y": -0.99, "width": 128, "height": 128}}, "tx_Point_079": {"tx_Point_070": {"x": 0.05, "y": -0.99, "width": 128, "height": 128}}, "tx_Point_080": {"tx_Point_070": {"x": 0.05, "y": -0.99, "width": 128, "height": 128}}, "tx_Point_081": {"tx_Point_070": {"x": 0.05, "y": -0.99, "width": 128, "height": 128}}, "tx_Point_082": {"tx_Point_070": {"x": 0.05, "y": -0.99, "width": 128, "height": 128}}, "W-a": {"W-a": {"x": 20, "y": 58.29, "width": 196, "height": 181}}, "W-b": {"W-b": {"x": 30.73, "y": 45.1, "width": 195, "height": 130}}, "W-c": {"W-c": {"x": -40.77, "y": 26.6, "width": 54, "height": 51}}, "W-d": {"W-d": {"x": 86.73, "y": -2.9, "width": 61, "height": 72}}, "W-e": {"W-e": {"x": 14.23, "y": -7.4, "width": 112, "height": 157}}, "W-f": {"W-f": {"type": "mesh", "uvs": [0.12666, 0.00647, 0.15828, 0.19251, 0.23848, 0.0868, 0.3063, 0.10367, 0.36956, 0.26629, 0.38726, 0.54531, 0.41262, 0.54195, 0.46386, 0.46732, 0.52011, 0.38538, 0.58854, 0.33625, 0.64791, 0.29364, 0.72738, 0.29817, 0.82912, 0.49859, 0.84926, 0.33999, 0.87515, 0.26266, 0.90119, 0.26158, 0.94357, 0.3438, 0.97045, 0.42678, 1, 0.58103, 1, 0.61026, 0.95605, 0.75946, 0.89957, 0.86911, 0.85151, 0.91254, 0.71369, 0.87392, 0.69218, 0.86638, 0.62714, 0.96928, 0.58121, 1, 0.56167, 1, 0.5223, 0.97465, 0.44205, 0.87079, 0.39603, 0.73031, 0.30283, 0.73128, 0.26316, 0.71014, 0.21395, 0.64592, 0.07603, 0.65706, 0.02819, 0.56911, 0.00345, 0.45668, 0.00335, 0.38234, 0.04359, 0.17197, 0.1034, 0.00708, 0.40018, 0.65036, 0.47323, 0.59989, 0.46511, 0.71029, 0.53815, 0.52734, 0.62093, 0.44533, 0.70533, 0.46111, 0.76864, 0.60935, 0.82707, 0.71029, 0.90336, 0.63458, 0.89849, 0.4958, 0.88388, 0.37279, 0.53328, 0.71029, 0.61281, 0.64089, 0.70209, 0.65351, 0.51867, 0.82699, 0.61281, 0.85222, 0.70533, 0.77337, 0.83356, 0.81122, 0.95692, 0.62197, 0.94393, 0.46741, 0.32877, 0.65666, 0.2541, 0.54311, 0.1697, 0.51157, 0.06906, 0.47057, 0.07393, 0.34755, 0.10802, 0.16461, 0.13886, 0.34125, 0.19405, 0.34125, 0.26871, 0.40118, 0.33688, 0.51157, 0.30442, 0.27501, 0.23138, 0.21192], "triangles": [50, 14, 15, 50, 15, 16, 13, 14, 50, 45, 10, 11, 59, 16, 17, 50, 16, 59, 49, 50, 59, 12, 13, 50, 12, 50, 49, 45, 11, 12, 59, 17, 18, 46, 45, 12, 58, 59, 18, 19, 58, 18, 49, 59, 58, 48, 49, 58, 53, 52, 45, 46, 53, 45, 47, 46, 12, 12, 49, 48, 47, 12, 48, 20, 48, 58, 20, 58, 19, 56, 53, 46, 56, 46, 47, 57, 47, 48, 21, 57, 48, 56, 47, 57, 56, 52, 53, 24, 55, 56, 20, 21, 48, 23, 56, 57, 24, 56, 23, 22, 57, 21, 23, 57, 22, 44, 9, 10, 44, 10, 45, 44, 43, 8, 44, 8, 9, 7, 8, 43, 41, 7, 43, 6, 7, 41, 52, 43, 44, 52, 44, 45, 40, 6, 41, 51, 41, 43, 51, 43, 52, 42, 40, 41, 42, 41, 51, 30, 40, 42, 54, 42, 51, 56, 55, 52, 51, 52, 55, 54, 51, 55, 29, 30, 42, 29, 42, 54, 25, 55, 24, 27, 28, 54, 55, 27, 54, 29, 54, 28, 55, 26, 27, 25, 26, 55, 65, 39, 0, 1, 65, 0, 38, 39, 65, 71, 1, 2, 71, 2, 3, 70, 71, 3, 66, 65, 1, 67, 1, 71, 66, 1, 67, 64, 38, 65, 64, 65, 66, 37, 38, 64, 68, 71, 70, 67, 71, 68, 63, 36, 37, 64, 63, 37, 62, 66, 67, 62, 67, 68, 35, 36, 63, 63, 66, 62, 66, 63, 64, 34, 62, 33, 34, 63, 62, 35, 63, 34, 70, 3, 4, 69, 70, 4, 4, 5, 69, 68, 70, 69, 61, 62, 68, 61, 68, 69, 33, 62, 61, 60, 61, 69, 60, 69, 5, 32, 33, 61, 60, 32, 61, 31, 32, 60, 31, 60, 30, 40, 5, 6, 60, 5, 40, 30, 60, 40], "vertices": [2, 10, 75.85, -69.96, 1e-05, 11, 41.99, -66.64, 0.99999, 2, 10, 62.49, -45.78, 0.06981, 11, 26.84, -43.53, 0.93019, 2, 10, 43.65, -64.5, 0.30413, 11, 9.47, -63.61, 0.69587, 2, 10, 24.95, -65.59, 0.40297, 11, -9.1, -66.11, 0.59703, 3, 9, -4.53, 48.22, 0.00144, 10, 3.67, -46.27, 0.62654, 11, -31.77, -48.44, 0.37202, 4, 9, 0.32, 8.88, 0.53436, 10, -8.37, -8.5, 0.35511, 11, -46.61, -11.68, 0.01265, 12, -12.84, 7.07, 0.09787, 4, 9, 7.27, 9.35, 0.30474, 10, -15.11, -10.25, 0.02845, 11, -53.2, -13.93, 0.00085, 12, -6.06, 8.65, 0.66596, 1, 12, 6.12, 21.28, 1, 2, 12, 19.49, 35.14, 0.97438, 13, -42.63, 34.11, 0.02562, 2, 12, 36.9, 44.98, 0.82843, 13, -25.47, 44.37, 0.17157, 2, 12, 51.99, 53.5, 0.62383, 13, -10.58, 53.26, 0.37617, 2, 12, 73.59, 56.35, 0.39611, 13, 10.94, 56.64, 0.60389, 2, 12, 105.62, 32.91, 0.02114, 13, 43.53, 33.98, 0.97886, 2, 12, 107.5, 55.86, 0, 13, 44.85, 56.98, 1, 1, 13, 49.82, 69, 1, 1, 13, 56.8, 70.46, 1, 1, 13, 70.35, 61.2, 1, 1, 13, 79.74, 51.05, 1, 1, 13, 91.69, 31.16, 1, 1, 13, 92.45, 27.11, 1, 1, 13, 84.48, 4.22, 1, 1, 13, 72.11, -13.82, 1, 1, 13, 60.29, -22.26, 1, 2, 12, 82.85, -24.39, 0.08729, 13, 22.17, -23.85, 0.91271, 2, 12, 76.87, -24.28, 0.23347, 13, 16.18, -23.89, 0.76653, 3, 9, 66.05, -50.9, 0.00159, 12, 61.59, -41.45, 0.75523, 13, 1.33, -41.43, 0.24318, 3, 9, 53.46, -55.23, 0.00733, 12, 49.86, -47.73, 0.87355, 13, -10.24, -48, 0.11913, 3, 9, 48.11, -55.23, 0.01076, 12, 44.57, -48.59, 0.89832, 13, -15.5, -48.98, 0.09092, 3, 9, 37.32, -51.66, 0.02473, 12, 33.35, -46.78, 0.93451, 13, -26.76, -47.45, 0.04076, 3, 9, 15.33, -37.02, 0.12554, 12, 9.31, -35.84, 0.87444, 13, -51.07, -37.1, 2e-05, 3, 9, 2.72, -17.21, 0.48876, 10, -15.56, 16.69, 0.08889, 12, -6.3, -18.3, 0.42235, 2, 9, -22.81, -17.34, 0.10752, 10, 9.51, 21.55, 0.89248, 2, 9, -33.68, -14.36, 0.01894, 10, 20.75, 20.63, 0.98106, 2, 10, 35.67, 14.22, 0.99908, 11, -4.4, 14.29, 0.00092, 2, 10, 72.52, 22.76, 0.05821, 11, 31.7, 25.57, 0.94179, 2, 10, 87.69, 13, 0.00398, 11, 47.57, 16.97, 0.99602, 1, 11, 58.21, 3.41, 1, 1, 11, 60.94, -6.71, 1, 1, 11, 57.95, -38.22, 1, 1, 11, 48.12, -64.91, 1, 3, 9, 3.86, -5.93, 0.65584, 10, -14.59, 5.4, 0.02472, 12, -6.98, -6.99, 0.31944, 1, 12, 11.64, 3.23, 1, 2, 9, 21.65, -14.38, 0.08956, 12, 11.93, -12.49, 0.91044, 2, 12, 27.57, 16.17, 0.9934, 13, -34.09, 15.34, 0.0066, 2, 12, 48.11, 31.21, 0.7745, 13, -13.92, 30.88, 0.2255, 2, 12, 71.3, 32.71, 0.34348, 13, 9.22, 32.94, 0.65652, 2, 12, 91.76, 14.84, 0.01967, 13, 30.11, 15.58, 0.98033, 1, 13, 48.47, 4.54, 1, 1, 13, 67.05, 18.87, 1, 2, 12, 124.32, 36.33, 0.0001, 13, 62.14, 37.86, 0.9999, 1, 13, 55.02, 54.18, 1, 2, 9, 40.33, -14.38, 0.0074, 12, 30.37, -9.5, 0.9926, 1, 12, 50.32, 3.63, 1, 2, 12, 74.75, 5.78, 0.00913, 13, 13.34, 6.11, 0.99087, 3, 9, 36.33, -30.84, 0.03639, 12, 29.05, -26.39, 0.95336, 13, -31.57, -27.17, 0.01025, 3, 9, 62.12, -34.4, 0.00134, 12, 55.08, -25.78, 0.76062, 13, -5.56, -25.92, 0.23804, 2, 12, 78.33, -10.76, 0.05752, 13, 17.32, -10.34, 0.94248, 1, 13, 52.83, -9.12, 1, 1, 13, 81.15, 23.32, 1, 1, 13, 73.65, 44.09, 1, 2, 9, -15.71, -6.82, 0.14376, 10, 4.47, 9.89, 0.85624, 2, 10, 27.54, -2.06, 0.97285, 11, -11.29, -2.56, 0.02715, 1, 11, 12.2, -0.88, 1, 2, 10, 79.26, -2.73, 0.00011, 11, 40.34, 0.66, 0.99989, 1, 11, 43.53, -16.45, 1, 2, 10, 76.75, -47.1, 0.00703, 11, 41.17, -43.78, 0.99297, 2, 10, 63.84, -24.19, 0.01327, 11, 26.57, -21.9, 0.98673, 2, 10, 48.98, -26.98, 0.10952, 11, 11.96, -25.8, 0.89048, 2, 10, 27.31, -22.47, 0.5525, 11, -9.98, -22.92, 0.4475, 3, 9, -13.48, 13.63, 0.03121, 10, 6.07, -10.62, 0.91716, 11, -32.05, -12.71, 0.05164, 2, 10, 20.98, -41.76, 0.52182, 11, -14.84, -42.64, 0.47818, 2, 10, 42.3, -46.8, 0.26952, 11, 6.79, -46.06, 0.73048], "hull": 40}}}}], "animations": {"animation": {"slots": {"snow-1": {"rgba": [{"color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}, {"time": 4, "color": "ffffff00"}]}, "snow-2": {"rgba": [{"color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}, {"time": 4, "color": "ffffff00"}]}, "snow-3": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 2, "color": "ffffff00"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 4, "color": "ffffffff"}]}, "snow-4": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 2, "color": "ffffff00"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3, "color": "ffffffff", "curve": "stepped"}, {"time": 4, "color": "ffffffff"}]}, "snow-5": {"rgba": [{"color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}, {"time": 4, "color": "ffffff00"}]}, "snow-6": {"rgba": [{"color": "ffffff00", "curve": [0.222, 1, 0.444, 1, 0.222, 1, 0.444, 1, 0.222, 1, 0.444, 1, 0.222, 0, 0.444, 1]}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8, "color": "ffffffff", "curve": [3.022, 1, 3.244, 1, 3.022, 1, 3.244, 1, 3.022, 1, 3.244, 1, 3.022, 1, 3.244, 0]}, {"time": 3.4667, "color": "ffffff00"}]}, "snow-7": {"rgba": [{"color": "ffffffff", "curve": [0.112, 1, 0.556, 1, 0.112, 1, 0.556, 1, 0.112, 1, 0.556, 1, 0.112, 1, 0.556, 0]}, {"time": 0.6667, "color": "ffffff00", "curve": [0.889, 1, 1.111, 1, 0.889, 1, 1.111, 1, 0.889, 1, 1.111, 1, 0.889, 0, 1.111, 1]}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 4, "color": "ffffffff"}]}, "snow-8": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": [1.556, 1, 1.778, 1, 1.556, 1, 1.778, 1, 1.556, 1, 1.778, 1, 1.556, 1, 1.778, 0]}, {"time": 2, "color": "ffffff00", "curve": [2.222, 1, 2.444, 1, 2.222, 1, 2.444, 1, 2.222, 1, 2.444, 1, 2.222, 0, 2.444, 1]}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 4, "color": "ffffffff"}]}, "snow-9": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff", "curve": [0.556, 1, 0.778, 1, 0.556, 1, 0.778, 1, 0.556, 1, 0.778, 1, 0.556, 1, 0.778, 0]}, {"time": 1, "color": "ffffff00", "curve": [1.222, 1, 1.444, 1, 1.222, 1, 1.444, 1, 1.222, 1, 1.444, 1, 1.222, 0, 1.444, 1]}, {"time": 1.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 4, "color": "ffffffff"}]}, "snow-10": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 2.3333, "color": "ffffffff", "curve": [2.556, 1, 2.778, 1, 2.556, 1, 2.778, 1, 2.556, 1, 2.778, 1, 2.556, 1, 2.778, 0]}, {"time": 3, "color": "ffffff00", "curve": [3.222, 1, 3.444, 1, 3.222, 1, 3.444, 1, 3.222, 1, 3.444, 1, 3.222, 0, 3.444, 1]}, {"time": 3.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 4, "color": "ffffffff"}]}, "snow-11": {"rgba": [{"color": "ffffff00", "curve": [0.222, 1, 0.444, 1, 0.222, 1, 0.444, 1, 0.222, 1, 0.444, 1, 0.222, 0, 0.444, 1]}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff", "curve": [3.556, 1, 3.778, 1, 3.556, 1, 3.778, 1, 3.556, 1, 3.778, 1, 3.556, 1, 3.778, 0]}, {"time": 4, "color": "ffffff00"}]}, "snow-12": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": [0.889, 1, 1.111, 1, 0.889, 1, 1.111, 1, 0.889, 1, 1.111, 1, 0.889, 1, 1.111, 0]}, {"time": 1.3333, "color": "ffffff00", "curve": [1.556, 1, 1.778, 1, 1.556, 1, 1.778, 1, 1.556, 1, 1.778, 1, 1.556, 0, 1.778, 1]}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 4, "color": "ffffffff"}]}, "snow-13": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": [2.222, 1, 2.444, 1, 2.222, 1, 2.444, 1, 2.222, 1, 2.444, 1, 2.222, 1, 2.444, 0]}, {"time": 2.6667, "color": "ffffff00", "curve": [2.889, 1, 3.111, 1, 2.889, 1, 3.111, 1, 2.889, 1, 3.111, 1, 2.889, 0, 3.111, 1]}, {"time": 3.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 4, "color": "ffffffff"}]}, "snow-14": {"rgba": [{"color": "ffffff00", "curve": [0.222, 1, 0.444, 1, 0.222, 1, 0.444, 1, 0.222, 1, 0.444, 1, 0.222, 0, 0.444, 1]}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff", "curve": [3.556, 1, 3.778, 1, 3.556, 1, 3.778, 1, 3.556, 1, 3.778, 1, 3.556, 1, 3.778, 0]}, {"time": 4, "color": "ffffff00"}]}, "snow-15": {"rgba": [{"color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}, {"time": 4, "color": "ffffff00"}]}, "snow-16": {"rgba": [{"color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 4, "color": "ffffffff"}]}, "snow-17": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 4, "color": "ffffffff"}]}, "snow-18": {"rgba": [{"color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}, {"time": 4, "color": "ffffff00"}]}, "snow-19": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 4, "color": "ffffffff"}]}, "snow-20": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00"}, {"time": 3, "color": "ffffffff", "curve": "stepped"}, {"time": 4, "color": "ffffffff"}]}, "snow-21": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff00"}, {"time": 4, "color": "ffffffff"}]}, "snow-22": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 1, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00"}, {"time": 2.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 4, "color": "ffffffff"}]}, "snow-24": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 2, "color": "ffffff00"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 4, "color": "ffffffff"}]}, "tx_Point_070": {"rgba": [{"color": "ffffff00", "curve": [0.111, 1, 0.222, 1, 0.111, 1, 0.222, 1, 0.111, 1, 0.222, 1, 0.111, 0, 0.222, 1]}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff", "curve": [1.022, 1, 1.511, 1, 1.022, 1, 1.511, 1, 1.022, 1, 1.511, 1, 1.022, 1, 1.511, 0]}, {"time": 2, "color": "ffffff00", "curve": [2.111, 1, 2.222, 1, 2.111, 1, 2.222, 1, 2.111, 1, 2.222, 1, 2.111, 0, 2.222, 1]}, {"time": 2.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5333, "color": "ffffffff", "curve": [3.022, 1, 3.511, 1, 3.022, 1, 3.511, 1, 3.022, 1, 3.511, 1, 3.022, 1, 3.511, 0.98]}, {"time": 4, "color": "ffffff00"}]}, "tx_Point_071": {"rgba": [{"color": "ffffffd2", "curve": [0.223, 1, 0.445, 1, 0.223, 1, 0.445, 1, 0.223, 1, 0.445, 1, 0.223, 0.69, 0.445, 0.44]}, {"time": 0.6667, "color": "ffffff00", "curve": [0.778, 1, 0.889, 1, 0.778, 1, 0.889, 1, 0.778, 1, 0.889, 1, 0.778, 0, 0.889, 1]}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2, "color": "ffffffff", "curve": [1.689, 1, 2.178, 1, 1.689, 1, 2.178, 1, 1.689, 1, 2.178, 1, 1.689, 1, 2.178, 0]}, {"time": 2.6667, "color": "ffffff00", "curve": [2.778, 1, 2.889, 1, 2.778, 1, 2.889, 1, 2.778, 1, 2.889, 1, 2.778, 0, 2.889, 1]}, {"time": 3, "color": "ffffffff", "curve": "stepped"}, {"time": 3.2, "color": "ffffffff", "curve": [3.467, 1, 3.735, 1, 3.467, 1, 3.735, 1, 3.467, 1, 3.735, 1, 3.467, 1, 3.735, 0.99]}, {"time": 4, "color": "ffffffd2"}]}, "tx_Point_072": {"rgba": [{"color": "ffffff00", "curve": [0.111, 1, 0.222, 1, 0.111, 1, 0.222, 1, 0.111, 1, 0.222, 1, 0.111, 0, 0.222, 1]}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff", "curve": [1.022, 1, 1.511, 1, 1.022, 1, 1.511, 1, 1.022, 1, 1.511, 1, 1.022, 1, 1.511, 0.98]}, {"time": 2, "color": "ffffff00", "curve": [2.111, 1, 2.222, 1, 2.111, 1, 2.222, 1, 2.111, 1, 2.222, 1, 2.111, 0, 2.222, 1]}, {"time": 2.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5333, "color": "ffffffff", "curve": [3.022, 1, 3.511, 1, 3.022, 1, 3.511, 1, 3.022, 1, 3.511, 1, 3.022, 1, 3.511, 0]}, {"time": 4, "color": "ffffff00"}]}, "tx_Point_073": {"rgba": [{"color": "fffffffe", "curve": [0.447, 1, 0.89, 1, 0.447, 1, 0.89, 1, 0.447, 1, 0.89, 1, 0.447, 0.99, 0.89, 0.89]}, {"time": 1.3333, "color": "ffffff00", "curve": [1.444, 1, 1.556, 1, 1.444, 1, 1.556, 1, 1.444, 1, 1.556, 1, 1.444, 0, 1.556, 1]}, {"time": 1.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.8667, "color": "ffffffff", "curve": [2.356, 1, 2.844, 1, 2.356, 1, 2.844, 1, 2.356, 1, 2.844, 1, 2.356, 1, 2.844, 0]}, {"time": 3.3333, "color": "ffffff00", "curve": [3.444, 1, 3.556, 1, 3.444, 1, 3.556, 1, 3.444, 1, 3.556, 1, 3.444, 0, 3.556, 1]}, {"time": 3.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.8667, "color": "ffffffff", "curve": "stepped"}, {"time": 4, "color": "fffffffe"}]}, "tx_Point_074": {"rgba": [{"color": "fffffff8", "curve": [0.447, 1, 0.89, 1, 0.447, 1, 0.89, 1, 0.447, 1, 0.89, 1, 0.447, 0.82, 0.89, 0]}, {"time": 1.3333, "color": "ffffff00", "curve": [1.444, 1, 1.556, 1, 1.444, 1, 1.556, 1, 1.444, 1, 1.556, 1, 1.444, 0, 1.556, 1]}, {"time": 1.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.8667, "color": "ffffffff", "curve": [2.356, 1, 2.844, 1, 2.356, 1, 2.844, 1, 2.356, 1, 2.844, 1, 2.356, 1, 2.844, 0.98]}, {"time": 3.3333, "color": "ffffff00", "curve": [3.444, 1, 3.556, 1, 3.444, 1, 3.556, 1, 3.444, 1, 3.556, 1, 3.444, 0, 3.556, 1]}, {"time": 3.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.8667, "color": "ffffffff", "curve": [3.912, 1, 3.958, 1, 3.912, 1, 3.958, 1, 3.912, 1, 3.958, 1, 3.912, 1, 3.958, 0.99]}, {"time": 4, "color": "fffffff8"}]}, "tx_Point_075": {"rgba": [{"color": "ffffff00", "curve": [0.111, 1, 0.222, 1, 0.111, 1, 0.222, 1, 0.111, 1, 0.222, 1, 0.111, 0, 0.222, 1]}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff", "curve": [1.022, 1, 1.511, 1, 1.022, 1, 1.511, 1, 1.022, 1, 1.511, 1, 1.022, 1, 1.511, 0]}, {"time": 2, "color": "ffffff00", "curve": [2.111, 1, 2.222, 1, 2.111, 1, 2.222, 1, 2.111, 1, 2.222, 1, 2.111, 0, 2.222, 1]}, {"time": 2.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5333, "color": "ffffffff", "curve": [3.022, 1, 3.511, 1, 3.022, 1, 3.511, 1, 3.022, 1, 3.511, 1, 3.022, 1, 3.511, 0.98]}, {"time": 4, "color": "ffffff00"}]}, "tx_Point_076": {"rgba": [{"color": "ffffff00", "curve": [0.111, 1, 0.222, 1, 0.111, 1, 0.222, 1, 0.111, 1, 0.222, 1, 0.111, 0, 0.222, 1]}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff", "curve": [1.022, 1, 1.511, 1, 1.022, 1, 1.511, 1, 1.022, 1, 1.511, 1, 1.022, 1, 1.511, 0]}, {"time": 2, "color": "ffffff00", "curve": [2.111, 1, 2.222, 1, 2.111, 1, 2.222, 1, 2.111, 1, 2.222, 1, 2.111, 0, 2.222, 1]}, {"time": 2.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5333, "color": "ffffffff", "curve": [3.022, 1, 3.511, 1, 3.022, 1, 3.511, 1, 3.022, 1, 3.511, 1, 3.022, 1, 3.511, 0.98]}, {"time": 4, "color": "ffffff00"}]}, "tx_Point_077": {"rgba": [{"color": "ffffff00", "curve": [0.111, 1, 0.222, 1, 0.111, 1, 0.222, 1, 0.111, 1, 0.222, 1, 0.111, 0, 0.222, 1]}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff", "curve": [1.022, 1, 1.511, 1, 1.022, 1, 1.511, 1, 1.022, 1, 1.511, 1, 1.022, 1, 1.511, 0]}, {"time": 2, "color": "ffffff00", "curve": [2.111, 1, 2.222, 1, 2.111, 1, 2.222, 1, 2.111, 1, 2.222, 1, 2.111, 0, 2.222, 1]}, {"time": 2.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5333, "color": "ffffffff", "curve": [3.022, 1, 3.511, 1, 3.022, 1, 3.511, 1, 3.022, 1, 3.511, 1, 3.022, 1, 3.511, 0.98]}, {"time": 4, "color": "ffffff00"}]}, "tx_Point_078": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": [0.689, 1, 1.178, 1, 0.689, 1, 1.178, 1, 0.689, 1, 1.178, 1, 0.689, 1, 1.178, 0.98]}, {"time": 1.6667, "color": "ffffff00", "curve": [1.778, 1, 1.889, 1, 1.778, 1, 1.889, 1, 1.778, 1, 1.889, 1, 1.778, 0, 1.889, 1]}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2, "color": "ffffffff", "curve": [2.689, 1, 3.178, 1, 2.689, 1, 3.178, 1, 2.689, 1, 3.178, 1, 2.689, 1, 3.178, 0]}, {"time": 3.6667, "color": "ffffff00", "curve": [3.778, 1, 3.889, 1, 3.778, 1, 3.889, 1, 3.778, 1, 3.889, 1, 3.778, 0, 3.889, 1]}, {"time": 4, "color": "ffffffff"}]}, "tx_Point_079": {"rgba": [{"color": "fffffff8", "curve": [0.447, 1, 0.89, 1, 0.447, 1, 0.89, 1, 0.447, 1, 0.89, 1, 0.447, 0.82, 0.89, 0]}, {"time": 1.3333, "color": "ffffff00", "curve": [1.444, 1, 1.556, 1, 1.444, 1, 1.556, 1, 1.444, 1, 1.556, 1, 1.444, 0, 1.556, 1]}, {"time": 1.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.8667, "color": "ffffffff", "curve": [2.356, 1, 2.844, 1, 2.356, 1, 2.844, 1, 2.356, 1, 2.844, 1, 2.356, 1, 2.844, 0.98]}, {"time": 3.3333, "color": "ffffff00", "curve": [3.444, 1, 3.556, 1, 3.444, 1, 3.556, 1, 3.444, 1, 3.556, 1, 3.444, 0, 3.556, 1]}, {"time": 3.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.8667, "color": "ffffffff", "curve": [3.912, 1, 3.958, 1, 3.912, 1, 3.958, 1, 3.912, 1, 3.958, 1, 3.912, 1, 3.958, 0.99]}, {"time": 4, "color": "fffffff8"}]}, "tx_Point_080": {"rgba": [{"color": "fffffff5", "curve": [0.335, 1, 0.668, 1, 0.335, 1, 0.668, 1, 0.335, 1, 0.668, 1, 0.335, 0.89, 0.668, 0.66]}, {"time": 1, "color": "ffffff00", "curve": [1.111, 1, 1.222, 1, 1.111, 1, 1.222, 1, 1.111, 1, 1.222, 1, 1.111, 0, 1.222, 1]}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5333, "color": "ffffffff", "curve": [2.022, 1, 2.511, 1, 2.022, 1, 2.511, 1, 2.022, 1, 2.511, 1, 2.022, 1, 2.511, 0]}, {"time": 3, "color": "ffffff00", "curve": [3.111, 1, 3.222, 1, 3.111, 1, 3.222, 1, 3.111, 1, 3.222, 1, 3.111, 0, 3.222, 1]}, {"time": 3.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.5333, "color": "ffffffff", "curve": [3.69, 1, 3.846, 1, 3.69, 1, 3.846, 1, 3.69, 1, 3.846, 1, 3.69, 1, 3.846, 1]}, {"time": 4, "color": "fffffff5"}]}, "tx_Point_081": {"rgba": [{"color": "ffffff45", "curve": [0.168, 1, 0.334, 1, 0.168, 1, 0.334, 1, 0.168, 1, 0.334, 1, 0.168, 0.12, 0.334, 0]}, {"time": 0.5, "color": "ffffff00", "curve": [0.611, 1, 0.722, 1, 0.611, 1, 0.722, 1, 0.611, 1, 0.722, 1, 0.611, 0, 0.722, 1]}, {"time": 0.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0333, "color": "ffffffff", "curve": [1.522, 1, 2.011, 1, 1.522, 1, 2.011, 1, 1.522, 1, 2.011, 1, 1.522, 1, 2.011, 0.98]}, {"time": 2.5, "color": "ffffff00", "curve": [2.611, 1, 2.722, 1, 2.611, 1, 2.722, 1, 2.611, 1, 2.722, 1, 2.611, 0, 2.722, 1]}, {"time": 2.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.0333, "color": "ffffffff", "curve": [3.356, 1, 3.679, 1, 3.356, 1, 3.679, 1, 3.356, 1, 3.679, 1, 3.356, 1, 3.679, 0.57]}, {"time": 4, "color": "ffffff45"}]}, "tx_Point_082": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": [0.689, 1, 1.178, 1, 0.689, 1, 1.178, 1, 0.689, 1, 1.178, 1, 0.689, 1, 1.178, 0.98]}, {"time": 1.6667, "color": "ffffff00", "curve": [1.778, 1, 1.889, 1, 1.778, 1, 1.889, 1, 1.778, 1, 1.889, 1, 1.778, 0, 1.889, 1]}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2, "color": "ffffffff", "curve": [2.689, 1, 3.178, 1, 2.689, 1, 3.178, 1, 2.689, 1, 3.178, 1, 2.689, 1, 3.178, 0]}, {"time": 3.6667, "color": "ffffff00", "curve": [3.778, 1, 3.889, 1, 3.778, 1, 3.889, 1, 3.778, 1, 3.889, 1, 3.778, 0, 3.889, 1]}, {"time": 4, "color": "ffffffff"}]}}, "bones": {"F": {"translate": [{"curve": [0.332, 0, 0.668, 0, 0.332, 0, 0.668, -17.61]}, {"time": 1, "y": -17.61, "curve": [1.332, 0, 1.667, 0, 1.332, -17.61, 1.667, 0]}, {"time": 2, "curve": [2.332, 0, 2.668, 0, 2.332, 0, 2.668, -17.61]}, {"time": 3, "y": -17.61, "curve": [3.332, 0, 3.668, 0, 3.332, -17.61, 3.668, 0]}, {"time": 4}]}, "F-d2": {"rotate": [{"value": -0.04, "curve": [0.043, -0.02, 0.092, 0]}, {"time": 0.1333, "curve": [0.465, 0, 0.801, -0.81]}, {"time": 1.1333, "value": -0.81, "curve": [1.424, -0.81, 1.711, -0.18]}, {"time": 2, "value": -0.04, "curve": [2.043, -0.02, 2.092, 0]}, {"time": 2.1333, "curve": [2.465, 0, 2.801, -0.81]}, {"time": 3.1333, "value": -0.81, "curve": [3.424, -0.81, 3.711, -0.19]}, {"time": 4, "value": -0.04}], "scale": [{"x": 0.995, "curve": [0.043, 0.998, 0.092, 1, 0.043, 1, 0.092, 1]}, {"time": 0.1333, "curve": [0.465, 1, 0.801, 0.898, 0.465, 1, 0.801, 1]}, {"time": 1.1333, "x": 0.898, "curve": [1.424, 0.898, 1.711, 0.977, 1.424, 1, 1.711, 1]}, {"time": 2, "x": 0.995, "curve": [2.043, 0.998, 2.092, 1, 2.043, 1, 2.092, 1]}, {"time": 2.1333, "curve": [2.465, 1, 2.801, 0.898, 2.465, 1, 2.801, 1]}, {"time": 3.1333, "x": 0.898, "curve": [3.424, 0.898, 3.711, 0.976, 3.424, 1, 3.711, 1]}, {"time": 4, "x": 0.995}]}, "F-d3": {"rotate": [{"value": -0.13, "curve": [0.085, -0.06, 0.151, 0]}, {"time": 0.2333, "curve": [0.565, 0, 0.901, -0.81]}, {"time": 1.2333, "value": -0.81, "curve": [1.483, -0.81, 1.744, -0.35]}, {"time": 2, "value": -0.13, "curve": [2.085, -0.06, 2.151, 0]}, {"time": 2.2333, "curve": [2.565, 0, 2.901, -0.81]}, {"time": 3.2333, "value": -0.81, "curve": [3.483, -0.81, 3.753, -0.35]}, {"time": 4, "value": -0.13}], "scale": [{"x": 0.984, "curve": [0.085, 0.993, 0.151, 1, 0.085, 1, 0.151, 1]}, {"time": 0.2333, "curve": [0.565, 1, 0.901, 0.898, 0.565, 1, 0.901, 1]}, {"time": 1.2333, "x": 0.898, "curve": [1.483, 0.898, 1.744, 0.955, 1.483, 1, 1.744, 1]}, {"time": 2, "x": 0.984, "curve": [2.085, 0.993, 2.151, 1, 2.085, 1, 2.151, 1]}, {"time": 2.2333, "curve": [2.565, 1, 2.901, 0.898, 2.565, 1, 2.901, 1]}, {"time": 3.2333, "x": 0.898, "curve": [3.483, 0.898, 3.753, 0.955, 3.483, 1, 3.753, 1]}, {"time": 4, "x": 0.984}]}, "F-d4": {"rotate": [{"value": -0.26, "curve": [0.126, -0.12, 0.243, 0]}, {"time": 0.3667, "curve": [0.699, 0, 1.035, -0.81]}, {"time": 1.3667, "value": -0.81, "curve": [1.575, -0.81, 1.789, -0.49]}, {"time": 2, "value": -0.26, "curve": [2.126, -0.12, 2.243, 0]}, {"time": 2.3667, "curve": [2.699, 0, 3.035, -0.81]}, {"time": 3.3667, "value": -0.81, "curve": [3.575, -0.81, 3.794, -0.49]}, {"time": 4, "value": -0.26}], "scale": [{"x": 0.967, "curve": [0.126, 0.985, 0.243, 1, 0.126, 1, 0.243, 1]}, {"time": 0.3667, "curve": [0.699, 1, 1.035, 0.898, 0.699, 1, 1.035, 1]}, {"time": 1.3667, "x": 0.898, "curve": [1.575, 0.898, 1.789, 0.937, 1.575, 1, 1.789, 1]}, {"time": 2, "x": 0.967, "curve": [2.126, 0.985, 2.243, 1, 2.126, 1, 2.243, 1]}, {"time": 2.3667, "curve": [2.699, 1, 3.035, 0.898, 2.699, 1, 3.035, 1]}, {"time": 3.3667, "x": 0.898, "curve": [3.575, 0.898, 3.794, 0.937, 3.575, 1, 3.794, 1]}, {"time": 4, "x": 0.967}]}, "F-a": {"rotate": [{"value": -1.4, "curve": [0.113, -1.67, 0.223, -1.89]}, {"time": 0.3333, "value": -1.89, "curve": [0.667, -1.89, 1, 0]}, {"time": 1.3333, "curve": [1.557, 0, 1.778, -0.86]}, {"time": 2, "value": -1.4, "curve": [2.113, -1.67, 2.223, -1.89]}, {"time": 2.3333, "value": -1.89, "curve": [2.667, -1.89, 3, 0]}, {"time": 3.3333, "curve": [3.557, 0, 3.78, -0.84]}, {"time": 4, "value": -1.4}]}, "N": {"translate": [{"curve": [0.333, 0, 0.667, 0, 0.333, 0, 0.667, 11.8]}, {"time": 1, "y": 11.8, "curve": [1.333, 0, 1.667, 0, 1.333, 11.8, 1.667, 0]}, {"time": 2, "curve": [2.333, 0, 2.667, 0, 2.333, 0, 2.667, 11.8]}, {"time": 3, "y": 11.8, "curve": [3.333, 0, 3.667, 0, 3.333, 11.8, 3.667, 0]}, {"time": 4}]}, "N-c": {"rotate": [{"value": -0.86, "curve": [0.058, -0.37, 0.112, 0]}, {"time": 0.1667, "curve": [0.5, 0, 0.833, -10.88]}, {"time": 1.1667, "value": -10.88, "curve": [1.446, -10.88, 1.722, -3.19]}, {"time": 2, "value": -0.86, "curve": [2.058, -0.37, 2.112, 0]}, {"time": 2.1667, "curve": [2.5, 0, 2.833, -10.88]}, {"time": 3.1667, "value": -10.88, "curve": [3.446, -10.88, 3.724, -3.34]}, {"time": 4, "value": -0.86}]}, "N-c2": {"rotate": [{"value": -2.84, "curve": [0.113, -1.26, 0.223, 0]}, {"time": 0.3333, "curve": [0.667, 0, 1, -10.88]}, {"time": 1.3333, "value": -10.88, "curve": [1.557, -10.88, 1.778, -5.96]}, {"time": 2, "value": -2.84, "curve": [2.113, -1.26, 2.223, 0]}, {"time": 2.3333, "curve": [2.667, 0, 3, -10.88]}, {"time": 3.3333, "value": -10.88, "curve": [3.557, -10.88, 3.78, -6.07]}, {"time": 4, "value": -2.84}]}, "N-c3": {"rotate": [{"value": -5.44, "curve": [0.169, -2.74, 0.334, 0]}, {"time": 0.5, "curve": [0.833, 0, 1.167, -10.88]}, {"time": 1.5, "value": -10.88, "curve": [1.668, -10.88, 1.833, -8.1]}, {"time": 2, "value": -5.44, "curve": [2.169, -2.74, 2.334, 0]}, {"time": 2.5, "curve": [2.833, 0, 3.167, -10.88]}, {"time": 3.5, "value": -10.88, "curve": [3.668, -10.88, 3.836, -8.18]}, {"time": 4, "value": -5.44}]}, "F-c2": {"rotate": [{"value": 7.25, "curve": [0.058, 8.04, 0.112, 8.64]}, {"time": 0.1667, "value": 8.64, "curve": [0.5, 8.64, 0.833, -9.09]}, {"time": 1.1667, "value": -9.09, "curve": [1.446, -9.09, 1.722, 3.44]}, {"time": 2, "value": 7.25, "curve": [2.058, 8.04, 2.112, 8.64]}, {"time": 2.1667, "value": 8.64, "curve": [2.5, 8.64, 2.833, -9.09]}, {"time": 3.1667, "value": -9.09, "curve": [3.446, -9.09, 3.724, 3.19]}, {"time": 4, "value": 7.25}]}, "F-c3": {"rotate": [{"value": 0.89, "curve": [0.113, 2.66, 0.223, 4.06]}, {"time": 0.3333, "value": 4.06, "curve": [0.667, 4.06, 1, -8.1]}, {"time": 1.3333, "value": -8.1, "curve": [1.557, -8.1, 1.778, -2.59]}, {"time": 2, "value": 0.89, "curve": [2.113, 2.66, 2.223, 4.06]}, {"time": 2.3333, "value": 4.06, "curve": [2.667, 4.06, 3, -8.1]}, {"time": 3.3333, "value": -8.1, "curve": [3.557, -8.1, 3.78, -2.72]}, {"time": 4, "value": 0.89}]}, "F-c7": {"rotate": [{"value": -2.02, "curve": [0.169, 1, 0.334, 4.06]}, {"time": 0.5, "value": 4.06, "curve": [0.833, 4.06, 1.167, -8.1]}, {"time": 1.5, "value": -8.1, "curve": [1.668, -8.1, 1.833, -5]}, {"time": 2, "value": -2.02, "curve": [2.169, 1, 2.334, 4.06]}, {"time": 2.5, "value": 4.06, "curve": [2.833, 4.06, 3.167, -8.1]}, {"time": 3.5, "value": -8.1, "curve": [3.668, -8.1, 3.836, -5.08]}, {"time": 4, "value": -2.02}]}, "F-c8": {"rotate": [{"value": -4.92, "curve": [0.224, -1.32, 0.446, 4.06]}, {"time": 0.6667, "value": 4.06, "curve": [1, 4.06, 1.333, -8.1]}, {"time": 1.6667, "value": -8.1, "curve": [1.779, -8.1, 1.889, -6.7]}, {"time": 2, "value": -4.92, "curve": [2.224, -1.32, 2.446, 4.06]}, {"time": 2.6667, "value": 4.06, "curve": [3, 4.06, 3.333, -8.1]}, {"time": 3.6667, "value": -8.1, "curve": [3.779, -8.1, 3.891, -6.75]}, {"time": 4, "value": -4.92}]}, "F-c10": {"rotate": [{"value": -4.92, "curve": [0.224, -1.32, 0.446, 4.06]}, {"time": 0.6667, "value": 4.06, "curve": [1, 4.06, 1.333, -8.1]}, {"time": 1.6667, "value": -8.1, "curve": [1.779, -8.1, 1.889, -6.7]}, {"time": 2, "value": -4.92, "curve": [2.224, -1.32, 2.446, 4.06]}, {"time": 2.6667, "value": 4.06, "curve": [3, 4.06, 3.333, -8.1]}, {"time": 3.6667, "value": -8.1, "curve": [3.779, -8.1, 3.891, -6.75]}, {"time": 4, "value": -4.92}]}, "F-c9": {"rotate": [{"value": -2.02, "curve": [0.169, 1, 0.334, 4.06]}, {"time": 0.5, "value": 4.06, "curve": [0.833, 4.06, 1.167, -8.1]}, {"time": 1.5, "value": -8.1, "curve": [1.668, -8.1, 1.833, -5]}, {"time": 2, "value": -2.02, "curve": [2.169, 1, 2.334, 4.06]}, {"time": 2.5, "value": 4.06, "curve": [2.833, 4.06, 3.167, -8.1]}, {"time": 3.5, "value": -8.1, "curve": [3.668, -8.1, 3.836, -5.08]}, {"time": 4, "value": -2.02}]}, "F-c6": {"rotate": [{"value": 0.89, "curve": [0.113, 2.66, 0.223, 4.06]}, {"time": 0.3333, "value": 4.06, "curve": [0.667, 4.06, 1, -8.1]}, {"time": 1.3333, "value": -8.1, "curve": [1.557, -8.1, 1.778, -2.59]}, {"time": 2, "value": 0.89, "curve": [2.113, 2.66, 2.223, 4.06]}, {"time": 2.3333, "value": 4.06, "curve": [2.667, 4.06, 3, -8.1]}, {"time": 3.3333, "value": -8.1, "curve": [3.557, -8.1, 3.78, -2.72]}, {"time": 4, "value": 0.89}]}, "F-c5": {"rotate": [{"value": 7.25, "curve": [0.058, 8.04, 0.112, 8.64]}, {"time": 0.1667, "value": 8.64, "curve": [0.5, 8.64, 0.833, -9.09]}, {"time": 1.1667, "value": -9.09, "curve": [1.446, -9.09, 1.722, 3.44]}, {"time": 2, "value": 7.25, "curve": [2.058, 8.04, 2.112, 8.64]}, {"time": 2.1667, "value": 8.64, "curve": [2.5, 8.64, 2.833, -9.09]}, {"time": 3.1667, "value": -9.09, "curve": [3.446, -9.09, 3.724, 3.19]}, {"time": 4, "value": 7.25}]}, "N-c6": {"rotate": [{"value": -5.44, "curve": [0.169, -2.74, 0.334, 0]}, {"time": 0.5, "curve": [0.833, 0, 1.167, -10.88]}, {"time": 1.5, "value": -10.88, "curve": [1.668, -10.88, 1.833, -8.1]}, {"time": 2, "value": -5.44, "curve": [2.169, -2.74, 2.334, 0]}, {"time": 2.5, "curve": [2.833, 0, 3.167, -10.88]}, {"time": 3.5, "value": -10.88, "curve": [3.668, -10.88, 3.836, -8.18]}, {"time": 4, "value": -5.44}]}, "N-c5": {"rotate": [{"value": -2.84, "curve": [0.113, -1.26, 0.223, 0]}, {"time": 0.3333, "curve": [0.667, 0, 1, -10.88]}, {"time": 1.3333, "value": -10.88, "curve": [1.557, -10.88, 1.778, -5.96]}, {"time": 2, "value": -2.84, "curve": [2.113, -1.26, 2.223, 0]}, {"time": 2.3333, "curve": [2.667, 0, 3, -10.88]}, {"time": 3.3333, "value": -10.88, "curve": [3.557, -10.88, 3.78, -6.07]}, {"time": 4, "value": -2.84}]}, "N-c4": {"rotate": [{"value": -0.86, "curve": [0.058, -0.37, 0.112, 0]}, {"time": 0.1667, "curve": [0.5, 0, 0.833, -10.88]}, {"time": 1.1667, "value": -10.88, "curve": [1.446, -10.88, 1.722, -3.19]}, {"time": 2, "value": -0.86, "curve": [2.058, -0.37, 2.112, 0]}, {"time": 2.1667, "curve": [2.5, 0, 2.833, -10.88]}, {"time": 3.1667, "value": -10.88, "curve": [3.446, -10.88, 3.724, -3.34]}, {"time": 4, "value": -0.86}]}, "tutem": {"scale": [{"curve": [0.333, 1, 0.667, 1, 0.333, 1, 0.667, 0.969]}, {"time": 1, "y": 0.969, "curve": [1.333, 1, 1.667, 1, 1.333, 0.969, 1.667, 1]}, {"time": 2, "curve": [2.333, 1, 2.667, 1, 2.333, 1, 2.667, 0.969]}, {"time": 3, "y": 0.969, "curve": [3.333, 1, 3.667, 1, 3.333, 0.969, 3.667, 1]}, {"time": 4}]}, "star": {"scale": [{}, {"time": 0.1667, "x": 1.219, "y": 1.219}, {"time": 0.3333}, {"time": 0.5, "x": 1.219, "y": 1.219}, {"time": 0.6667, "curve": "stepped"}, {"time": 1}, {"time": 1.1667, "x": 1.219, "y": 1.219}, {"time": 1.3333}, {"time": 1.5, "x": 1.219, "y": 1.219}, {"time": 1.6667, "curve": "stepped"}, {"time": 2}, {"time": 2.1667, "x": 1.219, "y": 1.219}, {"time": 2.3333}, {"time": 2.5, "x": 1.219, "y": 1.219}, {"time": 2.6667, "curve": "stepped"}, {"time": 3}, {"time": 3.1667, "x": 1.219, "y": 1.219}, {"time": 3.3333}, {"time": 3.5, "x": 1.219, "y": 1.219}, {"time": 3.6667}]}, "star2": {"scale": [{}, {"time": 0.1667, "x": 1.219, "y": 1.219}, {"time": 0.3333}, {"time": 0.5, "x": 1.219, "y": 1.219}, {"time": 0.6667, "curve": "stepped"}, {"time": 1}, {"time": 1.1667, "x": 1.219, "y": 1.219}, {"time": 1.3333}, {"time": 1.5, "x": 1.219, "y": 1.219}, {"time": 1.6667, "curve": "stepped"}, {"time": 2}, {"time": 2.1667, "x": 1.219, "y": 1.219}, {"time": 2.3333}, {"time": 2.5, "x": 1.219, "y": 1.219}, {"time": 2.6667, "curve": "stepped"}, {"time": 3}, {"time": 3.1667, "x": 1.219, "y": 1.219}, {"time": 3.3333}, {"time": 3.5, "x": 1.219, "y": 1.219}, {"time": 3.6667}]}, "W": {"translate": [{"curve": [0.333, 0, 0.667, 0, 0.333, 0, 0.667, -9.6]}, {"time": 1, "y": -9.6, "curve": [1.333, 0, 1.667, 0, 1.333, -9.6, 1.667, 0]}, {"time": 2, "curve": [2.333, 0, 2.667, 0, 2.333, 0, 2.667, -9.6]}, {"time": 3, "y": -9.6, "curve": [3.333, 0, 3.667, 0, 3.333, -9.6, 3.667, 0]}, {"time": 4}]}, "W-f2": {"rotate": [{"value": -0.5, "curve": [0.058, -0.22, 0.112, 0]}, {"time": 0.1667, "curve": [0.5, 0, 0.833, -6.4]}, {"time": 1.1667, "value": -6.4, "curve": [1.446, -6.4, 1.722, -1.88]}, {"time": 2, "value": -0.5, "curve": [2.058, -0.22, 2.112, 0]}, {"time": 2.1667, "curve": [2.5, 0, 2.833, -6.4]}, {"time": 3.1667, "value": -6.4, "curve": [3.446, -6.4, 3.724, -1.97]}, {"time": 4, "value": -0.5}]}, "W-f3": {"rotate": [{"value": -1.67, "curve": [0.113, -0.74, 0.223, 0]}, {"time": 0.3333, "curve": [0.667, 0, 1, -6.4]}, {"time": 1.3333, "value": -6.4, "curve": [1.557, -6.4, 1.778, -3.51]}, {"time": 2, "value": -1.67, "curve": [2.113, -0.74, 2.223, 0]}, {"time": 2.3333, "curve": [2.667, 0, 3, -6.4]}, {"time": 3.3333, "value": -6.4, "curve": [3.557, -6.4, 3.78, -3.57]}, {"time": 4, "value": -1.67}]}, "W-f4": {"rotate": [{"value": 0.51, "curve": [0.058, 0.22, 0.112, 0]}, {"time": 0.1667, "curve": [0.5, 0, 0.833, 6.46]}, {"time": 1.1667, "value": 6.46, "curve": [1.446, 6.46, 1.722, 1.89]}, {"time": 2, "value": 0.51, "curve": [2.058, 0.22, 2.112, 0]}, {"time": 2.1667, "curve": [2.5, 0, 2.833, 6.46]}, {"time": 3.1667, "value": 6.46, "curve": [3.446, 6.46, 3.724, 1.99]}, {"time": 4, "value": 0.51}]}, "W-f5": {"rotate": [{"value": 1.69, "curve": [0.113, 0.75, 0.223, 0]}, {"time": 0.3333, "curve": [0.667, 0, 1, 6.46]}, {"time": 1.3333, "value": 6.46, "curve": [1.557, 6.46, 1.778, 3.54]}, {"time": 2, "value": 1.69, "curve": [2.113, 0.75, 2.223, 0]}, {"time": 2.3333, "curve": [2.667, 0, 3, 6.46]}, {"time": 3.3333, "value": 6.46, "curve": [3.557, 6.46, 3.78, 3.6]}, {"time": 4, "value": 1.69}]}, "W-a": {"rotate": [{"value": 0.09, "curve": [0.058, 0.04, 0.112, 0]}, {"time": 0.1667, "curve": [0.5, 0, 0.833, 1.2]}, {"time": 1.1667, "value": 1.2, "curve": [1.446, 1.2, 1.722, 0.35]}, {"time": 2, "value": 0.09, "curve": [2.058, 0.04, 2.112, 0]}, {"time": 2.1667, "curve": [2.5, 0, 2.833, 1.2]}, {"time": 3.1667, "value": 1.2, "curve": [3.446, 1.2, 3.724, 0.37]}, {"time": 4, "value": 0.09}]}, "coin01": {"translate": [{"y": -4.8, "curve": [0.169, 0, 0.334, 0, 0.169, -2.42, 0.334, 0]}, {"time": 0.5, "curve": [0.833, 0, 1.167, 0, 0.833, 0, 1.167, -9.6]}, {"time": 1.5, "y": -9.6, "curve": [1.668, 0, 1.833, 0, 1.668, -9.6, 1.833, -7.15]}, {"time": 2, "y": -4.8, "curve": [2.169, 0, 2.334, 0, 2.169, -2.42, 2.334, 0]}, {"time": 2.5, "curve": [2.833, 0, 3.167, 0, 2.833, 0, 3.167, -9.6]}, {"time": 3.5, "y": -9.6, "curve": [3.668, 0, 3.836, 0, 3.668, -9.6, 3.836, -7.22]}, {"time": 4, "y": -4.8}]}, "coin02": {"translate": [{"y": -6.73, "curve": [0.113, 0, 0.223, 0, 0.113, -8.05, 0.223, -9.11]}, {"time": 0.3333, "y": -9.11, "curve": [0.667, 0, 1, 0, 0.667, -9.11, 1, 0]}, {"time": 1.3333, "curve": [1.557, 0, 1.778, 0, 1.557, 0, 1.778, -4.12]}, {"time": 2, "y": -6.73, "curve": [2.113, 0, 2.223, 0, 2.113, -8.05, 2.223, -9.11]}, {"time": 2.3333, "y": -9.11, "curve": [2.667, 0, 3, 0, 2.667, -9.11, 3, 0]}, {"time": 3.3333, "curve": [3.557, 0, 3.78, 0, 3.557, 0, 3.78, -4.03]}, {"time": 4, "y": -6.73}]}, "snow-1": {"translate": [{"curve": [0.669, 0, 1.337, -39.39, 0.669, 0, 1.337, -59.54]}, {"time": 2, "x": -88.71, "y": -118.45, "curve": [2.648, -136.09, 3.291, -193.48, 2.648, -175.04, 3.291, -231.1]}, {"time": 3.9333, "x": -193.48, "y": -231.1}, {"time": 4}]}, "snow-2": {"translate": [{"curve": [0.669, 0, 1.337, -39.39, 0.669, 0, 1.337, -59.54]}, {"time": 2, "x": -88.71, "y": -118.45, "curve": [2.335, -113.19, 3.623, -193.48, 2.335, -147.69, 3.623, -231.1]}, {"time": 3.9333, "x": -193.48, "y": -231.1}, {"time": 4}]}, "snow-3": {"translate": [{"x": -88.71, "y": -118.45, "curve": [0.648, -136.09, 1.291, -193.48, 0.648, -175.04, 1.291, -231.1]}, {"time": 1.9333, "x": -193.48, "y": -231.1}, {"time": 2, "curve": [2.669, 0, 3.337, -39.39, 2.669, 0, 3.337, -59.54]}, {"time": 4, "x": -88.71, "y": -118.45}]}, "snow-4": {"translate": [{"x": -88.71, "y": -118.45, "curve": [0.648, -136.09, 1.291, -193.48, 0.648, -175.04, 1.291, -231.1]}, {"time": 1.9333, "x": -193.48, "y": -231.1}, {"time": 2, "curve": [2.334, 0, 2.669, -9.75, 2.334, 0, 2.669, -14.75]}, {"time": 3, "x": -25.76, "y": -36.99, "curve": [3.336, -41.78, 3.669, -64.05, 3.336, -59.24, 3.669, -88.99]}, {"time": 4, "x": -88.71, "y": -118.45}]}, "snow-5": {"translate": [{"curve": [0.669, 0, 1.337, -39.39, 0.669, 0, 1.337, -59.54]}, {"time": 2, "x": -88.71, "y": -118.45, "curve": [2.648, -136.09, 3.291, -193.48, 2.648, -175.04, 3.291, -231.1]}, {"time": 3.9333, "x": -193.48, "y": -231.1}, {"time": 4}]}, "snow-6": {"translate": [{"curve": [0.172, 0, 1.098, -51.22, 0.172, 0, 1.098, -74.39]}, {"time": 2, "x": -120.09, "y": -148.66, "curve": [2.9, -187.14, 3.777, -271.9, 2.9, -220.98, 3.777, -293.24]}, {"time": 3.9333, "x": -271.9, "y": -293.24}, {"time": 4}], "scale": [{"x": 0.848, "y": 0.848, "curve": [0.222, 0.848, 0.444, 1, 0.222, 0.848, 0.444, 1]}, {"time": 0.6667, "curve": "stepped"}, {"time": 2.6667, "curve": [3.089, 1, 3.511, 0.696, 3.089, 1, 3.511, 0.696]}, {"time": 3.9333, "x": 0.544, "y": 0.544}, {"time": 4, "x": 0.848, "y": 0.848}]}, "snow-7": {"translate": [{"x": -225.26, "y": -250.83, "curve": [0.333, -251.71, 0.582, -271.9, 0.333, -275.2, 0.582, -293.24]}, {"time": 0.6, "x": -271.9, "y": -293.24}, {"time": 0.6667, "curve": [0.838, 0, 1.764, -51.22, 0.838, 0, 1.764, -74.39]}, {"time": 2.6667, "x": -120.09, "y": -148.66, "curve": [3.036, -147.62, 3.585, -191.61, 3.036, -178.36, 3.585, -219.82]}, {"time": 4, "x": -225.26, "y": -250.83}], "scale": [{"x": 0.808, "y": 0.808, "curve": [0.287, 0.691, 0.552, 0.561, 0.287, 0.691, 0.552, 0.561]}, {"time": 0.6, "x": 0.544, "y": 0.544}, {"time": 0.6667, "x": 0.848, "y": 0.848, "curve": [0.889, 0.848, 1.111, 1, 0.889, 0.848, 1.111, 1]}, {"time": 1.3333, "curve": "stepped"}, {"time": 3.3333, "curve": [3.488, 1, 3.752, 0.908, 3.488, 1, 3.752, 0.908]}, {"time": 4, "x": 0.808, "y": 0.808}]}, "snow-8": {"translate": [{"x": -167.71, "y": -193.9, "curve": [0.9, -234.76, 1.777, -319.52, 0.9, -266.22, 1.777, -338.48]}, {"time": 1.9333, "x": -319.52, "y": -338.48}, {"time": 2, "x": -47.62, "y": -45.24, "curve": [2.172, -47.62, 3.098, -98.84, 2.172, -45.24, 3.098, -119.63]}, {"time": 4, "x": -167.71, "y": -193.9}], "scale": [{"curve": "stepped"}, {"time": 0.6667, "curve": [1.089, 1, 1.511, 0.696, 1.089, 1, 1.511, 0.696]}, {"time": 1.9333, "x": 0.544, "y": 0.544}, {"time": 2, "x": 0.848, "y": 0.848, "curve": [2.222, 0.848, 2.444, 1, 2.222, 0.848, 2.444, 1]}, {"time": 2.6667, "curve": "stepped"}, {"time": 4}]}, "snow-9": {"translate": [{"x": -200.45, "y": -228.86, "curve": [0.474, -240.27, 0.838, -271.9, 0.474, -266.25, 0.838, -293.24]}, {"time": 0.9333, "x": -271.9, "y": -293.24}, {"time": 1, "curve": [1.172, 0, 2.098, -51.22, 1.172, 0, 2.098, -74.39]}, {"time": 3, "x": -120.09, "y": -148.66, "curve": [3.351, -146.24, 3.699, -175, 3.351, -176.87, 3.699, -204.96]}, {"time": 4, "x": -200.45, "y": -228.86}], "scale": [{"x": 0.944, "y": 0.944, "curve": [0.311, 0.849, 0.622, 0.656, 0.311, 0.849, 0.622, 0.656]}, {"time": 0.9333, "x": 0.544, "y": 0.544}, {"time": 1, "x": 0.848, "y": 0.848, "curve": [1.222, 0.848, 1.444, 1, 1.222, 0.848, 1.444, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 3.6667, "curve": [3.778, 1, 3.889, 0.978, 3.778, 1, 3.889, 0.978]}, {"time": 4, "x": 0.944, "y": 0.944}]}, "snow-10": {"translate": [{"x": -50.89, "y": -67.76, "curve": [0.311, -70.12, 0.657, -93.92, 0.311, -91.93, 0.657, -120.44]}, {"time": 1, "x": -120.09, "y": -148.66, "curve": [1.9, -187.14, 2.777, -271.9, 1.9, -220.98, 2.777, -293.24]}, {"time": 2.9333, "x": -271.9, "y": -293.24}, {"time": 3, "curve": [3.106, 0, 3.503, -19.51, 3.106, 0, 3.503, -28.32]}, {"time": 4, "x": -50.89, "y": -67.76}], "scale": [{"curve": "stepped"}, {"time": 1.6667, "curve": [2.089, 1, 2.511, 0.696, 2.089, 1, 2.511, 0.696]}, {"time": 2.9333, "x": 0.544, "y": 0.544}, {"time": 3, "x": 0.848, "y": 0.848, "curve": [3.222, 0.848, 3.444, 1, 3.222, 0.848, 3.444, 1]}, {"time": 3.6667, "curve": "stepped"}, {"time": 4}]}, "snow-11": {"translate": [{"curve": [0.172, 0, 1.098, -51.22, 0.172, 0, 1.098, -74.39]}, {"time": 2, "x": -120.09, "y": -148.66, "curve": [2.9, -187.14, 3.777, -271.9, 2.9, -220.98, 3.777, -293.24]}, {"time": 3.9333, "x": -271.9, "y": -293.24}, {"time": 4}], "scale": [{"x": 0.848, "y": 0.848, "curve": [0.222, 0.848, 0.444, 1, 0.222, 0.848, 0.444, 1]}, {"time": 0.6667, "curve": "stepped"}, {"time": 2.6667, "curve": [3.089, 1, 3.511, 0.696, 3.089, 1, 3.511, 0.696]}, {"time": 3.9333, "x": 0.544, "y": 0.544}, {"time": 4, "x": 0.848, "y": 0.848}]}, "snow-12": {"translate": [{"x": -172.72, "y": -202.3, "curve": [0.631, -224.58, 1.15, -271.9, 0.631, -252.81, 1.15, -293.24]}, {"time": 1.2667, "x": -271.9, "y": -293.24}, {"time": 1.3333, "curve": [1.505, 0, 2.431, -51.22, 1.505, 0, 2.431, -74.39]}, {"time": 3.3333, "x": -120.09, "y": -148.66, "curve": [3.561, -137.07, 3.788, -155.13, 3.561, -166.98, 3.788, -185.16]}, {"time": 4, "x": -172.72, "y": -202.3}], "scale": [{"curve": [0.422, 1, 0.844, 0.696, 0.422, 1, 0.844, 0.696]}, {"time": 1.2667, "x": 0.544, "y": 0.544}, {"time": 1.3333, "x": 0.848, "y": 0.848, "curve": [1.556, 0.848, 1.778, 1, 1.556, 0.848, 1.778, 1]}, {"time": 2, "curve": "stepped"}, {"time": 4}]}, "snow-13": {"translate": [{"x": -72.53, "y": -94.32, "curve": [0.22, -86.87, 0.444, -103.1, 0.22, -111.54, 0.444, -130.34]}, {"time": 0.6667, "x": -120.09, "y": -148.66, "curve": [1.567, -187.14, 2.444, -271.9, 1.567, -220.98, 2.444, -293.24]}, {"time": 2.6, "x": -271.9, "y": -293.24}, {"time": 2.6667, "curve": [2.796, 0, 3.353, -28.75, 2.796, 0, 3.353, -41.74]}, {"time": 4, "x": -72.53, "y": -94.32}], "scale": [{"curve": "stepped"}, {"time": 1.3333, "curve": [1.756, 1, 2.178, 0.696, 1.756, 1, 2.178, 0.696]}, {"time": 2.6, "x": 0.544, "y": 0.544}, {"time": 2.6667, "x": 0.848, "y": 0.848, "curve": [2.889, 0.848, 3.111, 1, 2.889, 0.848, 3.111, 1]}, {"time": 3.3333, "curve": [3.356, 1, 3.378, 0.787, 3.356, 1, 3.378, 0.787]}, {"time": 3.4, "x": 0.787, "y": 0.787, "curve": [3.603, 0.787, 3.804, 1, 3.603, 0.787, 3.804, 1]}, {"time": 4}]}, "snow-14": {"translate": [{"curve": [0.172, 0, 1.098, -51.22, 0.172, 0, 1.098, -74.39]}, {"time": 2, "x": -120.09, "y": -148.66, "curve": [2.9, -187.14, 3.777, -271.9, 2.9, -220.98, 3.777, -293.24]}, {"time": 3.9333, "x": -271.9, "y": -293.24}, {"time": 4}], "scale": [{"x": 0.848, "y": 0.848, "curve": [0.222, 0.848, 0.444, 1, 0.222, 0.848, 0.444, 1]}, {"time": 0.6667, "curve": "stepped"}, {"time": 2.6667, "curve": [3.089, 1, 3.511, 0.696, 3.089, 1, 3.511, 0.696]}, {"time": 3.9333, "x": 0.544, "y": 0.544}, {"time": 4, "x": 0.848, "y": 0.848}]}, "snow-15": {"translate": [{"curve": [0.669, 0, 1.337, -39.39, 0.669, 0, 1.337, -59.54]}, {"time": 2, "x": -88.71, "y": -118.45, "curve": [2.648, -136.09, 3.291, -193.48, 2.648, -175.04, 3.291, -231.1]}, {"time": 3.9333, "x": -193.48, "y": -231.1}, {"time": 4}]}, "snow-16": {"translate": [{"x": -178.73, "y": -216.37, "curve": [0.202, -187.75, 0.401, -193.48, 0.202, -225.49, 0.401, -231.1]}, {"time": 0.6, "x": -193.48, "y": -231.1}, {"time": 0.6667, "curve": [1.335, 0, 2.004, -39.39, 1.335, 0, 2.004, -59.54]}, {"time": 2.6667, "x": -88.71, "y": -118.45, "curve": [3.114, -121.41, 3.559, -158.65, 3.114, -157.5, 3.559, -196.07]}, {"time": 4, "x": -178.73, "y": -216.37}]}, "snow-17": {"translate": [{"x": -160.69, "y": -197.89, "curve": [0.312, -179.9, 0.623, -193.48, 0.312, -217.81, 0.623, -231.1]}, {"time": 0.9333, "x": -193.48, "y": -231.1}, {"time": 1, "curve": [1.669, 0, 2.337, -39.39, 1.669, 0, 2.337, -59.54]}, {"time": 3, "x": -88.71, "y": -118.45, "curve": [3.335, -113.19, 3.668, -140.17, 3.335, -147.69, 3.668, -176.59]}, {"time": 4, "x": -160.69, "y": -197.89}]}, "snow-18": {"translate": [{"curve": [0.669, 0, 1.337, -39.39, 0.669, 0, 1.337, -59.54]}, {"time": 2, "x": -88.71, "y": -118.45, "curve": [2.648, -136.09, 3.291, -193.48, 2.648, -175.04, 3.291, -231.1]}, {"time": 3.9333, "x": -193.48, "y": -231.1}, {"time": 4}]}, "snow-19": {"translate": [{"x": -138.27, "y": -174.21, "curve": [0.427, -168.47, 0.847, -193.48, 0.427, -206.58, 0.847, -231.1]}, {"time": 1.2667, "x": -193.48, "y": -231.1}, {"time": 1.3333, "curve": [2.002, 0, 2.671, -39.39, 2.002, 0, 2.671, -59.54]}, {"time": 3.3333, "x": -88.71, "y": -118.45, "curve": [3.558, -105.14, 3.782, -122.25, 3.558, -138.07, 3.782, -157.03]}, {"time": 4, "x": -138.27, "y": -174.21}]}, "snow-20": {"translate": [{"x": -65.01, "y": -89.15, "curve": [0.112, -72.54, 0.223, -80.49, 0.112, -98.72, 0.223, -108.63]}, {"time": 0.3333, "x": -88.71, "y": -118.45, "curve": [0.982, -136.09, 1.624, -193.48, 0.982, -175.04, 1.624, -231.1]}, {"time": 2.2667, "x": -193.48, "y": -231.1}, {"time": 2.3333, "curve": [2.891, 0, 3.448, -27.35, 2.891, 0, 3.448, -41.28]}, {"time": 4, "x": -65.01, "y": -89.15}]}, "snow-21": {"translate": [{"x": -12.17, "y": -17.79, "curve": [0.447, -27.5, 0.892, -55.83, 0.447, -39.79, 0.892, -79.18]}, {"time": 1.3333, "x": -88.71, "y": -118.45, "curve": [1.982, -136.09, 2.624, -193.48, 1.982, -175.04, 2.624, -231.1]}, {"time": 3.2667, "x": -193.48, "y": -231.1}, {"time": 3.3333, "curve": [3.556, 0, 3.779, -4.51, 3.556, 0, 3.779, -6.78]}, {"time": 4, "x": -12.17, "y": -17.79}]}, "snow-22": {"translate": [{"x": -117.7, "y": -140.59, "curve": [0.691, -158.09, 1.326, -193.48, 0.691, -188.83, 1.326, -231.1]}, {"time": 1.6, "x": -193.48, "y": -231.1}, {"time": 1.6667, "curve": [2.05, 0, 3.076, -63.43, 2.05, 0, 3.076, -75.77]}, {"time": 4, "x": -117.7, "y": -140.59}]}, "snow-24": {"translate": [{"x": -88.71, "y": -118.45, "curve": [0.648, -136.09, 1.291, -193.48, 0.648, -175.04, 1.291, -231.1]}, {"time": 1.9333, "x": -193.48, "y": -231.1}, {"time": 2, "curve": [2.669, 0, 3.337, -39.39, 2.669, 0, 3.337, -59.54]}, {"time": 4, "x": -88.71, "y": -118.45}]}, "tx_Point_070": {"rotate": [{"curve": [0.656, 0, 1.967, -82.3]}, {"time": 1.9667, "value": -105.53, "curve": [1.967, -108.66, 1.989, 0]}, {"time": 2, "curve": [2.656, 0, 3.967, -82.3]}, {"time": 3.9667, "value": -105.53, "curve": [3.967, -108.66, 3.989, 0]}, {"time": 4}], "scale": [{"x": 0, "y": 0, "curve": [0.178, 0, 0.356, 1.93, 0.178, 0, 0.356, 1.93]}, {"time": 0.5333, "x": 1.93, "y": 1.93, "curve": [1.022, 1.93, 1.511, 0, 1.022, 1.93, 1.511, 0]}, {"time": 2, "x": 0, "y": 0, "curve": [2.178, 0, 2.356, 1.93, 2.178, 0, 2.356, 1.93]}, {"time": 2.5333, "x": 1.93, "y": 1.93, "curve": [3.022, 1.93, 3.511, 0.53, 3.022, 1.93, 3.511, 0.53]}, {"time": 4, "x": 0, "y": 0}]}, "tx_Point_071": {"rotate": [{"value": -50.42, "curve": [0.36, -72.2, 0.633, -94.92]}, {"time": 0.6333, "value": -105.53, "curve": [0.633, -108.66, 0.656, 0]}, {"time": 0.6667, "curve": [1.322, 0, 2.633, -82.3]}, {"time": 2.6333, "value": -105.53, "curve": [2.633, -108.66, 2.656, 0]}, {"time": 2.6667, "curve": [3.023, 0, 3.573, -24.5]}, {"time": 4, "value": -50.42}], "scale": [{"x": 1.048, "y": 1.048, "curve": [0.223, 0.661, 0.445, 0.24, 0.223, 0.661, 0.445, 0.24]}, {"time": 0.6667, "x": 0, "y": 0, "curve": [0.844, 0, 1.022, 1.93, 0.844, 0, 1.022, 1.93]}, {"time": 1.2, "x": 1.93, "y": 1.93, "curve": [1.689, 1.93, 2.178, 0, 1.689, 1.93, 2.178, 0]}, {"time": 2.6667, "x": 0, "y": 0, "curve": [2.844, 0, 3.022, 1.93, 2.844, 0, 3.022, 1.93]}, {"time": 3.2, "x": 1.93, "y": 1.93, "curve": [3.467, 1.93, 3.735, 1.514, 3.467, 1.93, 3.735, 1.514]}, {"time": 4, "x": 1.048, "y": 1.048}]}, "tx_Point_072": {"rotate": [{"curve": [0.656, 0, 1.967, -82.3]}, {"time": 1.9667, "value": -105.53, "curve": [1.967, -108.66, 1.989, 0]}, {"time": 2, "curve": [2.656, 0, 3.967, -82.3]}, {"time": 3.9667, "value": -105.53, "curve": [3.967, -108.66, 3.989, 0]}, {"time": 4}], "scale": [{"x": 0, "y": 0, "curve": [0.178, 0, 0.356, 1.93, 0.178, 0, 0.356, 1.93]}, {"time": 0.5333, "x": 1.93, "y": 1.93, "curve": [1.022, 1.93, 1.511, 0.53, 1.022, 1.93, 1.511, 0.53]}, {"time": 2, "x": 0, "y": 0, "curve": [2.178, 0, 2.356, 1.93, 2.178, 0, 2.356, 1.93]}, {"time": 2.5333, "x": 1.93, "y": 1.93, "curve": [3.022, 1.93, 3.511, 0, 3.022, 1.93, 3.511, 0]}, {"time": 4, "x": 0, "y": 0}]}, "tx_Point_073": {"rotate": [{"value": -16.63, "curve": [0.627, -41.92, 1.3, -88.88]}, {"time": 1.3, "value": -105.53, "curve": [1.3, -108.66, 1.322, 0]}, {"time": 1.3333, "curve": [1.989, 0, 3.3, -82.3]}, {"time": 3.3, "value": -105.53, "curve": [3.3, -108.66, 3.322, 0]}, {"time": 3.3333, "curve": [3.519, 0, 3.757, -6.63]}, {"time": 4, "value": -16.63}], "scale": [{"x": 1.894, "y": 1.894, "curve": [0.447, 1.675, 0.89, 0.481, 0.447, 1.675, 0.89, 0.481]}, {"time": 1.3333, "x": 0, "y": 0, "curve": [1.511, 0, 1.689, 1.93, 1.511, 0, 1.689, 1.93]}, {"time": 1.8667, "x": 1.93, "y": 1.93, "curve": [2.356, 1.93, 2.844, 0, 2.356, 1.93, 2.844, 0]}, {"time": 3.3333, "x": 0, "y": 0, "curve": [3.511, 0, 3.689, 1.93, 3.511, 0, 3.689, 1.93]}, {"time": 3.8667, "x": 1.93, "y": 1.93, "curve": [3.912, 1.93, 3.958, 1.917, 3.912, 1.93, 3.958, 1.917]}, {"time": 4, "x": 1.894, "y": 1.894}]}, "tx_Point_074": {"rotate": [{"value": -16.63, "curve": [0.627, -41.92, 1.3, -88.88]}, {"time": 1.3, "value": -105.53, "curve": [1.3, -108.66, 1.322, 0]}, {"time": 1.3333, "curve": [1.989, 0, 3.3, -82.3]}, {"time": 3.3, "value": -105.53, "curve": [3.3, -108.66, 3.322, 0]}, {"time": 3.3333, "curve": [3.519, 0, 3.757, -6.63]}, {"time": 4, "value": -16.63}], "scale": [{"x": 1.881, "y": 1.881, "curve": [0.447, 1.585, 0.89, 0, 0.447, 1.585, 0.89, 0]}, {"time": 1.3333, "x": 0, "y": 0, "curve": [1.511, 0, 1.689, 1.93, 1.511, 0, 1.689, 1.93]}, {"time": 1.8667, "x": 1.93, "y": 1.93, "curve": [2.356, 1.93, 2.844, 0.53, 2.356, 1.93, 2.844, 0.53]}, {"time": 3.3333, "x": 0, "y": 0, "curve": [3.511, 0, 3.689, 1.93, 3.511, 0, 3.689, 1.93]}, {"time": 3.8667, "x": 1.93, "y": 1.93, "curve": [3.912, 1.93, 3.958, 1.912, 3.912, 1.93, 3.958, 1.912]}, {"time": 4, "x": 1.881, "y": 1.881}]}, "tx_Point_075": {"rotate": [{"curve": [0.656, 0, 1.967, -82.3]}, {"time": 1.9667, "value": -105.53, "curve": [1.967, -108.66, 1.989, 0]}, {"time": 2, "curve": [2.656, 0, 3.967, -82.3]}, {"time": 3.9667, "value": -105.53, "curve": [3.967, -108.66, 3.989, 0]}, {"time": 4}], "scale": [{"x": 0, "y": 0, "curve": [0.178, 0, 0.356, 1.93, 0.178, 0, 0.356, 1.93]}, {"time": 0.5333, "x": 1.93, "y": 1.93, "curve": [1.022, 1.93, 1.511, 0, 1.022, 1.93, 1.511, 0]}, {"time": 2, "x": 0, "y": 0, "curve": [2.178, 0, 2.356, 1.93, 2.178, 0, 2.356, 1.93]}, {"time": 2.5333, "x": 1.93, "y": 1.93, "curve": [3.022, 1.93, 3.511, 0.53, 3.022, 1.93, 3.511, 0.53]}, {"time": 4, "x": 0, "y": 0}]}, "tx_Point_076": {"rotate": [{"curve": [0.656, 0, 1.967, -82.3]}, {"time": 1.9667, "value": -105.53, "curve": [1.967, -108.66, 1.989, 0]}, {"time": 2, "curve": [2.656, 0, 3.967, -82.3]}, {"time": 3.9667, "value": -105.53, "curve": [3.967, -108.66, 3.989, 0]}, {"time": 4}], "scale": [{"x": 0, "y": 0, "curve": [0.178, 0, 0.356, 1.93, 0.178, 0, 0.356, 1.93]}, {"time": 0.5333, "x": 1.93, "y": 1.93, "curve": [1.022, 1.93, 1.511, 0, 1.022, 1.93, 1.511, 0]}, {"time": 2, "x": 0, "y": 0, "curve": [2.178, 0, 2.356, 1.93, 2.178, 0, 2.356, 1.93]}, {"time": 2.5333, "x": 1.93, "y": 1.93, "curve": [3.022, 1.93, 3.511, 0.53, 3.022, 1.93, 3.511, 0.53]}, {"time": 4, "x": 0, "y": 0}]}, "tx_Point_077": {"scale": [{"x": 0, "y": 0, "curve": [0.178, 0, 0.356, 1.249, 0.178, 0, 0.356, 1.249]}, {"time": 0.5333, "x": 1.249, "y": 1.249, "curve": [1.022, 1.249, 1.511, 0, 1.022, 1.249, 1.511, 0]}, {"time": 2, "x": 0, "y": 0, "curve": [2.178, 0, 2.356, 1.249, 2.178, 0, 2.356, 1.249]}, {"time": 2.5333, "x": 1.249, "y": 1.249, "curve": [3.022, 1.249, 3.511, 0.53, 3.022, 1.249, 3.511, 0.53]}, {"time": 4, "x": 0, "y": 0}]}, "tx_Point_078": {"scale": [{"x": 0.852, "y": 0.852, "curve": [0.067, 1.07, 0.134, 1.249, 0.067, 1.07, 0.134, 1.249]}, {"time": 0.2, "x": 1.249, "y": 1.249, "curve": [0.257, 1.249, 0.314, 1.162, 0.257, 1.249, 0.314, 1.162]}, {"time": 0.3667, "x": 1.143, "y": 1.143, "curve": [0.803, 1.006, 1.235, 0.468, 0.803, 1.006, 1.235, 0.468]}, {"time": 1.6667, "x": 0, "y": 0, "curve": [1.844, 0, 2.022, 1.249, 1.844, 0, 2.022, 1.249]}, {"time": 2.2, "x": 1.249, "y": 1.249, "curve": [2.689, 1.249, 3.178, 0, 2.689, 1.249, 3.178, 0]}, {"time": 3.6667, "x": 0, "y": 0, "curve": [3.778, 0, 3.889, 0.486, 3.778, 0, 3.889, 0.486]}, {"time": 4, "x": 0.852, "y": 0.852}]}, "tx_Point_079": {"scale": [{"x": 1.217, "y": 1.217, "curve": [0.447, 1.026, 0.89, 0, 0.447, 1.026, 0.89, 0]}, {"time": 1.3333, "x": 0, "y": 0, "curve": [1.511, 0, 1.689, 1.249, 1.511, 0, 1.689, 1.249]}, {"time": 1.8667, "x": 1.249, "y": 1.249, "curve": [2.356, 1.249, 2.844, 0.53, 2.356, 1.249, 2.844, 0.53]}, {"time": 3.3333, "x": 0, "y": 0, "curve": [3.511, 0, 3.689, 1.249, 3.511, 0, 3.689, 1.249]}, {"time": 3.8667, "x": 1.249, "y": 1.249, "curve": [3.912, 1.249, 3.958, 1.237, 3.912, 1.249, 3.958, 1.237]}, {"time": 4, "x": 1.217, "y": 1.217}]}, "tx_Point_080": {"scale": [{"x": 1.058, "y": 1.058, "curve": [0.335, 0.809, 0.668, 0.361, 0.335, 0.809, 0.668, 0.361]}, {"time": 1, "x": 0, "y": 0, "curve": [1.178, 0, 1.356, 1.249, 1.178, 0, 1.356, 1.249]}, {"time": 1.5333, "x": 1.249, "y": 1.249, "curve": [2.022, 1.249, 2.511, 0, 2.022, 1.249, 2.511, 0]}, {"time": 3, "x": 0, "y": 0, "curve": [3.178, 0, 3.356, 1.249, 3.178, 0, 3.356, 1.249]}, {"time": 3.5333, "x": 1.249, "y": 1.249, "curve": [3.69, 1.249, 3.846, 1.176, 3.69, 1.249, 3.846, 1.176]}, {"time": 4, "x": 1.058, "y": 1.058}]}, "tx_Point_081": {"scale": [{"x": 0.339, "y": 0.339, "curve": [0.168, 0.149, 0.334, 0, 0.168, 0.149, 0.334, 0]}, {"time": 0.5, "x": 0, "y": 0, "curve": [0.678, 0, 0.856, 1.249, 0.678, 0, 0.856, 1.249]}, {"time": 1.0333, "x": 1.249, "y": 1.249, "curve": [1.522, 1.249, 2.011, 0.53, 1.522, 1.249, 2.011, 0.53]}, {"time": 2.5, "x": 0, "y": 0, "curve": [2.678, 0, 2.856, 1.249, 2.678, 0, 2.856, 1.249]}, {"time": 3.0333, "x": 1.249, "y": 1.249, "curve": [3.356, 1.249, 3.679, 0.709, 3.356, 1.249, 3.679, 0.709]}, {"time": 4, "x": 0.339, "y": 0.339}]}, "tx_Point_082": {"scale": [{"x": 0.852, "y": 0.852, "curve": [0.067, 1.07, 0.134, 1.249, 0.067, 1.07, 0.134, 1.249]}, {"time": 0.2, "x": 1.249, "y": 1.249, "curve": [0.689, 1.249, 1.178, 0.53, 0.689, 1.249, 1.178, 0.53]}, {"time": 1.6667, "x": 0, "y": 0, "curve": [1.844, 0, 2.022, 1.249, 1.844, 0, 2.022, 1.249]}, {"time": 2.2, "x": 1.249, "y": 1.249, "curve": [2.689, 1.249, 3.178, 0, 2.689, 1.249, 3.178, 0]}, {"time": 3.6667, "x": 0, "y": 0, "curve": [3.778, 0, 3.889, 0.486, 3.778, 0, 3.889, 0.486]}, {"time": 4, "x": 0.852, "y": 0.852}]}}}}}