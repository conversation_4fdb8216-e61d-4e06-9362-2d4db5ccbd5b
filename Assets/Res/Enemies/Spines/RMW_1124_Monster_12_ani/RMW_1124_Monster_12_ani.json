{"skeleton": {"hash": "qxVPTv8mqk0", "spine": "4.1.17", "x": -237.92, "y": -0.12, "width": 478.48, "height": 410.18, "images": "./images/", "audio": "F:/SPINE/RogueMatchWar/RMW_1124_Monster_12_ani"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "y": 3, "scaleX": 0.43, "scaleY": 0.43}, {"name": "miao", "parent": "bone", "x": -124.98, "y": 311.06}, {"name": "body", "parent": "miao", "x": 125.36, "y": -192.48}, {"name": "eyes", "parent": "body", "x": 0.59, "y": 54.62}, {"name": "body2", "parent": "body", "length": 51.5, "rotation": -76.36, "x": -46.16, "y": -53.45}, {"name": "body3", "parent": "body", "length": 47.99, "rotation": -105.87, "x": 50.05, "y": -48.59}, {"name": "hair", "parent": "body", "x": -3.4, "y": 180.27}, {"name": "hair2", "parent": "hair", "length": 49.03, "rotation": -103.76, "x": -35.47, "y": -43.25}, {"name": "hair3", "parent": "hair", "length": 44.28, "rotation": -86.86, "x": 0.49, "y": -46.16}, {"name": "hair4", "parent": "hair", "length": 54.23, "rotation": -77.58, "x": 46.65, "y": -44.7}, {"name": "hand", "parent": "body", "length": 100.85, "rotation": -94.14, "x": -81.15, "y": 121.96}, {"name": "hand2", "parent": "hand", "length": 74.16, "rotation": 16.63, "x": 106.56, "y": 1.88}, {"name": "fly", "parent": "miao", "length": 41.23, "rotation": -167.74, "x": -37.95, "y": -15.77}, {"name": "fly2", "parent": "fly", "length": 32.5, "rotation": 5.52, "x": 42.94, "y": -0.37}, {"name": "fly3", "parent": "miao", "length": 44.72, "rotation": 46.19, "x": 24.36, "y": 19.11}, {"name": "hand3", "parent": "body", "length": 105.99, "rotation": -87.41, "x": 89.53, "y": 134.32}, {"name": "hand4", "parent": "hand3", "length": 75.24, "rotation": -16.43, "x": 114.98, "y": -2.79}, {"name": "fly4", "parent": "fly3", "length": 34.96, "rotation": -7.43, "x": 48.36, "y": -0.79}, {"name": "miao2", "parent": "bone", "rotation": -64.77, "x": 126.77, "y": 312.75}, {"name": "fly5", "parent": "miao2", "length": 41.23, "rotation": -167.74, "x": -37.95, "y": -15.77}, {"name": "fly6", "parent": "fly5", "length": 32.5, "rotation": 5.52, "x": 42.94, "y": -0.37}, {"name": "fly7", "parent": "miao2", "length": 44.72, "rotation": 46.19, "x": 24.36, "y": 19.11}, {"name": "fly8", "parent": "fly7", "length": 34.96, "rotation": -7.43, "x": 48.36, "y": -0.79}], "slots": [{"name": "body", "bone": "body", "attachment": "body"}, {"name": "hand", "bone": "hand", "attachment": "hand"}, {"name": "hand2", "bone": "hand3", "attachment": "hand"}, {"name": "fly", "bone": "fly", "attachment": "fly"}, {"name": "fly3", "bone": "fly5", "attachment": "fly"}, {"name": "fly2", "bone": "fly3", "attachment": "fly"}, {"name": "fly4", "bone": "fly7", "attachment": "fly"}, {"name": "miao", "bone": "miao", "attachment": "miao"}, {"name": "miao2", "bone": "miao2", "attachment": "miao"}, {"name": "hair", "bone": "hair", "attachment": "hair"}, {"name": "eyes", "bone": "eyes", "attachment": "eyes"}], "skins": [{"name": "default", "attachments": {"body": {"body": {"type": "mesh", "uvs": [0.57376, 0, 0.75555, 0.06193, 0.82854, 0.11282, 0.87126, 0.17673, 0.87106, 0.32006, 0.8356, 0.3522, 0.98938, 0.5318, 0.98929, 0.72653, 0.95712, 0.8259, 0.84281, 0.95869, 0.77402, 0.99178, 0.58684, 0.99256, 0.50677, 0.89697, 0.51875, 0.7983, 0.50627, 0.7848, 0.48596, 0.79847, 0.48768, 0.89435, 0.44917, 0.95458, 0.3573, 0.99215, 0.2092, 0.99157, 0.14137, 0.94004, 0.0392, 0.82144, 0.01061, 0.72147, 0.01064, 0.53841, 0.11024, 0.41161, 0.15238, 0.37793, 0.11446, 0.20284, 0.17459, 0.10511, 0.25409, 0.05937, 0.3889, 0.01158, 0.49385, 0, 0.28923, 0.8589, 0.25432, 0.75471, 0.72917, 0.90022, 0.7315, 0.7583, 0.25897, 0.56248, 0.51502, 0.55709, 0.79668, 0.54092, 0.50338, 0.35049, 0.50338, 0.12952, 0.32415, 0.35409, 0.71288, 0.34331], "triangles": [37, 5, 6, 7, 37, 6, 34, 36, 37, 34, 37, 7, 14, 36, 34, 13, 14, 34, 8, 34, 7, 33, 13, 34, 33, 34, 8, 12, 13, 33, 9, 33, 8, 10, 33, 9, 11, 12, 33, 11, 33, 10, 23, 24, 35, 22, 23, 35, 32, 22, 35, 36, 32, 35, 14, 32, 36, 15, 32, 14, 21, 22, 32, 31, 32, 15, 21, 32, 31, 31, 15, 16, 20, 21, 31, 17, 31, 16, 19, 20, 31, 18, 19, 31, 17, 18, 31, 39, 30, 0, 39, 0, 1, 29, 30, 39, 3, 41, 2, 2, 41, 39, 2, 39, 1, 4, 41, 3, 38, 39, 41, 5, 41, 4, 28, 39, 27, 39, 28, 29, 40, 39, 38, 40, 27, 39, 26, 27, 40, 25, 26, 40, 37, 41, 5, 36, 38, 41, 36, 41, 37, 35, 25, 40, 24, 25, 35, 40, 38, 36, 35, 40, 36], "vertices": [2, 3, 13.49, 120.51, 1, 6, -152.66, -81.4, 0, 2, 3, 47.3, 105.58, 0.99112, 6, -147.55, -44.79, 0.00888, 2, 3, 60.88, 93.32, 0.97877, 6, -139.46, -28.38, 0.02123, 2, 3, 68.82, 77.91, 0.95996, 6, -126.82, -16.53, 0.04004, 2, 3, 68.79, 43.37, 0.86365, 6, -93.58, -7.12, 0.13635, 2, 3, 62.19, 35.63, 0.79479, 6, -84.33, -11.34, 0.20521, 2, 3, 90.79, -7.66, 0.24838, 6, -50.52, 28, 0.75162, 2, 3, 90.78, -54.59, 0.0073, 6, -5.37, 40.82, 0.9927, 1, 6, 19.3, 41.61, 1, 1, 6, 55.9, 29.9, 1, 1, 6, 67.07, 19.78, 1, 2, 5, 78.05, 44.94, 0.00372, 6, 76.77, -13.66, 0.99628, 3, 3, 1.03, -95.66, 0.00656, 5, 52.15, 35.9, 0.0494, 6, 58.68, -34.28, 0.94404, 3, 3, 3.26, -71.88, 0.06835, 5, 29.57, 43.68, 0.28005, 6, 35.2, -38.64, 0.6516, 3, 3, 0.94, -68.63, 0.08985, 5, 25.86, 42.19, 0.4393, 6, 32.7, -41.76, 0.47085, 3, 3, -2.84, -71.92, 0.0691, 5, 28.17, 37.74, 0.6503, 6, 36.9, -44.5, 0.2806, 3, 3, -2.52, -95.03, 0.00583, 5, 50.7, 32.6, 0.95042, 6, 59.04, -37.87, 0.04374, 3, 3, -9.68, -109.55, 0.00014, 5, 63.12, 22.22, 0.9881, 6, 74.96, -40.79, 0.01176, 2, 5, 67.89, 3.47, 0.9997, 6, 88.35, -54.76, 0.0003, 1, 5, 61.25, -23.26, 1, 1, 5, 46.21, -32.59, 1, 1, 5, 13.95, -44.32, 1, 2, 3, -91.26, -53.37, 0.02817, 5, -10.72, -43.8, 0.97183, 2, 3, -91.25, -9.25, 0.29256, 5, -53.59, -33.39, 0.70744, 2, 3, -72.73, 21.31, 0.6147, 5, -78.92, -8.18, 0.3853, 2, 3, -64.89, 29.43, 0.74401, 5, -84.96, 1.35, 0.25599, 2, 3, -71.94, 71.62, 0.95654, 5, -127.63, 4.45, 0.04346, 2, 3, -60.76, 95.18, 0.98459, 5, -147.88, 20.87, 0.01541, 2, 3, -45.97, 106.2, 0.99436, 5, -155.1, 37.84, 0.00564, 2, 3, -20.89, 117.72, 0.99993, 5, -160.38, 64.93, 7e-05, 1, 3, -1.37, 120.51, 1, 1, 5, 33.69, -1.25, 1, 1, 5, 7.76, -1.64, 1, 1, 6, 48.12, 5.72, 1, 3, 3, 42.83, -62.24, 0.00579, 5, 29.53, 84.41, 0.00512, 6, 15.1, -3.21, 0.98909, 3, 3, -45.06, -15.05, 0.44576, 5, -37.06, 10.13, 0.55314, 6, -6.26, -100.66, 0.00111, 3, 3, 2.56, -13.75, 0.80156, 5, -27.09, 56.71, 0.08314, 6, -20.53, -55.2, 0.11529, 2, 3, 54.95, -9.85, 0.38858, 6, -38.6, -5.87, 0.61142, 3, 3, 0.4, 36.04, 0.98532, 5, -75.98, 66.35, 0.00619, 6, -67.83, -70.9, 0.00849, 3, 3, 0.4, 89.29, 0.99944, 5, -127.73, 78.92, 0.00013, 6, -119.06, -85.45, 0.00044, 2, 3, -32.94, 35.17, 0.90165, 5, -83, 33.75, 0.09835, 2, 3, 39.36, 37.77, 0.87396, 6, -80.15, -33.89, 0.12604], "hull": 31, "edges": [0, 60, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 22, 24, 28, 30, 30, 32, 24, 26, 26, 28], "width": 186, "height": 241}}, "eyes": {"eyes": {"x": -0.82, "y": -0.61, "width": 66, "height": 37}}, "fly": {"fly": {"type": "mesh", "uvs": [0.17376, 0, 0.50577, 0.09274, 0.81141, 0.17811, 0.94497, 0.26617, 0.99038, 0.45752, 0.90045, 0.55791, 0.68819, 0.79486, 0.50443, 1, 0.41702, 1, 0.1965, 0.91429, 0.02124, 0.6797, 0.02141, 0.32359, 0.09943, 0.12353, 0.17014, 0, 0.79674, 0.37118, 0.37384, 0.4866], "triangles": [7, 8, 6, 8, 9, 6, 9, 15, 6, 9, 10, 15, 10, 11, 15, 11, 12, 15, 1, 15, 12, 12, 13, 0, 1, 12, 0, 5, 15, 14, 5, 6, 15, 5, 14, 4, 15, 1, 14, 14, 3, 4, 14, 2, 3, 14, 1, 2], "vertices": [2, 13, 52.95, -44.05, 0.00245, 14, 5.76, -44.44, 0.99755, 2, 13, 25.48, -28.21, 0.45273, 14, -20.06, -26.02, 0.54727, 2, 13, 0.19, -13.62, 0.98431, 14, -43.83, -9.08, 0.01569, 1, 13, -9.75, -2.09, 1, 2, 13, -9.56, 18.23, 0.99921, 14, -50.46, 23.57, 0.00079, 2, 13, 0.66, 26.7, 0.97546, 14, -39.48, 31.01, 0.02454, 2, 13, 24.77, 46.68, 0.6445, 14, -13.56, 48.58, 0.3555, 2, 13, 45.64, 63.97, 0.38014, 14, 8.88, 63.78, 0.61986, 2, 13, 53.41, 62.28, 0.35004, 14, 16.45, 61.36, 0.64996, 2, 13, 71.13, 49.31, 0.21531, 14, 32.84, 46.74, 0.78469, 2, 13, 81.53, 22.08, 0.03807, 14, 40.57, 18.64, 0.96193, 2, 13, 73.65, -14.11, 0, 14, 29.24, -16.63, 1, 1, 14, 16.13, -34.27, 1, 2, 13, 53.27, -44.12, 0.00245, 14, 6.08, -44.54, 0.99755, 2, 13, 5.76, 5.72, 0.99981, 14, -36.43, 9.64, 0.00019, 2, 13, 45.91, 9.27, 0.31344, 14, 3.88, 9.31, 0.68656], "hull": 14, "edges": [0, 26, 4, 6, 6, 8, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 2, 2, 4, 8, 10, 10, 12, 12, 14], "width": 91, "height": 104}}, "fly2": {"fly": {"type": "mesh", "uvs": [0.33403, 0.0447, 0.57417, 0.11204, 0.89265, 0.20135, 0.94129, 0.25103, 0.98875, 0.45948, 0.75903, 0.71573, 0.60645, 0.88593, 0.50419, 1, 0.39881, 1, 0.1784, 0.89711, 0.02153, 0.67152, 0.02186, 0.32161, 0.13536, 0.031, 0.20219, 0.00773, 0.62953, 0.34699, 0.22765, 0.3458], "triangles": [7, 8, 6, 8, 9, 6, 6, 9, 15, 9, 10, 15, 5, 6, 15, 10, 11, 15, 11, 12, 15, 5, 15, 14, 4, 14, 3, 3, 14, 2, 14, 4, 5, 15, 1, 14, 14, 1, 2, 15, 12, 0, 15, 0, 1, 0, 12, 13], "vertices": [2, 15, 44.51, 44.33, 0.69199, 18, -9.65, 44.24, 0.30801, 2, 15, 26.32, 30.33, 0.94771, 18, -25.88, 28.02, 0.05229, 1, 15, 2.2, 11.78, 1, 1, 15, -0.22, 5.42, 1, 2, 15, 3.06, -16.44, 0.99127, 18, -42.89, -21.37, 0.00873, 2, 15, 31.76, -34.44, 0.53223, 18, -12.11, -35.51, 0.46777, 2, 15, 50.82, -46.39, 0.11923, 18, 8.33, -44.89, 0.88077, 2, 15, 63.6, -54.4, 0.02864, 18, 22.04, -51.18, 0.97136, 2, 15, 72.62, -51.15, 0.01269, 18, 30.56, -46.8, 0.98731, 1, 18, 43.5, -28.1, 1, 1, 18, 45.46, -0.71, 1, 2, 15, 80.99, 26.85, 0.21572, 18, 28.78, 31.63, 0.78428, 2, 15, 61.03, 51.79, 0.57832, 18, 5.77, 53.78, 0.42168, 2, 15, 54.49, 52.01, 0.60136, 18, -0.75, 53.15, 0.39864, 1, 15, 29.86, 5.64, 1, 2, 15, 64.22, 18.14, 0.27311, 18, 13.28, 20.83, 0.72689], "hull": 14, "edges": [4, 6, 6, 8, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 8, 10, 4, 2, 2, 0, 0, 26, 10, 12, 12, 14], "width": 91, "height": 104}}, "fly3": {"fly": {"type": "mesh", "uvs": [0.17376, 0, 0.50577, 0.09274, 0.81141, 0.17811, 0.94497, 0.26617, 0.99038, 0.45752, 0.90045, 0.55791, 0.68819, 0.79486, 0.50443, 1, 0.41702, 1, 0.1965, 0.91429, 0.02124, 0.6797, 0.02141, 0.32359, 0.09943, 0.12353, 0.17014, 0, 0.79674, 0.37118, 0.37384, 0.4866], "triangles": [7, 8, 6, 8, 9, 6, 9, 15, 6, 9, 10, 15, 10, 11, 15, 11, 12, 15, 1, 15, 12, 12, 13, 0, 1, 12, 0, 5, 15, 14, 5, 6, 15, 5, 14, 4, 15, 1, 14, 14, 3, 4, 14, 2, 3, 14, 1, 2], "vertices": [2, 20, 52.95, -44.05, 0.00245, 21, 5.76, -44.44, 0.99755, 2, 20, 25.48, -28.21, 0.45273, 21, -20.06, -26.02, 0.54727, 2, 20, 0.19, -13.62, 0.98431, 21, -43.83, -9.08, 0.01569, 1, 20, -9.75, -2.09, 1, 2, 20, -9.56, 18.23, 0.99921, 21, -50.46, 23.57, 0.00079, 2, 20, 0.66, 26.7, 0.97546, 21, -39.48, 31.01, 0.02454, 2, 20, 24.77, 46.68, 0.6445, 21, -13.56, 48.58, 0.3555, 2, 20, 45.64, 63.97, 0.38014, 21, 8.88, 63.78, 0.61986, 2, 20, 53.41, 62.28, 0.35004, 21, 16.45, 61.36, 0.64996, 2, 20, 71.13, 49.31, 0.21531, 21, 32.84, 46.74, 0.78469, 2, 20, 81.53, 22.08, 0.03807, 21, 40.57, 18.64, 0.96193, 2, 20, 73.65, -14.11, 0, 21, 29.24, -16.63, 1, 1, 21, 16.13, -34.27, 1, 2, 20, 53.27, -44.12, 0.00245, 21, 6.08, -44.54, 0.99755, 2, 20, 5.76, 5.72, 0.99981, 21, -36.43, 9.64, 0.00019, 2, 20, 45.91, 9.27, 0.31344, 21, 3.88, 9.31, 0.68656], "hull": 14, "edges": [0, 26, 4, 6, 6, 8, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 2, 2, 4, 8, 10, 10, 12, 12, 14], "width": 91, "height": 104}}, "fly4": {"fly": {"type": "mesh", "uvs": [0.33403, 0.0447, 0.57417, 0.11204, 0.89265, 0.20135, 0.94129, 0.25103, 0.98875, 0.45948, 0.75903, 0.71573, 0.60645, 0.88593, 0.50419, 1, 0.39881, 1, 0.1784, 0.89711, 0.02153, 0.67152, 0.02186, 0.32161, 0.13536, 0.031, 0.20219, 0.00773, 0.62953, 0.34699, 0.22765, 0.3458], "triangles": [7, 8, 6, 8, 9, 6, 6, 9, 15, 9, 10, 15, 5, 6, 15, 10, 11, 15, 11, 12, 15, 5, 15, 14, 4, 14, 3, 3, 14, 2, 14, 4, 5, 15, 1, 14, 14, 1, 2, 15, 12, 0, 15, 0, 1, 0, 12, 13], "vertices": [2, 22, 44.51, 44.33, 0.69199, 23, -9.65, 44.24, 0.30801, 2, 22, 26.32, 30.33, 0.94771, 23, -25.88, 28.02, 0.05229, 1, 22, 2.2, 11.78, 1, 1, 22, -0.22, 5.42, 1, 2, 22, 3.06, -16.44, 0.99127, 23, -42.89, -21.37, 0.00873, 2, 22, 31.76, -34.44, 0.53223, 23, -12.11, -35.51, 0.46777, 2, 22, 50.82, -46.39, 0.11923, 23, 8.33, -44.89, 0.88077, 2, 22, 63.6, -54.4, 0.02864, 23, 22.04, -51.18, 0.97136, 2, 22, 72.62, -51.15, 0.01269, 23, 30.56, -46.8, 0.98731, 1, 23, 43.5, -28.1, 1, 1, 23, 45.46, -0.71, 1, 2, 22, 80.99, 26.85, 0.21572, 23, 28.78, 31.63, 0.78428, 2, 22, 61.03, 51.79, 0.57832, 23, 5.77, 53.78, 0.42168, 2, 22, 54.49, 52.01, 0.60136, 23, -0.75, 53.15, 0.39864, 1, 22, 29.86, 5.64, 1, 2, 22, 64.22, 18.14, 0.27311, 23, 13.28, 20.83, 0.72689], "hull": 14, "edges": [4, 6, 6, 8, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 8, 10, 4, 2, 2, 0, 0, 26, 10, 12, 12, 14], "width": 91, "height": 104}}, "hair": {"hair": {"type": "mesh", "uvs": [0.81056, 0.08285, 0.91981, 0.16477, 0.96739, 0.23184, 1, 0.70029, 0.97894, 0.76362, 0.79177, 1, 0.76123, 1, 0.67539, 0.84705, 0.53701, 1, 0.46221, 1, 0.32514, 0.84521, 0.24046, 1, 0.20824, 1, 0.00266, 0.73991, 0.00673, 0.59238, 0.03395, 0.21099, 0.14303, 0.11085, 0.37516, 0.00721, 0.60522, 0.00665, 0.68854, 0.65287, 0.68555, 0.28827, 0.34099, 0.6495, 0.34099, 0.32203, 0.1732, 0.60561, 0.19118, 0.27139, 0.83835, 0.25114, 0.86831, 0.68663], "triangles": [4, 5, 26, 26, 5, 7, 5, 6, 7, 7, 19, 26, 4, 26, 3, 26, 2, 3, 19, 25, 26, 26, 25, 2, 22, 20, 19, 19, 20, 25, 20, 0, 25, 25, 1, 2, 25, 0, 1, 7, 8, 10, 8, 9, 10, 10, 21, 7, 7, 21, 19, 11, 12, 10, 12, 13, 10, 10, 13, 23, 10, 23, 21, 13, 14, 23, 21, 22, 19, 21, 23, 22, 23, 24, 22, 23, 14, 24, 14, 15, 24, 15, 16, 24, 24, 16, 17, 20, 22, 18, 22, 17, 18, 22, 24, 17, 20, 18, 0], "vertices": [3, 7, 52.86, 9.47, 0.42764, 9, -52.67, 55.35, 0.00169, 10, -51.57, 17.72, 0.57067, 2, 7, 70.34, -2.16, 0.23228, 10, -36.45, 32.29, 0.76772, 2, 7, 77.95, -11.69, 0.1634, 10, -25.51, 37.67, 0.8366, 1, 10, 40.57, 28.46, 1, 1, 10, 48.63, 23.24, 1, 2, 9, 77.2, 45.2, 0.02, 10, 74.97, -13.23, 0.98, 2, 9, 76.93, 40.32, 0.02448, 10, 73.92, -18, 0.97552, 2, 9, 54.49, 27.8, 0.4396, 10, 49.76, -26.74, 0.5604, 3, 8, 64.7, 61.72, 0.002, 9, 74.96, 4.5, 0.97923, 10, 66.21, -53.03, 0.01878, 3, 8, 67.54, 50.1, 0.02004, 9, 74.31, -7.45, 0.97838, 10, 63.63, -64.72, 0.00158, 2, 8, 51.41, 23.57, 0.58356, 9, 51.16, -28.14, 0.41644, 2, 8, 75.98, 15.64, 0.98925, 9, 72.36, -42.87, 0.01075, 2, 8, 77.21, 10.63, 0.9927, 9, 72.08, -48.02, 0.0073, 1, 8, 49.16, -30.1, 1, 2, 7, -75.75, -62.88, 0.00051, 8, 28.66, -34.45, 0.99949, 2, 7, -71.4, -8.73, 0.19843, 8, -24.98, -43.11, 0.80157, 2, 7, -53.94, 5.49, 0.36579, 8, -42.95, -29.54, 0.63421, 2, 7, -16.8, 20.21, 0.87024, 8, -66.08, 3.04, 0.12976, 3, 7, 20.01, 20.29, 0.8452, 9, -65.28, 23.14, 0.00015, 10, -69.2, -12.04, 0.15465, 2, 9, 27.08, 31.41, 0.35174, 10, 23.28, -18.75, 0.64826, 3, 7, 32.86, -19.7, 0.33456, 9, -24.64, 33.78, 0.1464, 10, -27.38, -8.09, 0.51904, 2, 8, 23.81, 19.42, 0.56516, 9, 23.55, -24.08, 0.43484, 3, 7, -22.27, -24.5, 0.2998, 8, -21.35, 8.36, 0.49606, 9, -22.88, -21.53, 0.20413, 1, 8, 24.15, -8.13, 1, 3, 7, -46.24, -17.3, 0.24413, 8, -22.64, -16.63, 0.75005, 9, -31.38, -45.07, 0.00582, 3, 7, 57.31, -14.43, 0.22378, 9, -28.57, 58.48, 0.00607, 10, -27.28, 16.92, 0.77015, 1, 10, 34.15, 8.3, 1], "hull": 19, "edges": [0, 36, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36], "width": 160, "height": 142}}, "hand": {"hand": {"type": "mesh", "uvs": [0.76427, 0.05923, 0.82313, 0.20231, 0.84205, 0.44854, 1, 0.70625, 1, 0.74047, 0.94751, 0.88569, 0.83081, 0.93497, 0.58935, 0.98741, 0.3989, 1, 0.38243, 1, 0.11827, 0.89031, 0.0131, 0.71116, 0.02617, 0.4094, 0.24956, 0.25241, 0.30943, 0.1624, 0.24365, 0.00829, 0.31484, 0.0056, 0.54256, 0.77792, 0.49926, 0.61088, 0.44513, 0.4062, 0.54798, 0.17094], "triangles": [7, 8, 17, 10, 17, 8, 8, 9, 10, 7, 17, 6, 6, 17, 5, 17, 10, 11, 17, 11, 18, 5, 17, 4, 4, 17, 3, 17, 18, 3, 18, 2, 3, 18, 12, 19, 12, 18, 11, 18, 19, 2, 19, 1, 2, 12, 13, 19, 19, 13, 20, 19, 20, 1, 20, 13, 14, 20, 0, 1, 0, 20, 16, 20, 14, 16, 14, 15, 16], "vertices": [1, 11, -40.65, 19.39, 1, 1, 11, -4.03, 28.71, 1, 2, 11, 59.67, 35.47, 0.90225, 12, -35.31, 45.61, 0.09775, 2, 11, 125.21, 58.12, 0.01756, 12, 33.97, 48.55, 0.98244, 2, 11, 134.08, 58.76, 0.00415, 12, 42.66, 46.63, 0.99585, 1, 12, 78.24, 32.67, 1, 1, 12, 87.89, 17.03, 1, 1, 12, 95.31, -12.56, 1, 2, 11, 206.29, -4.11, 0, 12, 93.85, -34.28, 1, 2, 11, 206.43, -5.96, 0, 12, 93.45, -36.1, 1, 2, 11, 180.14, -37.8, 0.02575, 12, 59.15, -59.07, 0.97425, 2, 11, 134.54, -53.02, 0.33338, 12, 11.1, -60.61, 0.66662, 2, 11, 56.18, -57.21, 0.9995, 12, -65.18, -42.2, 0.0005, 1, 11, 13.65, -34.99, 1, 1, 11, -10.18, -29.93, 1, 1, 11, -49.61, -40.24, 1, 1, 11, -50.89, -32.27, 1, 2, 11, 147.53, 7.91, 0.00193, 12, 40.99, -5.95, 0.99807, 2, 11, 104.57, -0.11, 0.3387, 12, -2.47, -1.33, 0.6613, 1, 11, 51.93, -10.06, 1, 1, 11, -9.92, -2.89, 1], "hull": 17, "edges": [0, 32, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32], "width": 113, "height": 260}}, "hand2": {"hand": {"type": "mesh", "uvs": [0.74488, 0.05705, 0.82068, 0.16974, 0.83782, 0.39702, 0.81604, 0.40661, 1, 0.70612, 1, 0.73778, 0.94602, 0.89419, 0.75206, 0.96037, 0.46371, 1, 0.3594, 1, 0.27697, 0.94361, 0.11583, 0.8907, 0.01386, 0.68899, 0.02615, 0.41045, 0.26069, 0.24069, 0.30859, 0.14717, 0.2463, 0.01898, 0.28634, 0.00275, 0.57039, 0.8465, 0.51137, 0.68404, 0.48514, 0.41041, 0.50481, 0.13678], "triangles": [7, 8, 18, 10, 18, 8, 8, 9, 10, 7, 18, 6, 10, 11, 18, 6, 18, 5, 11, 19, 18, 11, 12, 19, 18, 19, 5, 19, 4, 5, 19, 3, 4, 12, 13, 19, 13, 20, 19, 19, 20, 3, 13, 14, 20, 20, 14, 21, 3, 20, 2, 21, 14, 15, 1, 20, 21, 20, 1, 2, 21, 0, 1, 15, 16, 21, 16, 17, 21, 21, 17, 0], "vertices": [1, 16, -22.85, -33.99, 1, 1, 16, 6.8, -41.23, 1, 2, 16, 65.93, -40.49, 0.94056, 17, -36.39, -50.04, 0.05944, 2, 16, 68.3, -37.92, 0.92001, 17, -34.84, -46.9, 0.07999, 2, 16, 147.04, -55.17, 0.0013, 17, 45.56, -41.18, 0.9987, 1, 17, 53.34, -38.49, 1, 1, 17, 89.8, -19.48, 1, 1, 17, 98.93, 6.85, 1, 1, 17, 98.05, 41.01, 1, 1, 17, 94.21, 52.16, 1, 2, 16, 205.03, 29.24, 0.00175, 17, 77.31, 56.19, 0.99825, 2, 16, 190.47, 46.81, 0.01868, 17, 58.38, 68.92, 0.98132, 2, 16, 137.55, 55.95, 0.32061, 17, 5.04, 62.72, 0.67939, 2, 16, 65.27, 51.29, 0.99686, 17, -62.98, 37.81, 0.00314, 1, 16, 22.38, 22.82, 1, 1, 16, -1.67, 16.32, 1, 1, 16, -35.29, 21.84, 1, 1, 16, -39.3, 17.13, 1, 1, 17, 64.25, 16.61, 1, 2, 16, 138.81, -0.27, 0.01615, 17, 22.14, 9.15, 0.98385, 2, 16, 67.6, -0.52, 0.99971, 17, -46.09, -11.22, 0.00029, 1, 16, -3.37, -5.96, 1], "hull": 18, "edges": [0, 34, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34], "width": 113, "height": 260}}, "miao": {"miao": {"x": -8.87, "y": 12.03, "width": 102, "height": 110}}, "miao2": {"miao": {"x": -8.87, "y": 12.03, "width": 102, "height": 110}}}}], "animations": {"animation": {"bones": {"hand": {"rotate": [{"value": -4.08, "curve": [0.023, -4.21, 0.045, -4.31]}, {"time": 0.0667, "value": -4.31, "curve": [0.233, -4.31, 0.4, 0.17]}, {"time": 0.5667, "value": 0.17, "curve": [0.712, 0.17, 0.857, -3.19]}, {"time": 1, "value": -4.08}]}, "hand2": {"rotate": [{"value": -0.32, "curve": [0.068, -1.54, 0.134, -2.59]}, {"time": 0.2, "value": -2.59, "curve": [0.367, -2.59, 0.533, 3.87]}, {"time": 0.7, "value": 3.87, "curve": [0.801, 3.87, 0.901, 1.55]}, {"time": 1, "value": -0.32}]}, "hand3": {"rotate": [{"value": 1.39, "curve": [0.034, 1.6, 0.067, 1.73]}, {"time": 0.1, "value": 1.73, "curve": [0.267, 1.73, 0.433, -1.55]}, {"time": 0.6, "value": -1.55, "curve": [0.734, -1.55, 0.868, 0.56]}, {"time": 1, "value": 1.39}]}, "hand4": {"rotate": [{"value": 0.52, "curve": [0.068, 1.44, 0.134, 2.23]}, {"time": 0.2, "value": 2.23, "curve": [0.367, 2.23, 0.533, -2.64]}, {"time": 0.7, "value": -2.64, "curve": [0.801, -2.64, 0.901, -0.89]}, {"time": 1, "value": 0.52}]}, "body": {"scale": [{"x": 1.004, "y": 0.992, "curve": [0.049, 1.002, 0.09, 1, 0.049, 0.996, 0.09, 1]}, {"time": 0.1, "curve": [0.178, 1, 0.256, 0.98, 0.178, 1, 0.256, 1.01]}, {"time": 0.3333, "x": 0.98, "y": 1.01, "curve": [0.422, 0.98, 0.511, 0.995, 0.422, 1.01, 0.511, 1.005]}, {"time": 0.6, "curve": [0.678, 1.005, 0.756, 1.01, 0.678, 0.995, 0.756, 0.98]}, {"time": 0.8333, "x": 1.01, "y": 0.98, "curve": [0.87, 1.01, 0.941, 1.007, 0.87, 0.98, 0.941, 0.986]}, {"time": 1, "x": 1.004, "y": 0.992}]}, "hair2": {"rotate": [{"value": -5.83}, {"time": 0.5, "value": -0.21}, {"time": 1, "value": -5.83}], "translate": [{}, {"time": 0.5, "y": -1.71}, {"time": 1}]}, "hair3": {"rotate": [{}, {"time": 0.5, "value": 1.16}, {"time": 1}], "translate": [{"y": -1.79}, {"time": 0.5, "y": -5.62}, {"time": 1, "y": -1.79}], "scale": [{"x": 0.975}, {"time": 0.5}, {"time": 1, "x": 0.975}]}, "hair4": {"rotate": [{"value": -2.15}, {"time": 0.5, "value": 2.64}, {"time": 1, "value": -2.15}]}, "fly3": {"rotate": [{"value": 10.93}, {"time": 0.2333, "value": -11.82}, {"time": 0.5, "value": 10.93}, {"time": 0.7333, "value": -11.82}, {"time": 1, "value": 10.93}]}, "fly4": {"rotate": [{"value": 10.93}, {"time": 0.2333, "value": -11.82}, {"time": 0.5, "value": 10.93}, {"time": 0.7333, "value": -11.82}, {"time": 1, "value": 10.93}]}, "fly": {"rotate": [{"value": -5.47}, {"time": 0.2333, "value": 12.34}, {"time": 0.5, "value": -5.47}, {"time": 0.7333, "value": 12.34}, {"time": 1, "value": -5.47}]}, "fly2": {"rotate": [{"value": -5.47}, {"time": 0.2333, "value": 12.34}, {"time": 0.5, "value": -5.47}, {"time": 0.7333, "value": 12.34}, {"time": 1, "value": -5.47}]}, "miao": {"translate": [{}, {"time": 0.2333, "x": 0.8, "y": 12.11}, {"time": 0.5}, {"time": 0.7333, "x": 0.8, "y": 12.11}, {"time": 1}]}, "eyes": {"scale": [{"time": 0.6}, {"time": 0.6667, "y": 0.09, "curve": "stepped"}, {"time": 0.7, "y": 0.09}, {"time": 0.7667}]}, "fly6": {"rotate": [{"value": -5.47}, {"time": 0.2333, "value": 12.34}, {"time": 0.5, "value": -5.47}, {"time": 0.7333, "value": 12.34}, {"time": 1, "value": -5.47}]}, "fly5": {"rotate": [{"value": -5.47}, {"time": 0.2333, "value": 12.34}, {"time": 0.5, "value": -5.47}, {"time": 0.7333, "value": 12.34}, {"time": 1, "value": -5.47}]}, "fly8": {"rotate": [{"value": 10.93}, {"time": 0.2333, "value": -11.82}, {"time": 0.5, "value": 10.93}, {"time": 0.7333, "value": -11.82}, {"time": 1, "value": 10.93}]}, "fly7": {"rotate": [{"value": 10.93}, {"time": 0.2333, "value": -11.82}, {"time": 0.5, "value": 10.93}, {"time": 0.7333, "value": -11.82}, {"time": 1, "value": 10.93}]}, "miao2": {"translate": [{}, {"time": 0.2333, "x": 0.8, "y": 12.11}, {"time": 0.5}, {"time": 0.7333, "x": 0.8, "y": 12.11}, {"time": 1}]}, "body2": {"scale": [{"curve": [0.089, 1, 0.178, 0.97, 0.089, 1, 0.178, 1]}, {"time": 0.2667, "x": 0.97, "curve": [0.344, 0.97, 0.422, 1, 0.344, 1, 0.422, 1]}, {"time": 0.5, "curve": [0.589, 1, 0.678, 0.97, 0.589, 1, 0.678, 1]}, {"time": 0.7667, "x": 0.97, "curve": [0.844, 0.97, 0.922, 1, 0.844, 1, 0.922, 1]}, {"time": 1}]}, "body3": {"scale": [{"curve": [0.089, 1, 0.178, 0.97, 0.089, 1, 0.178, 1]}, {"time": 0.2667, "x": 0.97, "curve": [0.344, 0.97, 0.422, 1, 0.344, 1, 0.422, 1]}, {"time": 0.5, "curve": [0.589, 1, 0.678, 0.97, 0.589, 1, 0.678, 1]}, {"time": 0.7667, "x": 0.97, "curve": [0.844, 0.97, 0.922, 1, 0.844, 1, 0.922, 1]}, {"time": 1}]}}, "attachments": {"default": {"hair": {"hair": {"deform": [{}, {"time": 0.5, "offset": 78, "vertices": [4.14473, -0.46013, 4.07736, -0.87463, 0, -3.12781, 3.10873, -0.34513, 3.05823, -0.65601, 4.55251, 1.13264, 4.66272, -0.51764, 0, 0.62561, -0.6071, -0.15109, -0.62172, 0.06903, 3.67911, 0.91534]}, {"time": 1}]}}}}}}}