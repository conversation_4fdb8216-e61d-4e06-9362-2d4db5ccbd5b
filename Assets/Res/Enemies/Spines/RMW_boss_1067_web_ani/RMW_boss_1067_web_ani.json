{"skeleton": {"hash": "y3X4Qkvt6HA", "spine": "4.1.11", "x": -177.5, "y": -34.67, "width": 373, "height": 292}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": -0.49, "y": 117.39}, {"name": "bone2", "parent": "bone", "length": 129.08, "rotation": -133.11, "x": -8.29, "y": 132.24}, {"name": "bone3", "parent": "bone2", "length": 89.72, "rotation": 18.26, "x": 129.08}, {"name": "bone4", "parent": "bone3", "length": 48.31, "rotation": 1.88, "x": 89.72}, {"name": "bone5", "parent": "bone4", "x": 65.74, "y": 3.29}, {"name": "bone6", "parent": "bone", "length": 82.26, "rotation": -107.6, "x": -15.83, "y": 124.71}, {"name": "bone7", "parent": "bone6", "length": 80.82, "rotation": 9.02, "x": 82.26}, {"name": "bone8", "parent": "bone7", "length": 69.51, "rotation": 4.85, "x": 80.82}, {"name": "bone9", "parent": "bone8", "x": 97.95, "y": 4.12}, {"name": "bone10", "parent": "bone", "length": 87.74, "rotation": -70.94, "x": 16.59, "y": 131.49}, {"name": "bone11", "parent": "bone10", "length": 83.75, "rotation": -5.52, "x": 87.74}, {"name": "bone12", "parent": "bone11", "length": 69.77, "rotation": -7.33, "x": 83.75}, {"name": "bone13", "parent": "bone12", "x": 94.01, "y": -7.19}, {"name": "bone14", "parent": "bone", "length": 110.35, "rotation": -45.28, "x": 15.83, "y": 126.21}, {"name": "bone15", "parent": "bone14", "length": 107.35, "rotation": -15.28, "x": 110.35}, {"name": "bone16", "parent": "bone15", "length": 44.86, "rotation": -4.6, "x": 107.35}, {"name": "bone17", "parent": "bone16", "x": 63.08, "y": -0.13}], "slots": [{"name": "5", "bone": "root", "attachment": "web"}, {"name": "line", "bone": "root"}], "skins": [{"name": "default", "attachments": {"5": {"web": {"type": "mesh", "uvs": [0.45945, 0, 0.50071, 0, 0.65442, 0.09935, 0.71745, 0.23155, 0.79275, 0.38946, 0.85993, 0.52053, 0.92898, 0.65525, 0.97477, 0.77121, 1, 0.82412, 1, 0.91103, 0.90612, 0.93363, 0.87441, 0.86523, 0.74901, 0.87465, 0.73854, 0.97398, 0.63046, 1, 0.56215, 0.9092, 0.42938, 0.91234, 0.40475, 0.99763, 0.27237, 0.99624, 0.23514, 0.87779, 0.14171, 0.86523, 0.08876, 0.93787, 0, 0.90965, 0, 0.83582, 0.03881, 0.71285, 0.06488, 0.63025, 0.11567, 0.48679, 0.14998, 0.38988, 0.23034, 0.23242, 0.27106, 0.15261, 0.34484, 0.077, 0.41998, 0, 0.35807, 0.30617, 0.32365, 0.59826, 0.32365, 0.85267, 0.49822, 0.76158, 0.68017, 0.82754, 0.81048, 0.78985, 0.64329, 0.59198, 0.59903, 0.32188, 0.11958, 0.75844, 0.3839, 0.82123, 0.60963, 0.80197, 0.88767, 0.78109, 0.33991, 0.46032, 0.62257, 0.46553, 0.38205, 0.18762, 0.55731, 0.18528, 0.52896, 0.09249, 0.39696, 0.11385, 0.45966, 0.10371, 0.46163, 0.18656, 0.46755, 0.31331, 0.46757, 0.46267, 0.47251, 0.59534, 0.2034, 0.61313, 0.23012, 0.47328, 0.25077, 0.34933, 0.28586, 0.21602, 0.32678, 0.13546], "triangles": [11, 7, 8, 9, 10, 8, 10, 11, 8, 38, 5, 6, 6, 37, 38, 43, 6, 7, 43, 37, 6, 11, 37, 43, 12, 37, 11, 7, 11, 43, 39, 3, 4, 45, 4, 5, 2, 48, 1, 47, 48, 2, 47, 2, 3, 14, 36, 13, 14, 15, 36, 35, 54, 38, 42, 35, 38, 36, 42, 38, 37, 36, 38, 12, 36, 37, 15, 35, 42, 13, 36, 12, 36, 15, 42, 51, 48, 47, 52, 51, 47, 39, 47, 3, 52, 47, 39, 53, 52, 39, 45, 39, 4, 53, 39, 45, 38, 45, 5, 54, 53, 45, 54, 45, 38, 17, 34, 16, 18, 34, 17, 33, 56, 44, 54, 33, 44, 54, 44, 53, 55, 56, 33, 41, 33, 54, 41, 54, 35, 34, 33, 41, 55, 33, 34, 40, 55, 34, 34, 19, 40, 16, 41, 35, 16, 35, 15, 18, 19, 34, 16, 34, 41, 31, 0, 50, 49, 31, 50, 51, 50, 48, 49, 50, 51, 46, 59, 49, 46, 49, 51, 32, 58, 46, 46, 51, 52, 32, 46, 52, 57, 58, 32, 44, 57, 32, 32, 52, 53, 44, 32, 53, 56, 57, 44, 23, 40, 20, 21, 22, 23, 20, 21, 23, 25, 26, 55, 40, 25, 55, 24, 25, 40, 23, 24, 40, 19, 20, 40, 27, 28, 57, 56, 27, 57, 26, 27, 56, 55, 26, 56, 49, 30, 31, 59, 30, 49, 59, 29, 30, 58, 29, 59, 58, 59, 46, 28, 29, 58, 57, 28, 58, 50, 0, 1, 50, 1, 48], "vertices": [5, 1, -5.63, 139.94, 0.23408, 2, -7.43, -3.32, 0.23577, 6, -17.61, 5.11, 0.27536, 10, -15.24, -18.24, 0.17239, 14, -24.86, -5.59, 0.0824, 3, 1, 9.76, 70.62, 0.47882, 10, -70.41, 17.1, 0.35262, 14, -59.27, 50.15, 0.16856, 1, 14, 46.93, 25.66, 1, 2, 14, 90.9, 15.21, 0.54431, 15, -22.77, 9.54, 0.45569, 1, 15, 31.19, 11.33, 1, 2, 15, 76.83, 14.34, 0.51555, 16, -31.57, 11.84, 0.48445, 2, 15, 123.75, 17.43, 0.01756, 16, 14.95, 18.69, 0.98244, 2, 16, 52.85, 19.97, 0.54217, 17, -10.23, 20.09, 0.45783, 2, 16, 70.83, 22.01, 0.05067, 17, 7.75, 22.14, 0.94933, 1, 17, 30.78, 11.48, 1, 3, 12, 109.63, 84.79, 0.01068, 16, 85.14, -23.2, 0.03622, 17, 22.06, -23.07, 0.9531, 4, 12, 88.49, 75.19, 0.10352, 13, -5.52, 82.37, 0.00123, 16, 62.04, -25.54, 0.3735, 17, -1.04, -25.41, 0.52174, 5, 12, 86.17, 28.39, 0.52922, 13, -7.84, 35.58, 0.2829, 15, 146.54, -72.52, 0.00069, 16, 44.89, -69.14, 0.17462, 17, -18.19, -69.01, 0.01257, 4, 12, 114.58, 21.37, 0.08101, 13, 20.57, 28.56, 0.89702, 16, 69.57, -84.87, 0.02192, 17, 6.49, -84.74, 5e-05, 3, 8, 110.98, 118.4, 0.00384, 9, 13.03, 114.28, 0.00136, 13, 23.77, -12.34, 0.9948, 4, 8, 86.18, 91.25, 0.14661, 9, -11.77, 87.13, 0.05106, 12, 88.67, -41.99, 0.32437, 13, -5.34, -34.81, 0.47795, 4, 8, 90.31, 41.89, 0.3469, 9, -7.63, 37.77, 0.47438, 12, 84.23, -91.32, 0.12137, 13, -9.78, -84.14, 0.05736, 4, 8, 115.76, 34.34, 0.05566, 9, 17.82, 30.22, 0.90677, 12, 107.99, -103.15, 0.02481, 13, 13.98, -95.96, 0.01276, 4, 4, 76.09, 96.08, 0.00509, 5, 10.35, 92.78, 0.00103, 8, 118.57, -14.96, 0.02403, 9, 20.63, -19.08, 0.96986, 5, 3, 137.07, 71.39, 0.00039, 4, 49.66, 69.8, 0.15662, 5, -16.08, 66.5, 0.0468, 8, 84.96, -31.07, 0.56638, 9, -12.98, -35.19, 0.22982, 4, 4, 59.88, 36.28, 0.36856, 5, -5.86, 32.98, 0.42388, 8, 83.57, -66.08, 0.20171, 9, -14.38, -70.2, 0.00586, 3, 4, 87.12, 26.37, 0.02634, 5, 21.37, 23.07, 0.95186, 8, 106.02, -84.41, 0.0218, 1, 5, 26.7, -10.62, 1, 2, 4, 72.6, -15.74, 0.10654, 5, 6.86, -19.04, 0.89346, 3, 3, 124.13, -15.3, 0.10243, 4, 33.89, -16.42, 0.53856, 5, -31.85, -19.71, 0.359, 2, 3, 98.16, -16.61, 0.17124, 4, 7.89, -16.88, 0.82876, 2, 3, 52.19, -17.02, 0.66586, 4, -38.07, -15.78, 0.33414, 1, 3, 21.13, -17.29, 1, 2, 2, 100.51, -19.33, 0.66365, 3, -33.19, -9.41, 0.33635, 1, 2, 73.12, -24.17, 1, 2, 2, 38.19, -19.16, 0.73309, 6, 16.75, -28.83, 0.26691, 2, 2, 2.62, -14.07, 0.46127, 6, -13.15, -8.92, 0.53873, 3, 2, 83.68, 30.17, 0.06362, 6, 79.05, -3.89, 0.83239, 7, -3.78, -3.34, 0.104, 4, 3, 49.13, 67.06, 0.01872, 4, -38.37, 68.35, 0.00125, 7, 82.47, -3.3, 0.30751, 8, 1.37, -3.43, 0.67252, 6, 4, 30.03, 97.34, 0.00146, 5, -35.72, 94.04, 0.00035, 8, 75.49, 1.4, 0.83362, 9, -22.45, -2.72, 0.16059, 12, 62.64, -128.64, 0.00288, 13, -31.37, -121.46, 0.0011, 6, 7, 119.91, 68.2, 0.02991, 8, 44.72, 64.65, 0.39909, 9, -53.23, 60.53, 0.05519, 11, 118.84, -66.06, 0.03805, 12, 43.24, -61.04, 0.40716, 13, -50.77, -53.85, 0.0706, 5, 12, 69.72, 4.35, 0.96684, 13, -24.29, 11.53, 0.0156, 15, 121.94, -88.12, 0.00068, 16, 21.61, -86.67, 0.0166, 17, -41.46, -86.54, 0.00028, 5, 12, 64.03, 53.86, 0.38225, 13, -29.98, 61.05, 0.0084, 15, 136.25, -40.38, 0.02051, 16, 32.05, -37.93, 0.54573, 17, -31.03, -37.8, 0.04312, 5, 7, 62.86, 114.31, 0.00139, 8, -8.22, 115.42, 0.00144, 9, -106.17, 111.3, 1e-05, 11, 83.36, -1.86, 0.53946, 12, -0.15, -1.89, 0.4577, 4, 10, 90.59, 0.28, 0.18227, 11, 2.82, 0.55, 0.81333, 14, 78.56, -34.74, 0.00433, 15, -21.52, -41.89, 7e-05, 4, 3, 123.56, 17.63, 0.00718, 4, 34.39, 16.51, 0.82061, 5, -31.35, 13.22, 0.0619, 8, 52.99, -76.35, 0.11031, 5, 8, 64.87, 23.23, 0.73503, 9, -33.08, 19.11, 0.16488, 11, 125.8, -111.59, 0.0024, 12, 55.95, -105.31, 0.07462, 13, -38.07, -98.13, 0.02307, 6, 7, 125.36, 111.05, 0.00222, 8, 53.78, 106.88, 0.07406, 9, -44.17, 102.76, 0.01438, 11, 140.03, -28.42, 0.00025, 12, 59.45, -21, 0.78963, 13, -34.56, -13.82, 0.11946, 3, 12, 64.6, 82.76, 0.05662, 16, 41.82, -10.73, 0.85292, 17, -21.26, -10.6, 0.09047, 6, 2, 121.17, 55.98, 0.03004, 3, 10.03, 55.64, 0.00988, 4, -77.82, 58.22, 0.00066, 6, 124, 3.26, 0.3931, 7, 41.74, -3.32, 0.2114, 8, -39.22, 0, 0.35492, 8, 7, 27.5, 101.16, 0.00074, 8, -44.57, 105.3, 0.00077, 9, -142.51, 101.18, 0, 10, 133.11, -5.12, 0.08533, 11, 45.65, -0.73, 0.66768, 12, -37.69, -5.59, 0.24342, 14, 114.54, -58.02, 0.00203, 15, 19.33, -54.86, 3e-05, 3, 2, 52.29, 13.04, 0.2176, 6, 43.34, -5.84, 0.71867, 7, -39.35, 0.34, 0.06373, 5, 1, 30.87, 85.84, 0.2032, 10, 47.81, -1.41, 0.25456, 11, -39.6, -5.24, 0.46817, 14, 39.27, -17.73, 0.07402, 15, -63.9, -35.83, 4e-05, 5, 1, 20.29, 112.93, 0.34124, 10, 18.75, -2.55, 0.30367, 11, -68.42, -9.18, 0.2337, 14, 12.58, -6.17, 0.12137, 15, -92.69, -31.72, 2e-05, 3, 2, 32.76, 2.38, 0.3134, 6, 21.13, -7.05, 0.64793, 7, -61.47, 2.62, 0.03867, 8, 1, -5.55, 109.66, 0.1621, 2, 14.62, 17.43, 0.16453, 6, 11.23, 14.35, 0.34015, 7, -67.9, 25.31, 0.0203, 10, 13.41, -28.06, 0.14425, 11, -71.29, -35.08, 0.11101, 14, -3.29, -26.84, 0.05765, 15, -102.55, -55.84, 1e-05, 8, 1, -4.82, 85.47, 0.09227, 2, 31.78, 34.5, 0.11879, 6, 34.07, 22.36, 0.39233, 7, -44.08, 29.64, 0.03479, 10, 36.51, -35.26, 0.1156, 11, -47.59, -40.03, 0.2126, 14, 14.42, -43.35, 0.03361, 15, -81.12, -67.09, 2e-05, 7, 2, 57.29, 61.41, 0.03471, 6, 68.68, 35.66, 0.45418, 7, -7.82, 37.35, 0.05674, 10, 72.22, -45.26, 0.08282, 11, -11.09, -46.54, 0.36954, 14, 42.27, -67.82, 0.00197, 15, -47.8, -83.36, 3e-05, 12, 2, 89.13, 91.22, 0.01647, 3, -9.35, 99.14, 0.00542, 4, -95.77, 102.34, 0.00036, 6, 110.25, 48.86, 0.21555, 7, 35.31, 43.87, 0.11625, 8, -41.64, 47.56, 0.19496, 9, -139.58, 43.44, 0, 10, 113.44, -59.49, 0.03854, 11, 31.31, -56.74, 0.30156, 12, -44.77, -62.97, 0.10994, 14, 73.26, -98.51, 0.00092, 15, -9.82, -104.8, 2e-05, 7, 3, 25.03, 117.09, 0.01, 4, -60.82, 119.15, 0.00067, 7, 73.34, 51.47, 0.16495, 8, -3.1, 51.92, 0.35998, 9, -101.05, 47.8, 0, 11, 69.4, -64.02, 0.25124, 12, -6.06, -65.33, 0.21316, 4, 3, 71.92, 28.18, 0.0896, 4, -16.87, 28.75, 0.38578, 7, 93.46, -47.01, 0.16461, 8, 8.62, -47.91, 0.36001, 6, 2, 151.91, 28.67, 0.01533, 3, 30.67, 20.07, 0.33105, 4, -58.36, 21.99, 0.16393, 6, 139.99, -34.62, 0.20064, 7, 51.59, -43.25, 0.1079, 8, -32.78, -40.62, 0.18115, 4, 2, 120.23, 9.56, 0.03081, 3, -5.41, 11.85, 0.51565, 6, 103.16, -38.23, 0.40317, 7, 14.65, -41.03, 0.05037, 4, 2, 82.86, -7.49, 0.50041, 3, -46.23, 7.37, 0.21325, 6, 62.1, -37.53, 0.26302, 7, -25.79, -33.9, 0.02332, 3, 2, 55.26, -12.42, 0.69612, 6, 35.06, -30.09, 0.28677, 7, -51.33, -22.32, 0.01712], "hull": 32}}, "line": {"line": {"type": "mesh", "uvs": [1, 0.28018, 1, 0.58542, 1, 1, 0, 1, 0, 0.58024, 0, 0.26983, 0, 0, 1, 0], "triangles": [5, 6, 7, 5, 7, 0, 4, 5, 0, 4, 0, 1, 3, 4, 1, 3, 1, 2], "vertices": [11.06, 344.23, 11.06, 314.62, 11.06, 274.41, -3.7, 274.41, -3.7, 315.12, -3.7, 345.23, -3.7, 371.41, 11.06, 371.41], "hull": 8}}}}], "animations": {"attack2": {"slots": {"5": {"attachment": [{}, {"time": 0.0333, "name": "web"}]}, "line": {"attachment": [{}, {"time": 0.0333}]}}, "bones": {"bone": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6333}], "translate": [{"y": -111.91, "curve": "stepped"}, {"time": 0.0333, "y": -111.91, "curve": [0.144, 1.47, 0.256, 4.42, 0.144, -36.7, 0.256, 113.73]}, {"time": 0.3667, "x": 4.42, "y": 113.73, "curve": [0.456, 4.42, 0.544, 1.47, 0.456, 113.73, 0.544, 11.8]}, {"time": 0.6333}], "scale": [{"x": 0.214, "y": 0.214, "curve": "stepped"}, {"time": 0.0333, "x": 0.214, "y": 0.214}, {"time": 0.1667, "curve": [0.233, 1.088, 0.3, 1.265, 0.233, 0.904, 0.3, 0.712]}, {"time": 0.3667, "x": 1.265, "y": 0.712, "curve": [0.456, 1.265, 0.544, 1.088, 0.456, 0.712, 0.544, 0.904]}, {"time": 0.6333}], "shear": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6333}]}, "bone17": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7}], "translate": [{"x": 4.95, "y": -10.17, "curve": "stepped"}, {"time": 0.0333, "x": 4.95, "y": -10.17, "curve": "stepped"}, {"time": 0.1, "x": 4.95, "y": -10.17, "curve": "stepped"}, {"time": 0.1667, "x": 4.95, "y": -10.17, "curve": [0.256, 4.95, 0.344, -7.25, 0.256, -10.17, 0.344, 58.91]}, {"time": 0.4333, "x": -7.25, "y": 58.91, "curve": [0.522, -7.25, 0.611, 1.65, 0.522, 58.91, 0.611, -3.39]}, {"time": 0.7}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7}], "shear": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7}]}, "bone16": {"rotate": [{"value": -2.2, "curve": "stepped"}, {"time": 0.0333, "value": -2.2, "curve": "stepped"}, {"time": 0.1, "value": -2.2}, {"time": 0.1667, "value": 17.4, "curve": "stepped"}, {"time": 0.5667, "value": 17.4, "curve": [0.656, 17.4, 0.744, 5.8]}, {"time": 0.8333}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333}], "shear": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333}]}, "bone15": {"rotate": [{"value": -3.71, "curve": "stepped"}, {"time": 0.0333, "value": -3.71, "curve": "stepped"}, {"time": 0.1, "value": -3.71}, {"time": 0.1667, "value": 37.34, "curve": "stepped"}, {"time": 0.5, "value": 37.34, "curve": [0.589, 37.34, 0.678, 12.45]}, {"time": 0.7667}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7667}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7667}], "shear": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7667}]}, "bone14": {"rotate": [{"value": -26.03, "curve": "stepped"}, {"time": 0.0333, "value": -26.03, "curve": "stepped"}, {"time": 0.1, "value": -26.03}, {"time": 0.1667, "value": 15.4, "curve": "stepped"}, {"time": 0.4333, "value": 15.4, "curve": [0.522, 15.4, 0.611, 5.13]}, {"time": 0.7}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7}], "shear": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7}]}, "bone13": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7}], "translate": [{"x": 2.18, "y": 19.85, "curve": "stepped"}, {"time": 0.0333, "x": 2.18, "y": 19.85, "curve": "stepped"}, {"time": 0.1, "x": 2.18, "y": 19.85, "curve": "stepped"}, {"time": 0.1667, "x": 2.18, "y": 19.85, "curve": [0.256, 2.18, 0.344, -102.01, 0.256, 19.85, 0.344, 54.3]}, {"time": 0.4333, "x": -102.01, "y": 54.3, "curve": [0.522, -102.01, 0.611, 0.73, 0.522, 54.3, 0.611, 6.62]}, {"time": 0.7}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7}], "shear": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7}]}, "bone12": {"rotate": [{"value": -15.31, "curve": "stepped"}, {"time": 0.0333, "value": -15.31, "curve": "stepped"}, {"time": 0.1, "value": -15.31}, {"time": 0.1667, "value": -9.07, "curve": "stepped"}, {"time": 0.5667, "value": -9.07, "curve": [0.656, -9.07, 0.744, -3.02]}, {"time": 0.8333}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": [0.256, 0, 0.345, -41.35, 0.256, 0, 0.345, 27.1]}, {"time": 0.4333, "x": -41.35, "y": 27.1, "curve": [0.479, -41.35, 0.523, 0, 0.479, 27.1, 0.523, 0]}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333}], "shear": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333}]}, "bone11": {"rotate": [{"value": 0.02, "curve": "stepped"}, {"time": 0.0333, "value": 0.02, "curve": "stepped"}, {"time": 0.1, "value": 0.02}, {"time": 0.1667, "value": 10.13, "curve": "stepped"}, {"time": 0.5, "value": 10.13, "curve": [0.589, 10.13, 0.678, 3.38]}, {"time": 0.7667}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": [0.256, 0, 0.345, -28.89, 0.256, 0, 0.345, 8.35]}, {"time": 0.4333, "x": -28.89, "y": 8.35, "curve": [0.456, -28.89, 0.478, 0, 0.456, 8.35, 0.478, 0]}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7667}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7667}], "shear": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7667}]}, "bone10": {"rotate": [{"value": -9.46, "curve": "stepped"}, {"time": 0.0333, "value": -9.46, "curve": "stepped"}, {"time": 0.1, "value": -9.46}, {"time": 0.1667, "value": 9.58, "curve": "stepped"}, {"time": 0.4333, "value": 9.58, "curve": [0.522, 9.58, 0.611, 3.19]}, {"time": 0.7}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7}], "shear": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7}]}, "bone9": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": [0.256, 0, 0.344, -110.43, 0.256, 0, 0.344, -21.5]}, {"time": 0.4333, "x": -110.43, "y": -21.5, "curve": [0.522, -110.43, 0.611, 0, 0.522, -21.5, 0.611, 0]}, {"time": 0.7}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7}], "shear": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7}]}, "bone8": {"rotate": [{"value": 6.31, "curve": "stepped"}, {"time": 0.0333, "value": 6.31, "curve": "stepped"}, {"time": 0.1, "value": 6.31, "curve": "stepped"}, {"time": 0.1667, "value": 6.31, "curve": "stepped"}, {"time": 0.5667, "value": 6.31, "curve": [0.656, 6.31, 0.744, 2.1]}, {"time": 0.8333}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": [0.256, 0, 0.345, -33.04, 0.256, 0, 0.345, -8]}, {"time": 0.4333, "x": -33.04, "y": -8, "curve": [0.479, -33.04, 0.523, 0, 0.479, -8, 0.523, 0]}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333}], "shear": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333}]}, "bone7": {"rotate": [{"value": -0.96, "curve": "stepped"}, {"time": 0.0333, "value": -0.96, "curve": "stepped"}, {"time": 0.1, "value": -0.96}, {"time": 0.1667, "value": 14.08, "curve": "stepped"}, {"time": 0.5, "value": 14.08, "curve": [0.589, 14.08, 0.678, 4.69]}, {"time": 0.7667}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": [0.256, 0, 0.345, -25.02, 0.256, 0, 0.345, -23.3]}, {"time": 0.4333, "x": -25.02, "y": -23.3, "curve": [0.456, -25.02, 0.478, 0, 0.456, -23.3, 0.478, 0]}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7667}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7667}], "shear": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7667}]}, "bone6": {"rotate": [{"value": 12.68, "curve": "stepped"}, {"time": 0.0333, "value": 12.68, "curve": "stepped"}, {"time": 0.1, "value": 12.68}, {"time": 0.1667, "value": -19.1, "curve": "stepped"}, {"time": 0.4333, "value": -19.1, "curve": [0.522, -19.1, 0.611, -6.37]}, {"time": 0.7}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7}], "shear": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7}]}, "bone5": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": [0.256, 0, 0.344, -16, 0.256, 0, 0.344, -47.59]}, {"time": 0.4333, "x": -16, "y": -47.59, "curve": [0.522, -16, 0.611, 0, 0.522, -47.59, 0.611, 0]}, {"time": 0.7}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7}], "shear": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7}]}, "bone4": {"rotate": [{"value": 9.83, "curve": "stepped"}, {"time": 0.0333, "value": 9.83, "curve": "stepped"}, {"time": 0.1, "value": 9.83}, {"time": 0.1667, "value": -20.48, "curve": "stepped"}, {"time": 0.5667, "value": -20.48, "curve": [0.656, -20.48, 0.744, -6.83]}, {"time": 0.8333}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333}], "shear": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333}]}, "bone3": {"rotate": [{"value": 26.09, "curve": "stepped"}, {"time": 0.0333, "value": 26.09, "curve": "stepped"}, {"time": 0.1, "value": 26.09}, {"time": 0.1667, "value": -6.75, "curve": "stepped"}, {"time": 0.5, "value": -6.75, "curve": [0.589, -6.75, 0.678, -2.25]}, {"time": 0.7667}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7667}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7667}], "shear": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7667}]}, "bone2": {"rotate": [{"value": 13.79, "curve": "stepped"}, {"time": 0.0333, "value": 13.79, "curve": "stepped"}, {"time": 0.1, "value": 13.79}, {"time": 0.1667, "value": -22.94, "curve": "stepped"}, {"time": 0.4333, "value": -22.94, "curve": [0.522, -22.94, 0.611, -7.65]}, {"time": 0.7}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7}], "shear": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7}]}}}, "pull": {"slots": {"line": {"attachment": [{"name": "line"}]}}, "bones": {"bone": {"rotate": [{}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": [0.209, 0, 0.258, 0, 0.209, 0, 0.258, -10.6]}, {"time": 0.3, "y": -10.6}], "scale": [{"curve": [0.055, 1, 0.112, 1.514, 0.055, 1, 0.112, 0.848]}, {"time": 0.1667, "x": 1.514, "y": 0.848, "curve": [0.211, 1.514, 0.256, 1.324, 0.211, 0.848, 0.256, 1.039]}, {"time": 0.3, "x": 1.324, "y": 1.039, "curve": [0.338, 1.324, 0.362, 1.512, 0.338, 1.039, 0.362, 0.964]}, {"time": 0.4, "x": 1.512, "y": 0.964}], "shear": [{}]}, "bone17": {"rotate": [{}], "translate": [{"curve": [0.071, 0, 0.129, -8.51, 0.071, 0, 0.129, -8.62]}, {"time": 0.2, "x": -8.51, "y": -8.62}], "scale": [{}], "shear": [{}]}, "bone16": {"rotate": [{"curve": [0.071, 0, 0.129, -16.51]}, {"time": 0.2, "value": -16.51}], "translate": [{"curve": [0.071, 0, 0.129, -9.02, 0.071, 0, 0.129, -30.14]}, {"time": 0.2, "x": -9.02, "y": -30.14}], "scale": [{}], "shear": [{}]}, "bone15": {"rotate": [{}], "translate": [{"curve": [0.071, 0, 0.129, -33.56, 0.071, 0, 0.129, 18.36]}, {"time": 0.2, "x": -33.56, "y": 18.36}], "scale": [{}], "shear": [{}]}, "bone14": {"rotate": [{"curve": [0.071, 0, 0.129, -19.89]}, {"time": 0.2, "value": -19.89}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone13": {"rotate": [{}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone12": {"rotate": [{}], "translate": [{"curve": [0.071, 0, 0.129, 15.27, 0.071, 0, 0.129, 0]}, {"time": 0.2, "x": 15.27}], "scale": [{"curve": [0.071, 1, 0.129, 0.545, 0.071, 1, 0.129, 1]}, {"time": 0.2, "x": 0.545}], "shear": [{}]}, "bone11": {"rotate": [{"curve": [0.071, 0, 0.129, -5.45]}, {"time": 0.2, "value": -5.45}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone10": {"rotate": [{"curve": [0.071, 0, 0.129, -9.18]}, {"time": 0.2, "value": -9.18}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone9": {"rotate": [{}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone8": {"rotate": [{}], "translate": [{}], "scale": [{"curve": [0.071, 1, 0.129, 0.719, 0.071, 1, 0.129, 1]}, {"time": 0.2, "x": 0.719}], "shear": [{}]}, "bone7": {"rotate": [{"curve": [0.071, 0, 0.129, 1.71]}, {"time": 0.2, "value": 1.71}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone6": {"rotate": [{"curve": [0.071, 0, 0.129, 11.72]}, {"time": 0.2, "value": 11.72}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone5": {"rotate": [{}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone4": {"rotate": [{"curve": [0.071, 0, 0.129, 16.97]}, {"time": 0.2, "value": 16.97}], "translate": [{"curve": [0.071, 0, 0.129, -5, 0.071, 0, 0.129, 9.48]}, {"time": 0.2, "x": -5, "y": 9.48}], "scale": [{}], "shear": [{}]}, "bone3": {"rotate": [{"curve": [0.071, 0, 0.129, 15.93]}, {"time": 0.2, "value": 15.93}], "translate": [{"curve": [0.071, 0, 0.129, -20.55, 0.071, 0, 0.129, -16.74]}, {"time": 0.2, "x": -20.55, "y": -16.74}], "scale": [{}], "shear": [{}]}, "bone2": {"rotate": [{"curve": [0.071, 0, 0.129, 17.97]}, {"time": 0.2, "value": 17.97}], "translate": [{}], "scale": [{}], "shear": [{}]}}, "attachments": {"default": {"5": {"web": {"deform": [{"curve": [0.071, 0, 0.129, 1]}, {"time": 0.2, "offset": 28, "vertices": [-0.42398, 1.38638, -0.8952, 1.14053, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15.88521, -1.34691, 21.84086, -2.68576, 21.84158, -2.68582, 15.94069, 0.30383, 28.93483, 2.33579, 28.93433, 2.33572, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.00011, -8.8482, -0.00062, -8.84831, -2.73221, -8.41587, 0.48251, -8.83508, 0.483, -8.83518, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.23809, -1.25896, 10.2382, -1.25897, 7.47195, 0.14242, 13.563, 1.09488, 13.56293, 1.09486, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.83229, 7.99574, 3.75182, 8.78561, 0.90546, 8.8007, -0.74619, 8.70137, -1.51521, 8.83069, 0.16834, 8.01541, -3.70982, 8.70982, -1.46668, 10.66096, 5.0024, 11.71408, 1.20729, 11.73425, -0.99492, 11.60181, -2.02025, 11.77423, 0.22445, 10.68723, -4.9464, 11.61313, -1.95557, 16.79677, 7.88145, 18.32259, -2.92001, 16.3972, -8.68204, 18.45589, 1.90211, 18.48723, -1.56751, 25.4185, -3.12567, 25.4187, -3.12569, 18.27882, -3.18301, 18.5507, 0.35358, 33.67323, 2.71826, 16.83803, -7.7932, 18.2964, -3.08117, 13.77605, -2.19545, 12.32836, -6.52773, 13.89995, -1.17855, 19.11113, -2.35007, 19.11133, -2.35008, 13.94771, 0.26585, 25.3176, 2.04376]}]}}, "line": {"line": {"deform": [{"offset": 1, "vertices": [3496.7275, 0, 1005.7973, 0, 1005.7973, 0, 1005.7973, 0, 1005.7973, 0, 3496.7275, 0, 3496.7275, 0, 3496.7275], "curve": [0.071, 0, 0.129, 1]}, {"time": 0.2, "offset": 1, "vertices": [3946.326, 0, -42.71133, 0, -42.71133, 0, -42.71133, 0, -42.71133, 0, 3946.326, 0, 3946.326, 0, 3946.326], "curve": [0.228, 0, 0.272, 1]}, {"time": 0.3, "offset": 1, "vertices": [3975.6072, 0, -35.15179, 0, -35.15179, 0, -35.15179, 0, -35.15179, 0, 3975.6072, 0, 3975.6072, 0, 3975.6072], "curve": [0.335, 0, 0.365, 1]}, {"time": 0.4, "offset": 1, "vertices": [3975.6074, 0, -51.95078, 0, -51.95078, 0, -51.95078, 0, -51.95078, 0, 3975.6074, 0, 3975.6074, 0, 3975.6074]}]}}}}}}}