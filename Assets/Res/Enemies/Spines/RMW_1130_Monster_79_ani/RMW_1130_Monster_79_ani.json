{"skeleton": {"hash": "1g43ez43+t0", "spine": "4.1.11", "x": -77.14, "y": -1.58, "width": 154.05, "height": 174, "images": "./images/", "audio": "F:/动效/RogueMatchWar/RMW_1130_Monster_79_ani"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root"}, {"name": "fly-L", "parent": "bone", "length": 30.13, "rotation": 173.98, "x": -13.94, "y": 143.61}, {"name": "fly-L2", "parent": "fly-L", "length": 28.31, "rotation": -32.06, "x": 30.13}, {"name": "fly-R", "parent": "bone", "length": 30.13, "rotation": -173.98, "x": 13.69, "y": 143.61, "scaleX": -1}, {"name": "fly-L4", "parent": "fly-R", "length": 28.31, "rotation": -32.06, "x": 30.13}, {"name": "body", "parent": "bone", "x": -0.29, "y": 139.08}, {"name": "eye", "parent": "bone", "x": -0.59, "y": 125.57}, {"name": "hand-L", "parent": "bone", "length": 40.16, "rotation": -117.14, "x": -27.62, "y": 127.67}, {"name": "fly2", "parent": "bone", "length": 52.61, "rotation": -131.4, "x": -16.48, "y": 129.31}, {"name": "fly3", "parent": "fly2", "length": 28.79, "rotation": 6.74, "x": 52.61}, {"name": "fly2-R", "parent": "bone", "length": 52.61, "rotation": 131.4, "x": 16.26, "y": 129.31, "scaleX": -1}, {"name": "fly5", "parent": "fly2-R", "length": 28.79, "rotation": 6.74, "x": 52.61}, {"name": "leg-L", "parent": "bone", "length": 42.1, "rotation": -91.19, "x": -7.13, "y": 56.23}, {"name": "hand-R", "parent": "bone", "length": 40.16, "rotation": 117.14, "x": 26.75, "y": 127.67, "scaleX": -1}, {"name": "leg-R", "parent": "bone", "length": 42.1, "rotation": 91.19, "x": 7.69, "y": 56.23, "scaleX": -1}, {"name": "tou", "parent": "bone", "x": -0.45, "y": 115.62}], "slots": [{"name": "fly2", "bone": "fly2", "attachment": "fly2"}, {"name": "fly3", "bone": "fly2-R", "attachment": "fly2"}, {"name": "leg-L", "bone": "leg-L", "attachment": "leg-L"}, {"name": "leg-R", "bone": "leg-R", "attachment": "leg-L"}, {"name": "hand-L", "bone": "hand-L", "attachment": "hand-L"}, {"name": "hand-R", "bone": "hand-R", "attachment": "hand-L"}, {"name": "body", "bone": "body", "attachment": "body"}, {"name": "fly-L", "bone": "fly-L", "attachment": "fly-L"}, {"name": "fly-L2", "bone": "fly-R", "attachment": "fly-L"}, {"name": "tou", "bone": "tou", "attachment": "tou"}, {"name": "eye", "bone": "eye", "attachment": "eye"}], "skins": [{"name": "default", "attachments": {"body": {"body": {"x": 0.12, "y": -62.67, "width": 90, "height": 128}}, "eye": {"eye": {"x": 0.42, "y": 0.35, "width": 24, "height": 15}}, "fly-L": {"fly-L": {"type": "mesh", "uvs": [0.12481, 0, 0.17946, 0.087, 0.28282, 0.20833, 0.5324, 0.20755, 0.66475, 0.26939, 0.81053, 0.48664, 1, 0.57707, 0.9861, 0.73634, 0.97611, 0.85082, 0.70824, 0.97479, 0.32561, 0.97517, 0.11112, 0.68222, 0.01107, 0.30651, 0.01424, 0.1025, 0.06091, 0.01272, 0.76913, 0.74598, 0.44357, 0.6477, 0.20803, 0.41993, 0.10447, 0.24192], "triangles": [1, 18, 14, 1, 14, 0, 18, 1, 2, 13, 14, 18, 12, 13, 18, 17, 18, 2, 16, 2, 3, 16, 3, 4, 17, 2, 16, 17, 12, 18, 11, 17, 16, 11, 12, 17, 10, 11, 16, 10, 16, 9, 16, 4, 5, 7, 5, 6, 15, 16, 5, 15, 5, 7, 8, 15, 7, 9, 16, 15, 9, 15, 8], "vertices": [1, 3, 30.8, -8.45, 1, 1, 3, 26.21, -7.73, 1, 2, 2, 41.77, -16.39, 0.00161, 3, 18.57, -7.71, 0.99839, 2, 2, 27.38, -17.94, 0.30389, 3, 7.2, -16.66, 0.69611, 2, 2, 19.49, -16.35, 0.61384, 3, -0.33, -19.5, 0.38616, 2, 2, 10.19, -8.81, 0.96225, 3, -12.21, -18.05, 0.03775, 1, 2, -1.1, -6.46, 1, 1, 2, -0.95, -0.19, 1, 1, 2, -0.85, 4.31, 1, 2, 2, 14.1, 10.75, 0.99986, 3, -19.29, 0.6, 0.00014, 2, 2, 36.17, 13.09, 0.15459, 3, -1.83, 14.3, 0.84541, 1, 3, 15.01, 12.98, 1, 1, 3, 28.61, 5.03, 1, 1, 3, 33.38, -1.35, 1, 1, 3, 33.41, -5.77, 1, 1, 2, 11.52, 1.5, 1, 2, 2, 30.7, -0.33, 0.00823, 3, 0.66, 0.03, 0.99177, 1, 3, 16.89, 1.46, 1, 1, 3, 25.9, -0.3, 1], "hull": 15, "edges": [0, 28, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 12, 14, 14, 16, 14, 30, 30, 32, 32, 34, 34, 36, 36, 28], "width": 58, "height": 39}}, "fly-L2": {"fly-L": {"type": "mesh", "uvs": [0.12481, 0, 0.17946, 0.087, 0.28282, 0.20833, 0.5324, 0.20755, 0.66475, 0.26939, 0.81053, 0.48664, 1, 0.57707, 0.9861, 0.73634, 0.97611, 0.85082, 0.70824, 0.97479, 0.32561, 0.97517, 0.11112, 0.68222, 0.01107, 0.30651, 0.01424, 0.1025, 0.06091, 0.01272, 0.76913, 0.74598, 0.44357, 0.6477, 0.20803, 0.41993, 0.10447, 0.24192], "triangles": [1, 18, 14, 1, 14, 0, 18, 1, 2, 13, 14, 18, 12, 13, 18, 17, 18, 2, 16, 2, 3, 16, 3, 4, 17, 2, 16, 17, 12, 18, 11, 17, 16, 11, 12, 17, 10, 11, 16, 10, 16, 9, 16, 4, 5, 7, 5, 6, 15, 16, 5, 15, 5, 7, 8, 15, 7, 9, 16, 15, 9, 15, 8], "vertices": [1, 5, 30.8, -8.45, 1, 1, 5, 26.21, -7.73, 1, 2, 4, 41.77, -16.39, 0.00161, 5, 18.57, -7.71, 0.99839, 2, 4, 27.38, -17.94, 0.30389, 5, 7.2, -16.66, 0.69611, 2, 4, 19.49, -16.35, 0.61384, 5, -0.33, -19.5, 0.38616, 2, 4, 10.19, -8.81, 0.96225, 5, -12.21, -18.05, 0.03775, 1, 4, -1.1, -6.46, 1, 1, 4, -0.95, -0.19, 1, 1, 4, -0.85, 4.31, 1, 2, 4, 14.1, 10.75, 0.99986, 5, -19.29, 0.6, 0.00014, 2, 4, 36.17, 13.09, 0.15459, 5, -1.83, 14.3, 0.84541, 1, 5, 15.01, 12.98, 1, 1, 5, 28.61, 5.03, 1, 1, 5, 33.38, -1.35, 1, 1, 5, 33.41, -5.77, 1, 1, 4, 11.52, 1.5, 1, 2, 4, 30.7, -0.33, 0.00823, 5, 0.66, 0.03, 0.99177, 1, 5, 16.89, 1.46, 1, 1, 5, 25.9, -0.3, 1], "hull": 15, "edges": [0, 28, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 12, 14, 14, 16, 14, 30, 30, 32, 32, 34, 34, 36, 36, 28], "width": 58, "height": 39}}, "fly2": {"fly2": {"type": "mesh", "uvs": [0.66666, 1e-05, 0.83014, 0, 0.87517, 0.06383, 0.88235, 0.18745, 0.90352, 0.43068, 0.91807, 0.57052, 0.93414, 0.73315, 0.96914, 0.85323, 0.99999, 0.93485, 0.95456, 0.99999, 0.79843, 0.99999, 0.5876, 0.96028, 0.35972, 0.82601, 0.17305, 0.70278, 0.01548, 0.53226, 0, 0.41282, 0, 0.32545, 0, 0.23103, 0.08109, 0.09203, 0.17799, 0.05907, 0.39729, 0.02669, 0.19327, 0.23362, 0.47812, 0.17348, 0.73211, 0.16715, 0.24786, 0.43143, 0.53746, 0.39028, 0.78907, 0.39503, 0.44014, 0.79065, 0.66089, 0.72894, 0.88165, 0.72102, 0.74397, 0.88719], "triangles": [24, 15, 16, 24, 16, 21, 14, 15, 24, 13, 14, 24, 29, 28, 5, 29, 5, 6, 28, 27, 24, 28, 24, 25, 13, 24, 27, 12, 13, 27, 30, 28, 29, 27, 28, 30, 7, 30, 29, 7, 29, 6, 11, 27, 30, 12, 27, 11, 8, 10, 30, 8, 30, 7, 11, 30, 10, 9, 10, 8, 22, 21, 19, 18, 19, 21, 17, 18, 21, 16, 17, 21, 21, 22, 25, 24, 21, 25, 5, 28, 25, 2, 23, 0, 2, 0, 1, 22, 20, 0, 22, 0, 23, 23, 2, 3, 22, 19, 20, 25, 22, 23, 26, 23, 3, 25, 23, 26, 26, 3, 4, 25, 26, 5, 5, 26, 4], "vertices": [1, 9, 5.36, -12.14, 1, 1, 9, -2.42, -3.31, 1, 1, 9, 0.61, 3.68, 1, 2, 9, 10.28, 12.89, 0.99983, 10, -40.52, 17.77, 0.00017, 2, 9, 28.98, 31.41, 0.83781, 10, -19.78, 33.96, 0.16219, 2, 9, 39.61, 42.18, 0.57602, 10, -7.95, 43.41, 0.42398, 2, 9, 52.03, 54.66, 0.31399, 10, 5.84, 54.35, 0.68601, 2, 9, 60.09, 65.13, 0.21273, 10, 15.07, 63.8, 0.78727, 2, 9, 65.23, 72.62, 0.1876, 10, 21.06, 70.64, 0.8124, 2, 9, 72.67, 74.82, 0.18053, 10, 28.71, 71.95, 0.81947, 2, 9, 80.11, 66.39, 0.17314, 10, 35.1, 62.7, 0.82686, 2, 9, 86.93, 52.17, 0.15188, 10, 40.2, 47.78, 0.84812, 2, 9, 86.9, 30.27, 0.08839, 10, 37.61, 26.04, 0.91161, 2, 9, 85.8, 11.39, 0.01006, 10, 34.3, 7.41, 0.98994, 2, 9, 79.49, -9.3, 0.00785, 10, 25.6, -12.39, 0.99215, 2, 9, 70.55, -18.67, 0.17428, 10, 15.63, -20.64, 0.82572, 2, 9, 63.47, -24.91, 0.44778, 10, 7.87, -26.01, 0.55222, 2, 9, 55.82, -31.65, 0.7057, 10, -0.52, -31.81, 0.2943, 2, 9, 40.7, -37.2, 0.9195, 10, -16.19, -35.54, 0.0805, 2, 9, 33.41, -34.32, 0.95989, 10, -23.09, -31.83, 0.04011, 2, 9, 20.35, -24.79, 0.9997, 10, -34.94, -20.83, 0.0003, 2, 9, 46.83, -21.03, 0.83168, 10, -8.2, -20.2, 0.16832, 1, 9, 28.39, -9.94, 1, 1, 9, 15.79, 3.33, 1, 2, 9, 60.25, -3.95, 0.04997, 10, 7.13, -4.82, 0.95003, 2, 9, 43.13, 8.75, 0.92338, 10, -8.38, 9.8, 0.07662, 2, 9, 31.54, 22.68, 0.89671, 10, -18.26, 25, 0.10329, 2, 9, 80.2, 32.09, 0.10913, 10, 31.17, 28.63, 0.89087, 2, 9, 64.69, 39.6, 0.25549, 10, 16.65, 37.91, 0.74451, 2, 9, 53.54, 50.96, 0.33012, 10, 6.91, 50.5, 0.66988, 2, 9, 73.56, 55.39, 0.18319, 10, 27.31, 52.55, 0.81681], "hull": 21, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 36, 38, 38, 40, 34, 36, 40, 0, 0, 2, 32, 42, 42, 44, 44, 46, 46, 6, 28, 48, 48, 50, 50, 52, 52, 8, 24, 54, 54, 56, 56, 58, 58, 12], "width": 72, "height": 108}}, "fly3": {"fly2": {"type": "mesh", "uvs": [0.66666, 1e-05, 0.83014, 0, 0.87517, 0.06383, 0.88235, 0.18745, 0.90352, 0.43068, 0.91807, 0.57052, 0.93414, 0.73315, 0.96914, 0.85323, 0.99999, 0.93485, 0.95456, 0.99999, 0.79843, 0.99999, 0.5876, 0.96028, 0.35972, 0.82601, 0.17305, 0.70278, 0.01548, 0.53226, 0, 0.41282, 0, 0.32545, 0, 0.23103, 0.08109, 0.09203, 0.17799, 0.05907, 0.39729, 0.02669, 0.19327, 0.23362, 0.47812, 0.17348, 0.73211, 0.16715, 0.24786, 0.43143, 0.53746, 0.39028, 0.78907, 0.39503, 0.44014, 0.79065, 0.66089, 0.72894, 0.88165, 0.72102, 0.74397, 0.88719], "triangles": [24, 15, 16, 24, 16, 21, 14, 15, 24, 13, 14, 24, 29, 28, 5, 29, 5, 6, 28, 27, 24, 28, 24, 25, 13, 24, 27, 12, 13, 27, 30, 28, 29, 27, 28, 30, 7, 30, 29, 7, 29, 6, 11, 27, 30, 12, 27, 11, 8, 10, 30, 8, 30, 7, 11, 30, 10, 9, 10, 8, 22, 21, 19, 18, 19, 21, 17, 18, 21, 16, 17, 21, 21, 22, 25, 24, 21, 25, 5, 28, 25, 2, 23, 0, 2, 0, 1, 22, 20, 0, 22, 0, 23, 23, 2, 3, 22, 19, 20, 25, 22, 23, 26, 23, 3, 25, 23, 26, 26, 3, 4, 25, 26, 5, 5, 26, 4], "vertices": [1, 11, 5.36, -12.14, 1, 1, 11, -2.42, -3.31, 1, 1, 11, 0.61, 3.68, 1, 2, 11, 10.28, 12.89, 0.99983, 12, -40.52, 17.77, 0.00017, 2, 11, 28.98, 31.41, 0.83781, 12, -19.78, 33.96, 0.16219, 2, 11, 39.61, 42.18, 0.57602, 12, -7.95, 43.41, 0.42398, 2, 11, 52.03, 54.66, 0.31399, 12, 5.84, 54.35, 0.68601, 2, 11, 60.09, 65.13, 0.21273, 12, 15.07, 63.8, 0.78727, 2, 11, 65.23, 72.62, 0.1876, 12, 21.06, 70.64, 0.8124, 2, 11, 72.67, 74.82, 0.18053, 12, 28.71, 71.95, 0.81947, 2, 11, 80.11, 66.39, 0.17314, 12, 35.1, 62.7, 0.82686, 2, 11, 86.93, 52.17, 0.15188, 12, 40.2, 47.78, 0.84812, 2, 11, 86.9, 30.27, 0.08839, 12, 37.61, 26.04, 0.91161, 2, 11, 85.8, 11.39, 0.01006, 12, 34.3, 7.41, 0.98994, 2, 11, 79.49, -9.3, 0.00785, 12, 25.6, -12.39, 0.99215, 2, 11, 70.55, -18.67, 0.17428, 12, 15.63, -20.64, 0.82572, 2, 11, 63.47, -24.91, 0.44778, 12, 7.87, -26.01, 0.55222, 2, 11, 55.82, -31.65, 0.7057, 12, -0.52, -31.81, 0.2943, 2, 11, 40.7, -37.2, 0.9195, 12, -16.19, -35.54, 0.0805, 2, 11, 33.41, -34.32, 0.95989, 12, -23.09, -31.83, 0.04011, 2, 11, 20.35, -24.79, 0.9997, 12, -34.94, -20.83, 0.0003, 2, 11, 46.83, -21.03, 0.83168, 12, -8.2, -20.2, 0.16832, 1, 11, 28.39, -9.94, 1, 1, 11, 15.79, 3.33, 1, 2, 11, 60.25, -3.95, 0.04997, 12, 7.13, -4.82, 0.95003, 2, 11, 43.13, 8.75, 0.92338, 12, -8.38, 9.8, 0.07662, 2, 11, 31.54, 22.68, 0.89671, 12, -18.26, 25, 0.10329, 2, 11, 80.2, 32.09, 0.10913, 12, 31.17, 28.63, 0.89087, 2, 11, 64.69, 39.6, 0.25549, 12, 16.65, 37.91, 0.74451, 2, 11, 53.54, 50.96, 0.33012, 12, 6.91, 50.5, 0.66988, 2, 11, 73.56, 55.39, 0.18319, 12, 27.31, 52.55, 0.81681], "hull": 21, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 36, 38, 38, 40, 34, 36, 40, 0, 0, 2, 32, 42, 42, 44, 44, 46, 46, 6, 28, 48, 48, 50, 50, 52, 52, 8, 24, 54, 54, 56, 56, 58, 58, 12], "width": 72, "height": 108}}, "hand-L": {"hand-L": {"x": 35.32, "y": 4, "rotation": 117.14, "width": 36, "height": 74}}, "hand-R": {"hand-L": {"x": 35.32, "y": 4, "rotation": 117.14, "width": 36, "height": 74}}, "leg-L": {"leg-L": {"x": 20.91, "y": -4.61, "rotation": 91.19, "width": 24, "height": 74}}, "leg-R": {"leg-L": {"x": 20.91, "y": -4.61, "rotation": 91.19, "width": 24, "height": 74}}, "tou": {"tou": {"x": 0.28, "y": 5.79, "width": 74, "height": 80}}}}], "animations": {"animation": {"bones": {"bone": {"translate": [{"curve": [0.165, 0, 0.335, 0, 0.165, 0, 0.335, -4.81]}, {"time": 0.5, "y": -4.81, "curve": [0.665, 0, 0.835, 0, 0.665, -4.81, 0.835, 0]}, {"time": 1}]}, "fly2": {"rotate": [{"value": -0.72, "curve": [0.046, -1.57, 0.089, -2.21]}, {"time": 0.1333, "value": -2.21, "curve": [0.3, -2.21, 0.467, 6.12]}, {"time": 0.6333, "value": 6.12, "curve": [0.756, 6.12, 0.879, 1.66]}, {"time": 1, "value": -0.72}]}, "fly3": {"rotate": [{"value": 2.37, "curve": [0.09, 0.17, 0.178, -2.21]}, {"time": 0.2667, "value": -2.21, "curve": [0.433, -2.21, 0.6, 6.12]}, {"time": 0.7667, "value": 6.12, "curve": [0.845, 6.12, 0.923, 4.32]}, {"time": 1, "value": 2.37}]}, "fly2-R": {"rotate": [{"value": 4.28, "curve": [0.046, 5.66, 0.089, 6.69]}, {"time": 0.1333, "value": 6.69, "curve": [0.3, 6.69, 0.467, -6.79]}, {"time": 0.6333, "value": -6.79, "curve": [0.756, -6.79, 0.879, 0.43]}, {"time": 1, "value": 4.28}]}, "fly5": {"rotate": [{"value": 3.28, "curve": [0.09, 4.92, 0.178, 6.69]}, {"time": 0.2667, "value": 6.69, "curve": [0.433, 6.69, 0.6, 0.48]}, {"time": 0.7667, "value": 0.48, "curve": [0.845, 0.48, 0.923, 1.82]}, {"time": 1, "value": 3.28}]}, "fly-L": {"rotate": [{"value": -0.52, "curve": [0.023, -0.23, 0.045, 0]}, {"time": 0.0667, "curve": [0.233, 0, 0.4, -9.8]}, {"time": 0.5667, "value": -9.8, "curve": [0.712, -9.8, 0.857, -2.45]}, {"time": 1, "value": -0.52}]}, "fly-L2": {"rotate": [{"value": -2.08, "curve": [0.046, -0.89, 0.089, 0]}, {"time": 0.1333, "curve": [0.3, 0, 0.467, -11.65]}, {"time": 0.6333, "value": -11.65, "curve": [0.756, -11.65, 0.879, -5.41]}, {"time": 1, "value": -2.08}]}, "fly-R": {"rotate": [{"value": 0.81, "curve": [0.023, 0.37, 0.045, 0]}, {"time": 0.0667, "curve": [0.233, 0, 0.4, 15.27]}, {"time": 0.5667, "value": 15.27, "curve": [0.712, 15.27, 0.857, 3.82]}, {"time": 1, "value": 0.81}]}, "fly-L4": {"rotate": [{"value": 1.36, "curve": [0.046, 0.58, 0.089, 0]}, {"time": 0.1333, "curve": [0.3, 0, 0.467, 7.61]}, {"time": 0.6333, "value": 7.61, "curve": [0.756, 7.61, 0.879, 3.53]}, {"time": 1, "value": 1.36}]}, "hand-L": {"rotate": [{"curve": [0.167, 0, 0.333, -5.28]}, {"time": 0.5, "value": -5.28, "curve": [0.667, -5.28, 0.833, 0]}, {"time": 1}]}, "hand-R": {"rotate": [{"curve": [0.167, 0, 0.333, 3.97]}, {"time": 0.5, "value": 3.97, "curve": [0.667, 3.97, 0.833, 0]}, {"time": 1}]}, "leg-L": {"rotate": [{"value": -1.35, "curve": [0.167, -1.35, 0.333, 0.59]}, {"time": 0.5, "value": 0.59, "curve": [0.667, 0.59, 0.833, -1.35]}, {"time": 1, "value": -1.35}]}, "leg-R": {"rotate": [{"value": 2.43, "curve": [0.167, 2.43, 0.333, 0.94]}, {"time": 0.5, "value": 0.94, "curve": [0.667, 0.94, 0.833, 2.43]}, {"time": 1, "value": 2.43}]}, "body": {"translate": [{"curve": [0.167, 0, 0.333, 0, 0.167, 0, 0.333, -1.29]}, {"time": 0.5, "y": -1.29, "curve": [0.667, 0, 0.833, 0, 0.667, -1.29, 0.833, 0]}, {"time": 1}], "scale": [{"curve": [0.167, 1, 0.333, 1, 0.167, 1, 0.333, 0.974]}, {"time": 0.5, "y": 0.974, "curve": [0.667, 1, 0.833, 1, 0.667, 0.974, 0.833, 1]}, {"time": 1}]}, "eye": {"scale": [{"time": 0.2}, {"time": 0.3333, "y": 0}, {"time": 0.4667}]}}}}}