{"skeleton": {"hash": "4oKMbRK49hE", "spine": "4.1.11", "x": -243, "y": -2.79, "width": 486, "height": 457, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/RMW_<PERSON>_ani/RMW_<PERSON>_03_ani"}, "bones": [{"name": "root"}, {"name": "all", "parent": "root"}, {"name": "fly-L", "parent": "all", "length": 117.37, "rotation": 144.29, "x": -32.94, "y": 218.43, "color": "ff19edff"}, {"name": "fly-L2", "parent": "fly-L", "length": 78.12, "rotation": 0.12, "x": 117.37, "color": "ff19edff"}, {"name": "fly-L3", "parent": "fly-L2", "length": 58.91, "rotation": 2.24, "x": 78.12, "color": "ff19edff"}, {"name": "toushi", "parent": "all", "length": 65.04, "rotation": 90.97, "x": -1.15, "y": 103.64, "color": "f38dffff"}, {"name": "leg-L", "parent": "all", "length": 108.56, "rotation": -105.31, "x": -65.08, "y": 217.16, "color": "ffdc0eff"}, {"name": "leg2", "parent": "leg-L", "length": 59.61, "rotation": 34.75, "x": 108.56, "color": "ffdc0eff"}, {"name": "leg3", "parent": "leg2", "length": 52.27, "rotation": 5.5, "x": 59.61, "color": "ffdc0eff"}, {"name": "soldier", "parent": "all", "x": -2.25, "y": 184.1, "color": "3fff3fff"}, {"name": "soldier-dun<PERSON>i", "parent": "soldier", "length": 79.14, "rotation": -77.12, "x": 106.91, "y": 55.11, "color": "3fff3fff"}, {"name": "soldier-jian", "parent": "soldier", "length": 171.13, "rotation": -108.79, "x": -116.83, "y": 50.7, "color": "3fff3fff"}, {"name": "fly-R", "parent": "all", "length": 117.37, "rotation": -144.29, "x": 32.94, "y": 218.43, "scaleX": -1, "color": "ff19edff"}, {"name": "fly-L5", "parent": "fly-R", "length": 78.12, "rotation": 0.12, "x": 117.37, "color": "ff19edff"}, {"name": "fly-L6", "parent": "fly-L5", "length": 58.91, "rotation": 2.24, "x": 78.12, "color": "ff19edff"}, {"name": "leg-R", "parent": "all", "length": 108.56, "rotation": 105.31, "x": 65.08, "y": 217.16, "scaleX": -1, "color": "ffdc0eff"}, {"name": "leg5", "parent": "leg-R", "length": 59.61, "rotation": 34.75, "x": 108.56, "color": "ffdc0eff"}, {"name": "leg6", "parent": "leg5", "length": 52.27, "rotation": 5.5, "x": 59.61, "color": "ffdc0eff"}, {"name": "trail-1", "parent": "root", "length": 33.71, "rotation": 70.2, "x": -8.9, "y": 305.66, "color": "2de5ffff"}, {"name": "trail-2", "parent": "trail-1", "length": 17.23, "rotation": 26.14, "x": 33.71, "color": "2de5ffff"}, {"name": "trail-3", "parent": "trail-2", "length": 19.46, "rotation": 12.69, "x": 19.19, "y": 0.42, "color": "2de5ffff"}, {"name": "trail-4", "parent": "trail-3", "length": 18.86, "rotation": 0.63, "x": 20.06, "y": -0.21, "color": "2de5ffff"}, {"name": "trail-5", "parent": "trail-4", "length": 20.46, "rotation": -26.78, "x": 18.86, "color": "2de5ffff"}, {"name": "trail-6", "parent": "trail-5", "length": 20.27, "rotation": -13.01, "x": 20.46, "color": "2de5ffff"}, {"name": "trail-7", "parent": "trail-6", "length": 23.54, "rotation": 6.1, "x": -13.59, "y": -6.55, "color": "2de5ffff"}, {"name": "face", "parent": "all", "length": 72.93, "rotation": 90, "x": -1.75, "y": 43.53}], "slots": [{"name": "trail-2", "bone": "trail-7", "attachment": "trail-2"}, {"name": "trail-1", "bone": "trail-1", "attachment": "trail-1"}, {"name": "body", "bone": "all", "attachment": "body"}, {"name": "fly-L", "bone": "fly-L", "attachment": "fly-L"}, {"name": "fly-L2", "bone": "fly-R", "attachment": "fly-L"}, {"name": "soldier", "bone": "soldier", "attachment": "soldier"}, {"name": "soldier-jian", "bone": "soldier-jian", "attachment": "soldier-jian"}, {"name": "soldier-dun<PERSON>i", "bone": "soldier-dun<PERSON>i", "attachment": "soldier-dun<PERSON>i"}, {"name": "leg", "bone": "leg-L", "attachment": "leg"}, {"name": "leg2", "bone": "leg-R", "attachment": "leg"}, {"name": "face", "bone": "face", "attachment": "face"}, {"name": "toushi", "bone": "toushi", "attachment": "toushi"}], "skins": [{"name": "default", "attachments": {"body": {"body": {"x": -1, "y": 270.71, "width": 224, "height": 189}}, "face": {"face": {"x": 131.18, "y": -1.25, "rotation": -90, "width": 199, "height": 265}}, "fly-L": {"fly-L": {"type": "mesh", "uvs": [0.46125, 0.05314, 0.55675, 0.10821, 0.71332, 0.17745, 0.81663, 0.26515, 0.92094, 0.42302, 0.99589, 0.62406, 0.99573, 0.81037, 0.96248, 0.87505, 0.90585, 0.94564, 0.73686, 1, 0.61974, 1, 0.50359, 0.96905, 0.3429, 0.93625, 0.18751, 0.87174, 0.09433, 0.75505, 0.07314, 0.61347, 0.0211, 0.54042, 0, 0.40941, 0.05906, 0.33485, 0.00583, 0.23555, 0.00191, 0.15567, 0.10408, 0.00414, 0.21815, 0, 0.33592, 0.0041, 0.94462, 0.7471, 0.89146, 0.82186, 0.81169, 0.89168, 0.8349, 0.63025, 0.76471, 0.72497, 0.61123, 0.81246, 0.63233, 0.4025, 0.56108, 0.56377, 0.39812, 0.71145, 0.49518, 0.26782, 0.40717, 0.45129, 0.2483, 0.64214, 0.34114, 0.14701, 0.28225, 0.34785, 0.16812, 0.5035], "triangles": [20, 37, 19, 19, 37, 18, 21, 22, 20, 36, 22, 23, 36, 20, 22, 36, 23, 0, 1, 36, 0, 33, 1, 2, 33, 36, 1, 37, 36, 33, 20, 36, 37, 30, 33, 2, 34, 37, 33, 34, 33, 30, 38, 18, 37, 38, 37, 34, 17, 18, 38, 16, 17, 38, 15, 16, 38, 35, 38, 34, 32, 35, 34, 15, 38, 35, 31, 32, 34, 14, 15, 35, 13, 14, 35, 13, 35, 32, 12, 13, 32, 30, 2, 3, 30, 3, 4, 31, 34, 30, 27, 30, 4, 27, 4, 5, 31, 30, 27, 28, 31, 27, 24, 27, 5, 28, 27, 24, 6, 24, 5, 29, 31, 28, 32, 31, 29, 25, 28, 24, 25, 24, 6, 7, 25, 6, 26, 28, 25, 29, 28, 26, 12, 32, 29, 8, 25, 7, 26, 25, 8, 11, 12, 29, 10, 29, 26, 11, 29, 10, 9, 10, 26, 9, 26, 8], "vertices": [3, 2, 189.45, -72.75, 0.05729, 3, 71.92, -72.91, 0.5925, 4, -9.04, -72.61, 0.35022, 3, 2, 165.53, -74.49, 0.10378, 3, 48, -74.59, 0.78326, 4, -33.02, -73.36, 0.11295, 3, 2, 129.11, -81.23, 0.38223, 3, 11.56, -81.25, 0.61199, 4, -69.68, -78.59, 0.00579, 2, 2, 99.48, -77.9, 0.63352, 3, -18.05, -77.86, 0.36648, 2, 2, 60.35, -61.7, 0.92756, 3, -57.15, -61.58, 0.07244, 1, 2, 20.57, -33.85, 1, 1, 2, -4.2, 0.66, 1, 1, 2, -7.03, 16.79, 1, 2, 2, -6.59, 36.93, 0.99998, 3, -123.88, 37.2, 2e-05, 2, 2, 15.54, 68.1, 0.9843, 3, -101.68, 68.32, 0.0157, 2, 2, 35.89, 82.73, 0.94308, 3, -81.3, 82.91, 0.05692, 2, 2, 60.19, 91.52, 0.81292, 3, -56.98, 91.64, 0.18708, 2, 2, 92.48, 105.52, 0.57814, 3, -24.66, 105.57, 0.42186, 3, 2, 128.06, 112.99, 0.34183, 3, 10.94, 112.96, 0.65306, 4, -62.73, 115.5, 0.00511, 3, 2, 159.78, 103.02, 0.19929, 3, 42.64, 102.93, 0.76907, 4, -31.44, 104.24, 0.03164, 3, 2, 182.31, 79.46, 0.0721, 3, 65.11, 79.32, 0.78944, 4, -9.91, 79.77, 0.13846, 3, 2, 201.07, 72.44, 0.02058, 3, 83.86, 72.26, 0.70401, 4, 8.55, 71.98, 0.27542, 3, 2, 222.18, 50.82, 0.00048, 3, 104.92, 50.6, 0.49212, 4, 28.75, 49.51, 0.5074, 2, 3, 104.53, 29.42, 0.2287, 4, 27.54, 28.36, 0.7713, 2, 3, 126.97, 17.64, 0.00453, 4, 49.5, 15.72, 0.99547, 1, 4, 60.21, 0.96, 1, 2, 3, 140.58, -37.51, 0.02544, 4, 60.95, -39.92, 0.97456, 3, 2, 238.76, -52.22, 0.00022, 3, 121.28, -52.48, 0.19959, 4, 41.08, -54.13, 0.80019, 3, 2, 217.75, -66.17, 0.00042, 3, 100.24, -66.39, 0.35915, 4, 19.51, -67.2, 0.64043, 1, 2, 13.1, -4.67, 1, 1, 2, 12.39, 15.81, 1, 2, 2, 16.96, 38.7, 0.99725, 3, -100.33, 38.92, 0.00275, 1, 2, 47.72, -12.59, 1, 2, 2, 47.31, 13.71, 0.99903, 3, -70.03, 13.86, 0.00097, 2, 2, 62.33, 49.08, 0.9197, 3, -54.93, 49.2, 0.0803, 2, 2, 113.23, -29.45, 0.58545, 3, -4.21, -29.44, 0.41455, 2, 2, 104.14, 9.31, 0.96487, 3, -13.21, 9.34, 0.03513, 3, 2, 112.8, 57, 0.49596, 3, -4.44, 57.01, 0.50368, 4, -80.28, 60.19, 0.00036, 3, 2, 154.98, -37.25, 0.0793, 3, 37.53, -37.33, 0.88661, 4, -42.02, -35.72, 0.03409, 2, 2, 145.86, 7.71, 0.0013, 3, 28.5, 7.65, 0.9987, 3, 2, 148.06, 62.89, 0.17396, 3, 30.83, 62.82, 0.79619, 4, -44.81, 64.62, 0.02985, 3, 2, 197.83, -40.37, 0.00287, 3, 80.37, -40.54, 0.52745, 4, 0.66, -40.6, 0.46968, 1, 3, 63.97, 4.03, 1, 3, 2, 180.44, 47.24, 0.02266, 3, 63.18, 47.1, 0.79587, 4, -13.1, 47.65, 0.18146], "hull": 24, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 24, 26, 26, 28, 28, 30, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 30, 32, 12, 14, 14, 16, 10, 48, 14, 50, 48, 50, 50, 52, 52, 20, 8, 54, 56, 50, 54, 56, 56, 58, 58, 24, 22, 24, 4, 60, 62, 56, 60, 62, 62, 64, 64, 26, 2, 66, 68, 62, 66, 68, 68, 70, 70, 28, 46, 72, 40, 74, 74, 68, 72, 74, 74, 76, 76, 30, 42, 44, 44, 46, 2, 0, 0, 46], "width": 214, "height": 228}}, "fly-L2": {"fly-L": {"type": "mesh", "uvs": [0.46125, 0.05314, 0.55675, 0.10821, 0.71332, 0.17745, 0.81663, 0.26515, 0.92094, 0.42302, 0.99589, 0.62406, 0.99573, 0.81037, 0.96248, 0.87505, 0.90585, 0.94564, 0.73686, 1, 0.61974, 1, 0.50359, 0.96905, 0.3429, 0.93625, 0.18751, 0.87174, 0.09433, 0.75505, 0.07314, 0.61347, 0.0211, 0.54042, 0, 0.40941, 0.05906, 0.33485, 0.00583, 0.23555, 0.00191, 0.15567, 0.10408, 0.00414, 0.21815, 0, 0.33592, 0.0041, 0.94462, 0.7471, 0.89146, 0.82186, 0.81169, 0.89168, 0.8349, 0.63025, 0.76471, 0.72497, 0.61123, 0.81246, 0.63233, 0.4025, 0.56108, 0.56377, 0.39812, 0.71145, 0.49518, 0.26782, 0.40717, 0.45129, 0.2483, 0.64214, 0.34114, 0.14701, 0.28225, 0.34785, 0.16812, 0.5035], "triangles": [20, 37, 19, 19, 37, 18, 21, 22, 20, 36, 22, 23, 36, 20, 22, 36, 23, 0, 1, 36, 0, 33, 1, 2, 33, 36, 1, 37, 36, 33, 20, 36, 37, 30, 33, 2, 34, 37, 33, 34, 33, 30, 38, 18, 37, 38, 37, 34, 17, 18, 38, 16, 17, 38, 15, 16, 38, 35, 38, 34, 32, 35, 34, 15, 38, 35, 31, 32, 34, 14, 15, 35, 13, 14, 35, 13, 35, 32, 12, 13, 32, 30, 2, 3, 30, 3, 4, 31, 34, 30, 27, 30, 4, 27, 4, 5, 31, 30, 27, 28, 31, 27, 24, 27, 5, 28, 27, 24, 6, 24, 5, 29, 31, 28, 32, 31, 29, 25, 28, 24, 25, 24, 6, 7, 25, 6, 26, 28, 25, 29, 28, 26, 12, 32, 29, 8, 25, 7, 26, 25, 8, 11, 12, 29, 10, 29, 26, 11, 29, 10, 9, 10, 26, 9, 26, 8], "vertices": [3, 12, 189.45, -72.75, 0.05729, 13, 71.92, -72.91, 0.5925, 14, -9.04, -72.61, 0.35022, 3, 12, 165.53, -74.49, 0.10378, 13, 48, -74.59, 0.78326, 14, -33.02, -73.36, 0.11295, 3, 12, 129.11, -81.23, 0.38223, 13, 11.56, -81.25, 0.61199, 14, -69.68, -78.59, 0.00579, 2, 12, 99.48, -77.9, 0.63352, 13, -18.05, -77.86, 0.36648, 2, 12, 60.35, -61.7, 0.92756, 13, -57.15, -61.58, 0.07244, 1, 12, 20.57, -33.85, 1, 1, 12, -4.2, 0.66, 1, 1, 12, -7.03, 16.79, 1, 2, 12, -6.59, 36.93, 0.99998, 13, -123.88, 37.2, 2e-05, 2, 12, 15.54, 68.1, 0.9843, 13, -101.68, 68.32, 0.0157, 2, 12, 35.89, 82.73, 0.94308, 13, -81.3, 82.91, 0.05692, 2, 12, 60.19, 91.52, 0.81292, 13, -56.98, 91.64, 0.18708, 2, 12, 92.48, 105.52, 0.57814, 13, -24.66, 105.57, 0.42186, 3, 12, 128.06, 112.99, 0.34183, 13, 10.94, 112.96, 0.65306, 14, -62.73, 115.5, 0.00511, 3, 12, 159.78, 103.02, 0.19929, 13, 42.64, 102.93, 0.76907, 14, -31.44, 104.24, 0.03164, 3, 12, 182.31, 79.46, 0.0721, 13, 65.11, 79.32, 0.78944, 14, -9.91, 79.77, 0.13846, 3, 12, 201.07, 72.44, 0.02058, 13, 83.86, 72.26, 0.70401, 14, 8.55, 71.98, 0.27542, 3, 12, 222.18, 50.82, 0.00048, 13, 104.92, 50.6, 0.49212, 14, 28.75, 49.51, 0.5074, 2, 13, 104.53, 29.42, 0.2287, 14, 27.54, 28.36, 0.7713, 2, 13, 126.97, 17.64, 0.00453, 14, 49.5, 15.72, 0.99547, 1, 14, 60.21, 0.96, 1, 2, 13, 140.58, -37.51, 0.02544, 14, 60.95, -39.92, 0.97456, 3, 12, 238.76, -52.22, 0.00022, 13, 121.28, -52.48, 0.19959, 14, 41.08, -54.13, 0.80019, 3, 12, 217.75, -66.17, 0.00042, 13, 100.24, -66.39, 0.35915, 14, 19.51, -67.2, 0.64043, 1, 12, 13.1, -4.67, 1, 1, 12, 12.39, 15.81, 1, 2, 12, 16.96, 38.7, 0.99725, 13, -100.33, 38.92, 0.00275, 1, 12, 47.72, -12.59, 1, 2, 12, 47.31, 13.71, 0.99903, 13, -70.03, 13.86, 0.00097, 2, 12, 62.33, 49.08, 0.9197, 13, -54.93, 49.2, 0.0803, 2, 12, 113.23, -29.45, 0.58545, 13, -4.21, -29.44, 0.41455, 2, 12, 104.14, 9.31, 0.96487, 13, -13.21, 9.34, 0.03513, 3, 12, 112.8, 57, 0.49596, 13, -4.44, 57.01, 0.50368, 14, -80.28, 60.19, 0.00036, 3, 12, 154.98, -37.25, 0.0793, 13, 37.53, -37.33, 0.88661, 14, -42.02, -35.72, 0.03409, 2, 12, 145.86, 7.71, 0.0013, 13, 28.5, 7.65, 0.9987, 3, 12, 148.06, 62.89, 0.17396, 13, 30.83, 62.82, 0.79619, 14, -44.81, 64.62, 0.02985, 3, 12, 197.83, -40.37, 0.00287, 13, 80.37, -40.54, 0.52745, 14, 0.66, -40.6, 0.46968, 1, 13, 63.97, 4.03, 1, 3, 12, 180.44, 47.24, 0.02266, 13, 63.18, 47.1, 0.79587, 14, -13.1, 47.65, 0.18146], "hull": 24, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 24, 26, 26, 28, 28, 30, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 30, 32, 12, 14, 14, 16, 10, 48, 14, 50, 48, 50, 50, 52, 52, 20, 8, 54, 56, 50, 54, 56, 56, 58, 58, 24, 22, 24, 4, 60, 62, 56, 60, 62, 62, 64, 64, 26, 2, 66, 68, 62, 66, 68, 68, 70, 70, 28, 46, 72, 40, 74, 74, 68, 72, 74, 74, 76, 76, 30, 42, 44, 44, 46, 2, 0, 0, 46], "width": 214, "height": 228}}, "leg": {"leg": {"type": "mesh", "uvs": [0.61664, 0, 0.76669, 0.03951, 0.86584, 0.11036, 0.89105, 0.21617, 0.83493, 0.37008, 0.70612, 0.48502, 0.75845, 0.55785, 0.84663, 0.66831, 0.94592, 0.80541, 1, 0.88361, 0.99999, 0.89808, 0.99567, 0.9207, 0.91734, 0.95631, 0.71184, 1, 0.47049, 1, 0.26231, 0.96147, 0.11625, 0.79811, 0.01397, 0.65398, 0, 0.57098, 0.00863, 0.49857, 0.0785, 0.34586, 0.17864, 0.1484, 0.26546, 0.00427, 0.32622, 0.53104, 0.30423, 0.4929, 0.61671, 0.87655, 0.49489, 0.73021, 0.39149, 0.60601, 0.36021, 0.36067, 0.44869, 0.17702], "triangles": [14, 25, 13, 13, 25, 12, 14, 15, 25, 25, 16, 26, 25, 15, 16, 11, 12, 10, 9, 10, 12, 9, 12, 25, 25, 8, 9, 25, 26, 8, 26, 7, 8, 16, 27, 26, 16, 17, 27, 26, 6, 7, 26, 27, 6, 27, 18, 23, 27, 17, 18, 27, 5, 6, 27, 23, 5, 23, 19, 24, 23, 18, 19, 23, 24, 5, 19, 20, 24, 24, 28, 5, 24, 20, 28, 5, 28, 4, 28, 29, 4, 4, 29, 3, 20, 21, 28, 28, 21, 29, 29, 2, 3, 21, 22, 29, 29, 1, 2, 29, 0, 1, 29, 22, 0], "vertices": [1, 6, -5.13, 3.44, 1, 1, 6, -0.88, 21.41, 1, 1, 6, 11.61, 35.92, 1, 2, 6, 33.75, 44.81, 0.99576, 7, -35.93, 79.45, 0.00424, 2, 6, 68.6, 48.06, 0.89637, 7, -5.44, 62.26, 0.10363, 3, 6, 97.11, 41.44, 0.46191, 7, 14.21, 40.58, 0.52317, 8, -41.3, 44.74, 0.01491, 3, 6, 111.35, 51.2, 0.12817, 7, 31.48, 40.48, 0.75482, 8, -24.12, 42.99, 0.11701, 3, 6, 132.7, 66.92, 0.00497, 7, 57.98, 41.22, 0.43674, 8, 2.33, 41.19, 0.55829, 2, 7, 90.51, 41.11, 0.02603, 8, 34.7, 37.96, 0.97397, 2, 7, 108.97, 40.79, 0.00017, 8, 53.04, 35.87, 0.99983, 1, 8, 55.98, 34.5, 1, 1, 8, 60.38, 31.94, 1, 1, 8, 64.05, 20.91, 1, 1, 8, 63.56, -3.34, 1, 1, 8, 52.57, -26.98, 1, 2, 7, 98.9, -40.14, 0.02426, 8, 35.26, -43.72, 0.97574, 2, 7, 59.14, -42.84, 0.51852, 8, -4.57, -42.6, 0.48148, 3, 6, 153.34, -20.67, 0.01848, 7, 25.02, -42.51, 0.9399, 8, -38.5, -39, 0.04162, 3, 6, 135.81, -27.03, 0.16174, 7, 6.99, -37.74, 0.83642, 8, -56, -32.52, 0.00185, 2, 6, 119.92, -30.41, 0.48103, 7, -8, -31.47, 0.51897, 2, 6, 84.93, -32.17, 0.99823, 7, -37.74, -12.97, 0.00177, 1, 6, 39.42, -33.41, 1, 1, 6, 5.8, -32.89, 1, 1, 7, 10.28, -1.54, 1, 2, 6, 110.27, 0.04, 0.15616, 7, 1.43, -0.94, 0.84384, 1, 8, 34.15, -1, 1, 2, 7, 58.41, 0.79, 0.76632, 8, -1.12, 0.9, 0.23368, 1, 7, 28.46, -0.48, 1, 1, 6, 80.1, -1.95, 1, 1, 6, 37.9, -3.59, 1], "hull": 23, "edges": [0, 44, 4, 6, 6, 8, 8, 10, 10, 12, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 12, 14, 14, 16, 16, 18, 30, 32, 0, 2, 2, 4, 46, 48, 26, 50, 16, 50, 50, 30, 52, 50, 32, 52, 52, 14, 46, 54, 54, 52, 12, 54, 54, 34, 10, 46, 46, 36, 38, 48, 48, 10, 56, 48, 40, 56, 56, 8, 0, 58, 58, 56, 42, 58, 58, 6], "width": 108, "height": 224}}, "leg2": {"leg": {"type": "mesh", "uvs": [0.61664, 0, 0.76669, 0.03951, 0.86584, 0.11036, 0.89105, 0.21617, 0.83493, 0.37008, 0.70612, 0.48502, 0.75845, 0.55785, 0.84663, 0.66831, 0.94592, 0.80541, 1, 0.88361, 0.99999, 0.89808, 0.99567, 0.9207, 0.91734, 0.95631, 0.71184, 1, 0.47049, 1, 0.26231, 0.96147, 0.11625, 0.79811, 0.01397, 0.65398, 0, 0.57098, 0.00863, 0.49857, 0.0785, 0.34586, 0.17864, 0.1484, 0.26546, 0.00427, 0.32622, 0.53104, 0.30423, 0.4929, 0.61671, 0.87655, 0.49489, 0.73021, 0.39149, 0.60601, 0.36021, 0.36067, 0.44869, 0.17702], "triangles": [14, 25, 13, 13, 25, 12, 14, 15, 25, 25, 16, 26, 25, 15, 16, 11, 12, 10, 9, 10, 12, 9, 12, 25, 25, 8, 9, 25, 26, 8, 26, 7, 8, 16, 27, 26, 16, 17, 27, 26, 6, 7, 26, 27, 6, 27, 18, 23, 27, 17, 18, 27, 5, 6, 27, 23, 5, 23, 19, 24, 23, 18, 19, 23, 24, 5, 19, 20, 24, 24, 28, 5, 24, 20, 28, 5, 28, 4, 28, 29, 4, 4, 29, 3, 20, 21, 28, 28, 21, 29, 29, 2, 3, 21, 22, 29, 29, 1, 2, 29, 0, 1, 29, 22, 0], "vertices": [1, 15, -5.13, 3.44, 1, 1, 15, -0.88, 21.41, 1, 1, 15, 11.61, 35.92, 1, 2, 15, 33.75, 44.81, 0.99576, 16, -35.93, 79.45, 0.00424, 2, 15, 68.6, 48.06, 0.89637, 16, -5.44, 62.26, 0.10363, 3, 15, 97.11, 41.44, 0.46191, 16, 14.21, 40.58, 0.52317, 17, -41.3, 44.74, 0.01491, 3, 15, 111.35, 51.2, 0.12817, 16, 31.48, 40.48, 0.75482, 17, -24.12, 42.99, 0.11701, 3, 15, 132.7, 66.92, 0.00497, 16, 57.98, 41.22, 0.43674, 17, 2.33, 41.19, 0.55829, 2, 16, 90.51, 41.11, 0.02603, 17, 34.7, 37.96, 0.97397, 2, 16, 108.97, 40.79, 0.00017, 17, 53.04, 35.87, 0.99983, 1, 17, 55.98, 34.5, 1, 1, 17, 60.38, 31.94, 1, 1, 17, 64.05, 20.91, 1, 1, 17, 63.56, -3.34, 1, 1, 17, 52.57, -26.98, 1, 2, 16, 98.9, -40.14, 0.02426, 17, 35.26, -43.72, 0.97574, 2, 16, 59.14, -42.84, 0.51852, 17, -4.57, -42.6, 0.48148, 3, 15, 153.34, -20.67, 0.01848, 16, 25.02, -42.51, 0.9399, 17, -38.5, -39, 0.04162, 3, 15, 135.81, -27.03, 0.16174, 16, 6.99, -37.74, 0.83642, 17, -56, -32.52, 0.00185, 2, 15, 119.92, -30.41, 0.48103, 16, -8, -31.47, 0.51897, 2, 15, 84.93, -32.17, 0.99823, 16, -37.74, -12.97, 0.00177, 1, 15, 39.42, -33.41, 1, 1, 15, 5.8, -32.89, 1, 1, 16, 10.28, -1.54, 1, 2, 15, 110.27, 0.04, 0.15616, 16, 1.43, -0.94, 0.84384, 1, 17, 34.15, -1, 1, 2, 16, 58.41, 0.79, 0.76632, 17, -1.12, 0.9, 0.23368, 1, 16, 28.46, -0.48, 1, 1, 15, 80.1, -1.95, 1, 1, 15, 37.9, -3.59, 1], "hull": 23, "edges": [0, 44, 4, 6, 6, 8, 8, 10, 10, 12, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 12, 14, 14, 16, 16, 18, 30, 32, 0, 2, 2, 4, 46, 48, 26, 50, 16, 50, 50, 30, 52, 50, 32, 52, 52, 14, 46, 54, 54, 52, 12, 54, 54, 34, 10, 46, 46, 36, 38, 48, 48, 10, 56, 48, 40, 56, 56, 8, 0, 58, 58, 56, 42, 58, 58, 6], "width": 108, "height": 224}}, "soldier": {"soldier": {"x": 2.25, "y": 113.11, "width": 261, "height": 265}}, "soldier-dunpai": {"soldier-dunpai": {"x": 46.74, "y": -4.18, "rotation": 77.12, "width": 108, "height": 97}}, "soldier-jian": {"soldier-jian": {"x": 100.24, "y": 11.47, "rotation": 108.79, "width": 101, "height": 212}}, "toushi": {"toushi": {"x": 61.05, "y": -1.69, "rotation": -90.97, "width": 165, "height": 127}}, "trail-1": {"trail-1": {"type": "mesh", "uvs": [0.48946, 0.00713, 0.6833, 0.00699, 0.72795, 0.04059, 0.61326, 0.14667, 0.55605, 0.23042, 0.5566, 0.30985, 0.62413, 0.38381, 0.91434, 0.58107, 0.97836, 0.66451, 1, 0.76104, 0.98807, 0.88583, 0.89254, 0.99261, 0.44433, 0.99271, 0.02197, 0.9928, 0.02079, 0.93245, 0.15131, 0.87584, 0.31945, 0.76255, 0.35521, 0.66913, 0.23164, 0.51595, 0.11086, 0.36623, 0.1106, 0.22477, 0.24483, 0.09002, 0.4057, 0.1184, 0.3139, 0.22629, 0.29941, 0.34236, 0.41054, 0.44698, 0.64728, 0.61863, 0.70043, 0.67748, 0.69077, 0.76903, 0.57964, 0.89], "triangles": [3, 4, 22, 22, 23, 21, 3, 22, 2, 2, 22, 1, 22, 0, 1, 22, 21, 0, 5, 23, 4, 5, 24, 23, 24, 20, 23, 4, 23, 22, 23, 20, 21, 25, 6, 7, 18, 19, 25, 19, 24, 25, 25, 24, 6, 24, 5, 6, 19, 20, 24, 18, 25, 26, 28, 17, 27, 27, 8, 9, 17, 26, 27, 8, 27, 7, 17, 18, 26, 27, 26, 7, 26, 25, 7, 13, 14, 12, 14, 15, 12, 12, 29, 11, 12, 15, 29, 11, 29, 10, 15, 16, 29, 29, 28, 10, 29, 16, 28, 10, 28, 9, 16, 17, 28, 28, 27, 9], "vertices": [1, 23, 21.53, 4.49, 1, 1, 23, 24.55, -3.69, 1, 1, 23, 21.05, -7.11, 1, 2, 22, 24.72, -8.3, 0.09999, 23, 6.03, -7.13, 0.90001, 3, 21, 27.57, -12.38, 0.00458, 22, 13.35, -7.13, 0.95829, 23, -5.32, -8.54, 0.03713, 2, 21, 17.61, -8.85, 0.44381, 22, 2.87, -8.46, 0.55619, 2, 21, 7.32, -8.4, 0.98082, 22, -6.51, -12.7, 0.01918, 3, 19, 20.35, -11.95, 0.65456, 20, -1.59, -12.32, 0.34245, 21, -21.78, -11.88, 0.00299, 3, 18, 47.77, -8.23, 0.01957, 19, 9, -13.59, 0.97684, 20, -13.02, -11.43, 0.0036, 2, 18, 36.02, -13.49, 0.57729, 19, -3.87, -13.14, 0.42271, 2, 18, 20.23, -18.61, 0.99891, 19, -20.3, -10.77, 0.00109, 1, 18, 5.41, -19.38, 1, 1, 18, -1.44, -0.4, 1, 1, 18, -7.89, 17.47, 1, 1, 18, -0.35, 20.24, 1, 2, 18, 8.72, 17.27, 0.99828, 19, -14.82, 26.51, 0.00172, 3, 18, 25.46, 15.25, 0.74643, 19, -0.68, 17.32, 0.22993, 20, -15.68, 20.86, 0.02365, 3, 18, 37.7, 17.95, 0.13293, 19, 11.49, 14.35, 0.51183, 20, -4.46, 15.28, 0.35524, 2, 20, 16.62, 13.9, 0.91021, 21, -3.29, 14.14, 0.08979, 3, 20, 37.21, 12.55, 0.05628, 21, 17.29, 12.56, 0.75769, 22, -7.06, 10.51, 0.18604, 3, 21, 35.02, 6.25, 0.00099, 22, 11.61, 12.86, 0.98247, 23, -11.51, 10.54, 0.01654, 2, 22, 30.14, 9.09, 0.06626, 23, 7.39, 11.03, 0.93374, 2, 22, 27.29, 1.43, 0.00473, 23, 6.34, 2.94, 0.99527, 2, 22, 12.54, 3.75, 0.99667, 23, -8.55, 1.88, 0.00333, 3, 20, 37.45, 3.49, 0.01018, 21, 17.43, 3.51, 0.80694, 22, -2.86, 2.49, 0.18288, 2, 20, 22.66, 3.3, 0.40296, 21, 2.64, 3.48, 0.59704, 2, 18, 48.47, 7.85, 0.00015, 19, 16.71, 0.55, 0.99985, 1, 19, 8.67, -0.96, 1, 1, 18, 30.31, -0.76, 1, 1, 18, 13.48, -1.51, 1], "hull": 22, "edges": [0, 42, 0, 2, 2, 4, 12, 14, 14, 16, 20, 22, 26, 28, 28, 30, 38, 40, 40, 42, 16, 18, 18, 20, 30, 32, 32, 34, 34, 36, 36, 38, 8, 10, 10, 12, 4, 6, 6, 8, 22, 24, 24, 26, 2, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 24], "width": 45, "height": 133}}, "trail-2": {"trail-2": {"x": 24.89, "y": 4.48, "rotation": -75.96, "width": 54, "height": 61}}}}], "animations": {"animation": {"bones": {"fly-L": {"rotate": [{"value": -4.81, "curve": [0.058, -5.41, 0.112, -5.87]}, {"time": 0.1667, "value": -5.87, "curve": [0.5, -5.87, 0.833, 7.55]}, {"time": 1.1667, "value": 7.55, "curve": [1.446, 7.55, 1.724, -1.75]}, {"time": 2, "value": -4.81}]}, "fly-L2": {"rotate": [{"value": -4.31, "curve": [0.113, -7.6, 0.223, -10.2]}, {"time": 0.3333, "value": -10.2, "curve": [0.667, -10.2, 1, 12.32]}, {"time": 1.3333, "value": 12.32, "curve": [1.557, 12.32, 1.78, 2.36]}, {"time": 2, "value": -4.31}]}, "fly-L3": {"rotate": [{"value": -1.63, "curve": [0.169, -5.77, 0.334, -9.95]}, {"time": 0.5, "value": -9.95, "curve": [0.833, -9.95, 1.167, 6.69]}, {"time": 1.5, "value": 6.69, "curve": [1.668, 6.69, 1.836, 2.55]}, {"time": 2, "value": -1.63}]}, "fly-R": {"rotate": [{"value": 3.6, "curve": [0.058, 4.34, 0.112, 4.91]}, {"time": 0.1667, "value": 4.91, "curve": [0.5, 4.91, 0.833, -11.69]}, {"time": 1.1667, "value": -11.69, "curve": [1.446, -11.69, 1.724, -0.19]}, {"time": 2, "value": 3.6}]}, "fly-L5": {"rotate": [{"value": -8.14, "curve": [0.113, -10.21, 0.223, -11.84]}, {"time": 0.3333, "value": -11.84, "curve": [0.667, -11.84, 1, 2.32]}, {"time": 1.3333, "value": 2.32, "curve": [1.557, 2.32, 1.78, -3.95]}, {"time": 2, "value": -8.14}]}, "fly-L6": {"rotate": [{"value": 1.45, "curve": [0.169, -2.56, 0.334, -6.63]}, {"time": 0.5, "value": -6.63, "curve": [0.833, -6.63, 1.167, 9.54]}, {"time": 1.5, "value": 9.54, "curve": [1.668, 9.54, 1.836, 5.52]}, {"time": 2, "value": 1.45}]}, "toushi": {"translate": [{"y": -3.95, "curve": [0.136, 0, 0.268, 0, 0.136, -1.82, 0.268, 0]}, {"time": 0.4, "curve": [0.733, 0, 1.067, 0, 0.733, 0, 1.067, -11.22]}, {"time": 1.4, "y": -11.22, "curve": [1.601, 0, 1.802, 0, 1.601, -11.22, 1.802, -7.19]}, {"time": 2, "y": -3.95}]}, "soldier-jian": {"translate": [{"x": -0.48, "y": -2.25, "curve": [0.09, -0.2, 0.152, 0, 0.09, -0.94, 0.152, 0]}, {"time": 0.1667, "curve": [0.5, 0, 0.833, -2.54, 0.5, 0, 0.833, -11.84]}, {"time": 1.1667, "x": -2.54, "y": -11.84, "curve": [1.368, -2.54, 1.773, -1.22, 1.368, -11.84, 1.773, -5.68]}, {"time": 2, "x": -0.48, "y": -2.25}]}, "soldier-dunpai": {"translate": [{"x": 0.5, "y": -0.76, "curve": [0.058, 0.22, 0.112, 0, 0.058, -0.33, 0.112, 0]}, {"time": 0.1667, "curve": [0.5, 0, 0.833, 6.4, 0.5, 0, 0.833, -9.61]}, {"time": 1.1667, "x": 6.4, "y": -9.61, "curve": [1.446, 6.4, 1.724, 1.97, 1.446, -9.61, 1.724, -2.95]}, {"time": 2, "x": 0.5, "y": -0.76}]}, "trail-1": {"rotate": [{"value": 6.79, "curve": [0.058, 7.48, 0.112, 8]}, {"time": 0.1667, "value": 8, "curve": [0.5, 8, 0.833, -7.35]}, {"time": 1.1667, "value": -7.35, "curve": [1.446, -7.35, 1.724, 3.28]}, {"time": 2, "value": 6.79}]}, "trail-2": {"rotate": [{"value": 3.81, "curve": [0.113, 6.15, 0.223, 8]}, {"time": 0.3333, "value": 8, "curve": [0.667, 8, 1, -8.04]}, {"time": 1.3333, "value": -8.04, "curve": [1.557, -8.04, 1.78, -0.95]}, {"time": 2, "value": 3.81}]}, "trail-3": {"rotate": [{"value": -5.04, "curve": [0.169, -3.9, 0.334, -2.73]}, {"time": 0.5, "value": -2.73, "curve": [0.833, -2.73, 1.167, -7.35]}, {"time": 1.5, "value": -7.35, "curve": [1.668, -7.35, 1.836, -6.2]}, {"time": 2, "value": -5.04}]}, "trail-4": {"rotate": [{"value": -8.39, "curve": [0.224, 5.9, 0.446, 27.23]}, {"time": 0.6667, "value": 27.23, "curve": [1, 27.23, 1.333, -20.99]}, {"time": 1.6667, "value": -20.99, "curve": [1.779, -20.99, 1.891, -15.64]}, {"time": 2, "value": -8.39}]}, "trail-5": {"rotate": [{"value": -4.61, "curve": [0.28, 3.56, 0.557, 27.5]}, {"time": 0.8333, "value": 27.5, "curve": [1.167, 27.5, 1.5, -7.35]}, {"time": 1.8333, "value": -7.35, "curve": [1.89, -7.35, 1.947, -6.28]}, {"time": 2, "value": -4.61}]}, "trail-6": {"rotate": [{"value": -7.35, "curve": [0.333, -7.35, 0.667, 41.23]}, {"time": 1, "value": 41.23, "curve": [1.333, 41.23, 1.667, -7.35]}, {"time": 2, "value": -7.35}]}, "trail-7": {"rotate": [{"value": 3.63, "curve": [0.113, 4.35, 0.223, 4.92]}, {"time": 0.3333, "value": 4.92, "curve": [0.667, 4.92, 1, 0]}, {"time": 1.3333, "curve": [1.557, 0, 1.78, 2.18]}, {"time": 2, "value": 3.63}]}, "soldier": {"translate": [{"y": -6.29, "curve": [0.224, 0, 0.446, 0, 0.224, -3.77, 0.446, 0]}, {"time": 0.6667, "curve": [1, 0, 1.333, 0, 1, 0, 1.333, -8.52]}, {"time": 1.6667, "y": -8.52, "curve": [1.779, 0, 1.891, 0, 1.779, -8.52, 1.891, -7.57]}, {"time": 2, "y": -6.29}]}, "leg-L": {"rotate": [{"value": 2.71, "curve": [0.224, 1.62, 0.446, 0]}, {"time": 0.6667, "curve": [1, 0, 1.333, 3.67]}, {"time": 1.6667, "value": 3.67, "curve": [1.779, 3.67, 1.891, 3.26]}, {"time": 2, "value": 2.71}]}, "leg-R": {"rotate": [{"value": -2.29, "curve": [0.224, -1.37, 0.446, 0]}, {"time": 0.6667, "curve": [1, 0, 1.333, -3.1]}, {"time": 1.6667, "value": -3.1, "curve": [1.779, -3.1, 1.891, -2.75]}, {"time": 2, "value": -2.29}]}, "face": {"translate": [{"y": -6.76, "curve": [0.169, 0, 0.334, 0, 0.169, -3.4, 0.334, 0]}, {"time": 0.5, "curve": [0.833, 0, 1.167, 0, 0.833, 0, 1.167, -13.51]}, {"time": 1.5, "y": -13.51, "curve": [1.668, 0, 1.836, 0, 1.668, -13.51, 1.836, -10.16]}, {"time": 2, "y": -6.76}]}}}}}