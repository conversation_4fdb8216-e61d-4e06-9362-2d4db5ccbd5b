{"skeleton": {"hash": "Vjf7Pfhc8RI", "spine": "4.1.11", "x": -71.37, "y": -0.06, "width": 142, "height": 175.05}, "bones": [{"name": "root"}, {"name": "body", "parent": "root", "length": 54.07, "rotation": 90, "x": -0.21, "y": 47.23}, {"name": "head", "parent": "body", "length": 76.78, "rotation": 0.67, "x": 42.42}, {"name": "hand", "parent": "body", "length": 52.4, "rotation": -151.39, "x": 54.67, "y": -23.3}, {"name": "hand2", "parent": "hand", "length": 15.84, "rotation": -30.77, "x": 52.4}, {"name": "foot", "parent": "body", "length": 44.61, "rotation": -176.16, "x": -0.6, "y": -15.83}, {"name": "hand3", "parent": "body", "length": 52.4, "rotation": -26.53, "x": 54.67, "y": 24.79, "scaleX": -1}, {"name": "hand4", "parent": "hand3", "length": 15.84, "rotation": -30.77, "x": 52.4}, {"name": "foot2", "parent": "body", "length": 44.61, "rotation": -1.47, "x": -1.26, "y": 17.33, "scaleX": -1}, {"name": "eye", "parent": "head", "x": 14.33, "y": 0.49}, {"name": "bone", "parent": "root", "length": 14.04, "rotation": -94.51, "x": -13.16, "y": 39.66}, {"name": "bone2", "parent": "bone", "length": 10.42, "rotation": -3.62, "x": 14.04}], "slots": [{"name": "foot", "bone": "foot", "attachment": "foot"}, {"name": "foot2", "bone": "foot2", "attachment": "foot"}, {"name": "body", "bone": "body", "attachment": "body"}, {"name": "circle", "bone": "body", "attachment": "circle"}, {"name": "hand", "bone": "hand", "attachment": "hand"}, {"name": "hand2", "bone": "hand3", "attachment": "hand"}, {"name": "head", "bone": "head", "attachment": "head"}, {"name": "eye", "bone": "eye", "attachment": "eye"}], "skins": [{"name": "poolparty", "attachments": {"body": {"body": {"name": "body2", "type": "mesh", "uvs": [0.98344, 0.60838, 0.99238, 0.74715, 0.62332, 0.87795, 0.27896, 1, 0.24139, 1, 0.00905, 0.81772, 0.01447, 0.60031, 0.02917, 0.01012, 0.94494, 0.01076, 0.28306, 0.67709, 0.60156, 0.69083, 0.34016, 0.67956, 0.30383, 0.85839], "triangles": [5, 9, 12, 3, 12, 2, 3, 4, 12, 4, 5, 12, 5, 6, 9, 12, 11, 10, 11, 9, 7, 12, 10, 2, 2, 10, 1, 10, 0, 1, 0, 10, 8, 8, 10, 11, 8, 11, 7, 6, 7, 9, 12, 9, 11], "vertices": [1, 1, 0.78, -35.63, 1, 2, 1, -11.98, -36.29, 0.97149, 10, 0.52, 49.42, 0.02851, 3, 1, -24.02, -9.34, 0.54514, 10, 14.64, 23.51, 0.3813, 11, -0.88, 23.5, 0.07356, 3, 1, -35.25, 15.79, 0.0005, 10, 27.81, -0.66, 0.00329, 11, 13.79, 0.21, 0.99621, 2, 10, 28.03, -3.4, 0.01724, 11, 14.17, -2.51, 0.98276, 2, 10, 12.65, -21.62, 0.80274, 11, -0.03, -21.67, 0.19726, 1, 1, 1.53, 35.1, 1, 1, 1, 55.82, 34.03, 1, 1, 1, 55.76, -32.82, 1, 1, 1, -5.54, 15.49, 1, 1, 1, -6.8, -7.76, 1, 1, 1, -5.76, 11.33, 1, 3, 1, -22.22, 13.98, 0.0002, 10, 14.68, 0.12, 0.19416, 11, 0.63, 0.16, 0.80563], "hull": 9}}, "circle": {"circle": {"x": 11.76, "y": -0.21, "rotation": -90, "width": 125, "height": 84}}, "foot": {"foot": {"name": "foot2", "x": 19.24, "y": 0.09, "rotation": 86.16, "width": 42, "height": 55}}, "foot2": {"foot": {"name": "foot2", "x": 18.51, "y": -1.02, "rotation": -271.47, "width": 42, "height": 55}}, "hand": {"hand": {"name": "hand2", "x": 36.18, "y": -0.32, "rotation": 61.39, "width": 45, "height": 74}}, "hand2": {"hand": {"name": "hand2", "x": 36.18, "y": -0.32, "rotation": 61.39, "width": 45, "height": 74}}, "head": {"head": {"name": "head2", "x": 38.33, "y": -0.29, "rotation": -90.67, "width": 142, "height": 94}}}}, {"name": "yellow", "attachments": {"body": {"body": {"type": "mesh", "uvs": [0.98344, 0.60838, 0.99238, 0.74715, 0.62332, 0.87795, 0.27896, 1, 0.24139, 1, 0.00905, 0.81772, 0.01447, 0.60031, 0.02917, 0.01012, 0.94494, 0.01076, 0.28306, 0.67709, 0.60156, 0.69083, 0.34016, 0.67956, 0.30383, 0.85839], "triangles": [5, 9, 12, 3, 12, 2, 3, 4, 12, 4, 5, 12, 5, 6, 9, 12, 11, 10, 11, 9, 7, 12, 10, 2, 2, 10, 1, 10, 0, 1, 0, 10, 8, 8, 10, 11, 8, 11, 7, 6, 7, 9, 12, 9, 11], "vertices": [1, 1, 0.78, -35.63, 1, 2, 1, -11.98, -36.29, 0.97149, 10, 0.52, 49.42, 0.02851, 3, 1, -24.02, -9.34, 0.54514, 10, 14.64, 23.51, 0.3813, 11, -0.88, 23.5, 0.07356, 3, 1, -35.25, 15.79, 0.0005, 10, 27.81, -0.66, 0.00329, 11, 13.79, 0.21, 0.99621, 2, 10, 28.03, -3.4, 0.01724, 11, 14.17, -2.51, 0.98276, 2, 10, 12.65, -21.62, 0.80274, 11, -0.03, -21.67, 0.19726, 1, 1, 1.53, 35.1, 1, 1, 1, 55.82, 34.03, 1, 1, 1, 55.76, -32.82, 1, 1, 1, -5.54, 15.49, 1, 1, 1, -6.8, -7.76, 1, 1, 1, -5.76, 11.33, 1, 3, 1, -22.22, 13.98, 0.0002, 10, 14.68, 0.12, 0.19416, 11, 0.63, 0.16, 0.80563], "hull": 9}}, "eye": {"eye": {"x": 0.51, "rotation": -90.67, "width": 29, "height": 21}}, "foot": {"foot": {"x": 22.7, "y": 15.02, "rotation": 86.16, "width": 55, "height": 52}}, "foot2": {"foot": {"x": 22.7, "y": 15.02, "rotation": 86.16, "width": 55, "height": 52}}, "hand": {"hand": {"type": "mesh", "uvs": [0.68946, 0.27582, 0.98225, 0.52648, 0.97988, 0.6895, 0.97554, 0.98766, 0.43072, 0.98731, 0.29909, 0.71852, 0.13889, 0.39138, 0, 0.10778, 0.10532, 0, 0.43869, 0.06113, 0.67688, 0.74183, 0.68353, 0.85104], "triangles": [4, 11, 3, 3, 11, 2, 11, 5, 10, 11, 4, 5, 11, 10, 2, 2, 10, 1, 0, 1, 10, 5, 6, 0, 0, 10, 5, 6, 7, 9, 6, 9, 0, 9, 7, 8], "vertices": [1, 3, 25.7, 15.11, 1, 2, 3, 48.29, 17.79, 0.81034, 4, -12.64, 13.18, 0.18966, 2, 3, 58.83, 11.92, 0.22486, 4, -0.58, 13.53, 0.77514, 1, 4, 21.48, 14.17, 1, 2, 3, 66.34, -20.33, 0.00938, 4, 22.38, -10.33, 0.99062, 2, 3, 46.04, -16, 0.61615, 4, 2.72, -17, 0.38385, 1, 3, 21.34, -10.74, 1, 1, 3, -0.08, -6.18, 1, 1, 3, -4.81, 1.8, 1, 1, 3, 6.35, 12.81, 1, 1, 4, 3.8, 0.05, 1, 1, 4, 11.87, 0.65, 1], "hull": 10}}, "hand2": {"hand": {"type": "mesh", "uvs": [0.58121, 0.21735, 0.96028, 0.44968, 0.97557, 0.65606, 1, 0.98582, 0.43044, 0.98951, 0.30648, 0.73566, 0.16343, 0.44274, 0, 0.10806, 0, 0.01325, 0.24304, 0.01009, 0.68578, 0.76578, 0.51143, 0.44093], "triangles": [4, 10, 3, 4, 5, 10, 10, 2, 3, 5, 11, 10, 2, 10, 11, 2, 11, 1, 5, 6, 11, 11, 0, 1, 11, 6, 0, 0, 6, 7, 0, 7, 9, 7, 8, 9], "vertices": [1, 6, 19.56, 12.9, 1, 2, 6, 42.83, 19.64, 0.97649, 7, -18.28, 11.98, 0.02351, 2, 6, 56.56, 12.93, 0.46279, 7, -3.04, 13.24, 0.53721, 1, 7, 21.3, 15.26, 1, 2, 6, 66.48, -20.42, 0, 7, 22.54, -10.34, 1, 2, 6, 47.32, -16.32, 0.45232, 7, 3.98, -16.62, 0.54768, 2, 6, 25.2, -11.59, 0.99877, 7, -17.44, -23.87, 0.00123, 1, 6, -0.06, -6.19, 1, 1, 6, -6.22, -2.83, 1, 1, 6, -1.19, 6.89, 1, 1, 7, 5.56, 0.52, 1, 1, 6, 32.59, 2.22, 1], "hull": 10}}, "head": {"head": {"x": 38.33, "y": -0.29, "rotation": -90.67, "width": 142, "height": 94}}}}], "animations": {"animation": {"bones": {"body": {"rotate": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}], "translate": [{"curve": [0.078, 0, 0.156, 0, 0.078, 0, 0.156, 1.19]}, {"time": 0.2333, "y": 1.19, "curve": [0.322, 0, 0.411, 0, 0.322, 1.19, 0.411, 0]}, {"time": 0.5, "curve": [0.6, 0, 0.7, 0, 0.6, 0, 0.7, 1.19]}, {"time": 0.8, "y": 1.19, "curve": [0.867, 0, 0.933, 0, 0.867, 1.19, 0.933, 0]}, {"time": 1}], "scale": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}], "shear": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}]}, "foot": {"rotate": [{"value": -2.19, "curve": [0.167, -2.19, 0.333, -2.73]}, {"time": 0.5, "value": -2.73, "curve": [0.667, -2.73, 0.833, -2.19]}, {"time": 1, "value": -2.19}], "translate": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}], "scale": [{"x": 1.171, "y": 1.039, "curve": [0.167, 1.171, 0.333, 0.94, 0.167, 1.039, 0.333, 0.979]}, {"time": 0.5, "x": 0.94, "y": 0.979, "curve": [0.667, 0.94, 0.833, 1.171, 0.667, 0.979, 0.833, 1.039]}, {"time": 1, "x": 1.171, "y": 1.039}], "shear": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}]}, "foot2": {"rotate": [{"value": 1.61, "curve": [0.167, 1.61, 0.333, 1.77]}, {"time": 0.5, "value": 1.77, "curve": [0.667, 1.77, 0.833, 1.61]}, {"time": 1, "value": 1.61}], "translate": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}], "scale": [{"x": 0.924, "y": 0.968, "curve": [0.167, 0.924, 0.333, 1.153, 0.167, 0.968, 0.333, 1.018]}, {"time": 0.5, "x": 1.153, "y": 1.018, "curve": [0.667, 1.153, 0.833, 0.924, 0.667, 1.018, 0.833, 0.968]}, {"time": 1, "x": 0.924, "y": 0.968}], "shear": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}]}, "hand": {"rotate": [{"value": 1.52, "curve": [0.167, 1.52, 0.333, -2.09]}, {"time": 0.5, "value": -2.09, "curve": [0.667, -2.09, 0.833, 1.52]}, {"time": 1, "value": 1.52}], "translate": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}], "scale": [{"x": 0.964, "y": 0.963, "curve": [0.167, 0.964, 0.333, 1.025, 0.167, 0.963, 0.333, 1.05]}, {"time": 0.5, "x": 1.025, "y": 1.05, "curve": [0.667, 1.025, 0.833, 0.964, 0.667, 1.05, 0.833, 0.963]}, {"time": 1, "x": 0.964, "y": 0.963}], "shear": [{"x": 4.74, "curve": [0.167, 4.74, 0.333, -2.9, 0.167, 0, 0.333, 0]}, {"time": 0.5, "x": -2.9}, {"time": 1, "x": 4.74}]}, "hand2": {"rotate": [{"value": 1.14, "curve": [0.034, 1.36, 0.067, 1.52]}, {"time": 0.1, "value": 1.52, "curve": [0.267, 1.52, 0.433, -2.09]}, {"time": 0.6, "value": -2.09, "curve": [0.734, -2.09, 0.868, 0.22]}, {"time": 1, "value": 1.14}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 1}], "scale": [{"x": 0.97, "y": 0.972, "curve": [0.034, 0.966, 0.067, 0.964, 0.034, 0.967, 0.067, 0.963]}, {"time": 0.1, "x": 0.964, "y": 0.963, "curve": [0.267, 0.964, 0.433, 1.025, 0.267, 0.963, 0.433, 1.05]}, {"time": 0.6, "x": 1.025, "y": 1.05, "curve": [0.734, 1.025, 0.868, 0.986, 0.734, 1.05, 0.868, 0.994]}, {"time": 1, "x": 0.97, "y": 0.972}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 1}]}, "hand3": {"rotate": [{"value": 2.07, "curve": [0.167, 2.07, 0.333, -1.78]}, {"time": 0.5, "value": -1.78, "curve": [0.667, -1.78, 0.833, 2.07]}, {"time": 1, "value": 2.07}], "translate": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}], "scale": [{"x": 1.008, "y": 1.025, "curve": [0.167, 1.008, 0.333, 0.956, 0.167, 1.025, 0.333, 0.94]}, {"time": 0.5, "x": 0.956, "y": 0.94, "curve": [0.667, 0.956, 0.833, 1.008, 0.667, 0.94, 0.833, 1.025]}, {"time": 1, "x": 1.008, "y": 1.025}], "shear": [{"x": 3.68}, {"time": 0.5, "x": -5.09, "curve": [0.667, -5.09, 0.833, 3.68, 0.667, 0, 0.833, 0]}, {"time": 1, "x": 3.68}]}, "hand4": {"rotate": [{"value": -0.16, "curve": [0.034, -0.25, 0.067, -0.32]}, {"time": 0.1, "value": -0.32, "curve": [0.267, -0.32, 0.433, 1.18]}, {"time": 0.6, "value": 1.18, "curve": [0.734, 1.18, 0.868, 0.22]}, {"time": 1, "value": -0.16}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 1}], "scale": [{"x": 1.003, "y": 1.016, "curve": [0.034, 1.006, 0.067, 1.008, 0.034, 1.022, 0.067, 1.025]}, {"time": 0.1, "x": 1.008, "y": 1.025, "curve": [0.267, 1.008, 0.433, 0.956, 0.267, 1.025, 0.433, 0.94]}, {"time": 0.6, "x": 0.956, "y": 0.94, "curve": [0.734, 0.956, 0.868, 0.99, 0.734, 0.94, 0.868, 0.995]}, {"time": 1, "x": 1.003, "y": 1.016}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 1}]}, "head": {"rotate": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}], "translate": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}], "scale": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}], "shear": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}]}, "eye": {"rotate": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}], "translate": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}], "scale": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}], "shear": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}]}, "bone": {"rotate": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}], "translate": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}], "scale": [{"curve": [0.078, 1, 0.156, 0.887, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.887, "curve": [0.322, 0.887, 0.411, 1, 0.322, 1, 0.411, 1]}, {"time": 0.5, "curve": [0.6, 1, 0.7, 0.887, 0.6, 1, 0.7, 1]}, {"time": 0.8, "x": 0.887, "curve": [0.867, 0.887, 0.933, 1, 0.867, 1, 0.933, 1]}, {"time": 1}], "shear": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1}]}, "bone2": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 1}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 1}], "scale": [{"x": 0.943, "curve": [0.033, 0.972, 0.067, 1, 0.033, 1, 0.067, 1]}, {"time": 0.1, "curve": [0.178, 1, 0.256, 0.887, 0.178, 1, 0.256, 1]}, {"time": 0.3333, "x": 0.887, "curve": [0.422, 0.887, 0.511, 1, 0.422, 1, 0.511, 1]}, {"time": 0.6, "curve": [0.7, 1, 0.8, 0.887, 0.7, 1, 0.8, 1]}, {"time": 0.9, "x": 0.887, "curve": [0.933, 0.887, 0.967, 0.915, 0.933, 1, 0.967, 1]}, {"time": 1, "x": 0.943}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 1}]}}}}}